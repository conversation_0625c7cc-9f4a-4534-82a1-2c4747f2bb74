# Google OAuth Implementation - Frontend Integration Guide

## Overview
Backend sudah dikonfigurasi untuk menangani Google OAuth dengan logika sebagai berikut:
- **User sudah terdaftar**: Langsung redirect ke `/dashboard` dengan `isNewUser=false`
- **User belum terdaftar**: Otomatis buat user baru dan redirect ke `/dashboard` dengan `isNewUser=true`

## Backend Configuration
✅ Google OAuth environment variables sudah dikonfigurasi di `wrangler.toml`:
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`  
- `GOOGLE_REDIRECT_URI="http://localhost:3000/api/portal/auth/google/callback"`

## Frontend Implementation

### 1. Google Login Button
```html
<a href="http://localhost:3000/api/portal/auth/google" class="google-login-btn">
  Login with Google
</a>
```

### 2. Dashboard Page - Handling OAuth Callback
Setelah Google OAuth berhasil, user akan di-redirect ke:
```
http://localhost:3001/dashboard?token=JWT_TOKEN&isNewUser=BOOLEAN&email=USER_EMAIL&displayName=DISPLAY_NAME
```

### 3. Parameter yang Diterima Frontend

| Parameter | Type | Description |
|-----------|------|-------------|
| `token` | string | JWT token untuk authentication |
| `isNewUser` | boolean | `true` jika user baru signup, `false` jika user existing |
| `email` | string | Email address user |
| `displayName` | string | Display name dari Google profile |

### 4. Contoh JavaScript untuk Dashboard Page
```javascript
// Parse URL parameters
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');
const isNewUser = urlParams.get('isNewUser') === 'true';
const email = urlParams.get('email');
const displayName = urlParams.get('displayName');

if (token) {
    // Save token to localStorage/sessionStorage
    localStorage.setItem('authToken', token);
    
    // Clean URL parameters
    window.history.replaceState({}, document.title, window.location.pathname);
    
    if (isNewUser) {
        // Show welcome message for new user
        showWelcomeMessage(`Welcome to our platform, ${displayName}!`);
        
        // Optional: Show onboarding flow
        showOnboardingFlow();
    } else {
        // Show returning user message
        showMessage(`Welcome back, ${displayName}!`);
    }
    
    // Load dashboard data
    loadDashboardData();
} else {
    // Redirect to login page if no token
    window.location.href = '/login';
}

function showWelcomeMessage(message) {
    // Your implementation for showing welcome message
    console.log(message);
}

function showMessage(message) {
    // Your implementation for showing message
    console.log(message);
}

function loadDashboardData() {
    // Load dashboard with the JWT token
    const token = localStorage.getItem('authToken');
    
    fetch('/api/dashboard', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        // Handle dashboard data
        console.log('Dashboard data:', data);
    });
}
```

### 5. Testing URLs

#### Existing User (<EMAIL>):
```
http://localhost:3001/dashboard?token=jwt_token_here&isNewUser=false&email=santanalawalata7%40gmail.com&displayName=Santana%20Lawalata
```

#### New User:
```
http://localhost:3001/dashboard?token=jwt_token_here&isNewUser=true&email=newuser%40gmail.com&displayName=New%20User
```

## User Data Structure

User yang tersimpan di backend memiliki struktur:
```json
{
  "id": "portal_1752513979553_hn8y2verp",
  "email": "<EMAIL>",
  "type": "portal",
  "isActive": 1,
  "googleId": "106975055236887718238",
  "profile": {
    "username": "santanalawalata7",
    "displayName": "Santana Lawalata"
  },
  "createdAt": "2025-07-14T17:26:19.553Z",
  "lastLogin": null
}
```

## Error Handling

Jika terjadi error dalam Google OAuth flow, user akan di-redirect ke URL error atau menerima response error dari backend. Frontend harus menangani kemungkinan ini.

## Next Steps for Frontend Developer

1. Implementasikan button "Login with Google" yang mengarah ke `/api/portal/auth/google`
2. Buat handler di dashboard page untuk membaca URL parameters
3. Simpan JWT token dan lakukan request authenticated ke backend APIs
4. Implementasikan logic berbeda untuk new user vs existing user
5. Test dengan kedua skenario (user baru dan user existing)
