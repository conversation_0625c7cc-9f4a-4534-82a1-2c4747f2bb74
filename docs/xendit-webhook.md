# Xendit Webhook Integration

This document explains how to set up and use the Xendit payment webhook integration.

## Webhook Endpoints

The application provides two webhook endpoints for Xendit:

1. `/api/xendit/webhook` - General purpose webhook endpoint
2. `/api/xendit/payment-webhook` - Dedicated endpoint for payment notifications

Both endpoints handle the same payload and perform the same operations, but having a dedicated payment webhook endpoint makes it easier to configure in the Xendit dashboard.

## Webhook Configuration in Xendit Dashboard

1. Log in to your Xendit Dashboard
2. Go to Settings > Webhooks
3. Add a new webhook with the following details:
   - URL: `https://your-domain.com/api/xendit/payment-webhook`
   - Events: Select "Payment Status Change" or equivalent
   - Authentication: Set a callback token (see below)

## Webhook Security

The webhook endpoint verifies the authenticity of incoming requests using the `x-callback-token` header. You must set the `XENDIT_WEBHOOK_TOKEN` environment variable in your application to match the callback token configured in the Xendit dashboard.

## Expected Payload

The webhook expects a payload similar to the following:

```json
{
  "id": "67d53ae210b21173ca67d492",
  "amount": 150000,
  "status": "PAID",
  "created": "2025-03-15T08:31:31.294Z",
  "is_high": false,
  "paid_at": "2025-03-15T08:31:34.521Z",
  "updated": "2025-03-15T08:31:36.412Z",
  "user_id": "679e3e04efb310294787b54c",
  "currency": "IDR",
  "payment_id": "qrpy_a396a9a7-9ec4-4c01-b88c-ffaab52b4dc8",
  "description": "Upgrade to Basic Plan",
  "external_id": "invoice-1742027490163",
  "paid_amount": 150000,
  "payer_email": "<EMAIL>",
  "merchant_name": "Your Merchant Name",
  "payment_method": "QR_CODE",
  "payment_channel": "QRIS",
  "payment_details": {
    "source": "DANA",
    "receipt_id": ""
  },
  "payment_method_id": "pm-1c5cf38a-eb94-478d-b124-50747302ffc4"
}
```

## Webhook Processing

When a payment notification is received with `status: "PAID"`, the webhook will:

1. Verify the callback token
2. Look up subscription data using the `external_id`
3. Upgrade the user's tier based on the subscription data or payment amount
4. Store the payment record
5. Clean up temporary reference data

## Tier Determination

If subscription data is found, the tier specified in that data will be used. Otherwise, the tier will be determined based on:

1. The payment description (e.g., "Upgrade to Basic Plan")
2. The payment amount:
   - Amount >= 500,000 IDR: High tier
   - Amount >= 150,000 IDR: Medium tier
   - Lower amounts: Free tier

## Environment Variables

Make sure to set the following environment variables:

- `XENDIT_WEBHOOK_TOKEN`: The callback token for webhook authentication
- `XENDIT_API_KEY`: Your Xendit API key for making API calls

## Testing the Webhook

You can test the webhook by sending a POST request to the endpoint with a sample payload. Make sure to include the `x-callback-token` header with the correct token value.

Example using curl:

```bash
curl -X POST https://your-domain.com/api/xendit/payment-webhook \
  -H "Content-Type: application/json" \
  -H "x-callback-token: your-webhook-token" \
  -d '{
    "id": "test-payment-id",
    "amount": 150000,
    "status": "PAID",
    "created": "2025-03-15T08:31:31.294Z",
    "paid_at": "2025-03-15T08:31:34.521Z",
    "description": "Upgrade to Basic Plan",
    "external_id": "test-invoice-id",
    "payer_email": "<EMAIL>",
    "payment_method": "QR_CODE",
    "payment_channel": "QRIS"
  }'
```
