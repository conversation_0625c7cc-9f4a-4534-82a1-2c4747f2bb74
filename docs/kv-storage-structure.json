{"dataModel": {"email": {"pattern": "email:{email}", "example": "email:<EMAIL>", "schema": {"id": "user-uuid", "email": "<EMAIL>", "password": "hashed_password", "created_at": "2024-01-20T00:00:00.000Z", "updated_at": "2024-01-20T00:00:00.000Z", "domains": [{"domain": "domain1.com", "api_key": "api-key-uuid-1", "status": "active", "created_at": "2024-01-20T00:00:00.000Z", "activated_at": "2024-01-20T00:00:00.000Z"}]}}, "api_key": {"pattern": "api_key:{api<PERSON>ey}", "example": "api_key:api-key-uuid-1", "schema": {"id": "user-uuid", "userId": "user-uuid", "email": "<EMAIL>", "domain": "domain1.com", "api_key": "api-key-uuid-1", "status": "active", "created_at": "2024-01-20T00:00:00.000Z", "activated_at": "2024-01-20T00:00:00.000Z", "tier": "medium", "quota": {"resetDate": "2024-02-20T00:00:00.000Z", "usage": {"images": 0, "content": 0, "title": 0}, "limits": {"images": 20000, "content": 20000, "title": 20000}}}}, "domain": {"pattern": "domain:{domain}", "example": "domain:domain1.com", "schema": {"id": "user-uuid", "userId": "user-uuid", "email": "<EMAIL>", "domain": "domain1.com", "api_key": "api-key-uuid-1", "status": "active", "created_at": "2024-01-20T00:00:00.000Z", "activated_at": "2024-01-20T00:00:00.000Z", "tier": "medium", "quota": {"resetDate": "2024-02-20T00:00:00.000Z", "usage": {"images": 0, "content": 0, "title": 0}, "limits": {"images": 20000, "content": 20000, "title": 20000}}}}, "subscription": {"pattern": "subscription:{subscriptionId}", "example": "subscription:I-XXXXX", "schema": {"subscriptionId": "I-XXXXX", "planId": "P-XXXXX", "productId": "PROD-XXXXX", "userId": "user-uuid", "tier": "medium", "basePrice": 9.99, "addons": {"addon1": false, "addon2": false}, "totalPrice": 9.99, "status": "ACTIVE", "created_at": "2024-01-20T00:00:00.000Z", "activated_at": "2024-01-20T00:00:00.000Z", "updated_at": "2024-01-20T00:00:00.000Z", "nextBillingTime": "2024-02-20T00:00:00.000Z"}}, "payment": {"pattern": "payment:{paymentId}", "example": "payment:PAY-XXXXX", "schema": {"id": "PAY-XXXXX", "amount": {"currency_code": "USD", "value": "9.99"}, "time": "2024-01-20T00:00:00.000Z", "recordedAt": "2024-01-20T00:00:00.000Z"}}, "webhook_event": {"pattern": "webhook_event:{eventId}", "example": "webhook_event:WH-XXXXX", "schema": {"id": "WH-XXXXX", "event_type": "BILLING.SUBSCRIPTION.ACTIVATED", "event_version": "1.0", "create_time": "2024-01-20T00:00:00.000Z", "resource_type": "subscription", "resource_version": "2.0", "summary": "Subscription activated", "resource": {"subscription_id": "I-XXXXX", "status": "ACTIVE", "status_update_time": "2024-01-20T00:00:00.000Z"}, "receivedAt": "2024-01-20T00:00:00.000Z"}}, "settings": {"pattern": "settings:tiers", "schema": {"config": {"free": {"name": "Free Tier", "maxQuota": 1000, "imagesQuota": 1000, "contentQuota": 1000, "titleQuota": 1000, "price": 0, "features": ["Basic API access", "Community support"]}, "medium": {"name": "Medium Tier", "maxQuota": 40000, "imagesQuota": 20000, "contentQuota": 20000, "titleQuota": 20000, "price": 9.99, "addon1_price": 19.99, "addon2_price": 39.99, "features": ["Increased quota", "Email support"]}, "high": {"name": "High Tier", "maxQuota": 1000000, "imagesQuota": 1000000, "contentQuota": 1000000, "titleQuota": 1000000, "price": 49.99, "features": ["Maximum quota", "Priority support", "24/7 phone support"]}}, "updated_at": "2024-01-20T00:00:00.000Z", "version": "1.0"}}, "email_queue": {"pattern": "email_queue:{id}", "example": "email_queue:queue-uuid", "schema": {"id": "queue-uuid", "user_id": "user-uuid", "type": "welcome", "status": "pending", "data": {"to": "<EMAIL>", "subject": "Welcome", "template": "welcome"}, "attempts": 0, "queued_at": "2024-01-20T00:00:00.000Z", "process_after": "2024-01-20T00:00:00.000Z", "sent_at": null}}}}