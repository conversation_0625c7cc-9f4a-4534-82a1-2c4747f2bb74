#!/bin/bash

# setup-d1.sh
# Script to set up D1 database for the project

echo "🚀 Setting up D1 database..."

# Create D1 database
echo "📦 Creating D1 database..."
wrangler d1 create supersense-db

echo "⚠️  Please update wrangler.toml with the database_id from the output above"
echo "   Replace 'your-database-id-here' with the actual database ID"

# Apply schema
echo "📋 Applying database schema..."
wrangler d1 execute supersense-db --file=./schema.sql

echo "✅ D1 database setup completed!"
echo ""
echo "Next steps:"
echo "1. Update wrangler.toml with the correct database_id"
echo "2. Run migration: wrangler dev --test-scheduled (then call migration endpoint)"
echo "3. Test the application with D1"
echo "4. Remove KV configuration from wrangler.toml when migration is complete"