#!/bin/bash
# CURL commands untuk testing payment endpoints
# Ganti BASE_URL dengan URL Cloudflare Workers Anda

BASE_URL="http://localhost:8787"

echo "🚀 Testing Payment Endpoints dengan CURL..."

echo ""
echo "1️⃣ Testing PayPal Subscription..."
curl -X POST "$BASE_URL/api/paypal/subscriptions" \
  -H "Content-Type: application/json" \
  -d '{
    "tier": "pro",
    "email": "<EMAIL>",
    "addons": {
      "addon1": false,
      "addon2": true
    }
  }' | jq '.'

echo ""
echo "2️⃣ Testing PayPal Purchase..."
curl -X POST "$BASE_URL/api/paypal/purchases" \
  -H "Content-Type: application/json" \
  -d '{
    "tier": "pro",
    "email": "<EMAIL>",
    "amount": 99.99,
    "addons": {
      "addon1": false,
      "addon2": true
    }
  }' | jq '.'

echo ""
echo "3️⃣ Testing Xendit Subscription..."
curl -X POST "$BASE_URL/api/xendit/subscriptions" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "<EMAIL>",
    "tier": "basic",
    "addons": {
      "addon1": true,
      "addon2": false
    }
  }' | jq '.'

echo ""
echo "4️⃣ Testing Xendit Invoice..."
curl -X POST "$BASE_URL/api/xendit/invoice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-test-token-here" \
  -d '{
    "external_id": "inv-'$(date +%s)'",
    "amount": 100000,
    "payer_email": "<EMAIL>",
    "description": "Test payment for services",
    "payment_methods": ["QRIS", "BANK_TRANSFER"],
    "should_send_email": true
  }' | jq '.'

echo ""
echo "5️⃣ Testing Xendit QRIS..."
curl -X POST "$BASE_URL/api/xendit/qris" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "qris-'$(date +%s)'",
    "amount": 50000,
    "description": "Test QRIS payment"
  }' | jq '.'

echo ""
echo "6️⃣ Testing PayPal Webhook Simulation..."
curl -X POST "$BASE_URL/api/paypal/webhooks" \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "BILLING.SUBSCRIPTION.ACTIVATED",
    "resource": {
      "id": "test-subscription-id",
      "status": "ACTIVE"
    }
  }' | jq '.'

echo ""
echo "7️⃣ Testing Xendit Webhook Simulation..."
curl -X POST "$BASE_URL/api/xendit/webhooks" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "test-ref-123",
    "status": "PAID",
    "amount": 100000
  }' | jq '.'

echo ""
echo "✅ All CURL tests completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Check the approval/invoice URLs in the responses above"
echo "2. Test the payment flows by visiting the URLs"
echo "3. Monitor webhook endpoints for payment confirmations"
echo "4. Check your KV storage for updated user tiers"