// Temporary route to migrate tier data
import { migrateTierData } from './tmp_rovodev_migrate_tier_data.js';

export async function handleTierMigration(request, env) {
  try {
    const result = await migrateTierData(env);
    
    return new Response(JSON.stringify(result, null, 2), {
      status: result.success ? 200 : 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      message: 'Migration failed',
      error: error.message
    }, null, 2), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}