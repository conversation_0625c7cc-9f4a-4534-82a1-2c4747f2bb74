// switch-to-d1.js
// Script to switch all service imports from KV to D1 versions

import fs from 'fs';
import path from 'path';

const serviceMapping = {
  'UserService': 'UserServiceD1',
  'TierService': 'TierServiceD1', 
  'ApiKeyService': 'ApiKeyServiceD1',
  'EmailQueueService': 'EmailQueueServiceD1'
};

const importMapping = {
  './userService.js': './userServiceD1.js',
  './tierService.js': './tierServiceD1.js',
  './apiKeyService.js': './apiKeyServiceD1.js',
  './emailQueueService.js': './emailQueueServiceD1.js',
  '../services/userService.js': '../services/userServiceD1.js',
  '../services/tierService.js': '../services/tierServiceD1.js',
  '../services/apiKeyService.js': '../services/apiKeyServiceD1.js',
  '../services/emailQueueService.js': '../services/emailQueueServiceD1.js'
};

function findJSFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      findJSFiles(fullPath, files);
    } else if (item.endsWith('.js') && !item.includes('D1') && !item.includes('switch-to-d1')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Update imports
  for (const [oldImport, newImport] of Object.entries(importMapping)) {
    const oldImportRegex = new RegExp(`from\\s+["']${oldImport.replace('.', '\\.')}["']`, 'g');
    if (oldImportRegex.test(content)) {
      content = content.replace(oldImportRegex, `from "${newImport}"`);
      updated = true;
      console.log(`  ✅ Updated import: ${oldImport} → ${newImport}`);
    }
  }
  
  // Update class instantiations
  for (const [oldService, newService] of Object.entries(serviceMapping)) {
    const serviceRegex = new RegExp(`new\\s+${oldService}\\s*\\(`, 'g');
    if (serviceRegex.test(content)) {
      content = content.replace(serviceRegex, `new ${newService}(`);
      updated = true;
      console.log(`  ✅ Updated service instantiation: ${oldService} → ${newService}`);
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`📝 Updated file: ${filePath}`);
  }
  
  return updated;
}

function switchToD1() {
  console.log('🔄 Switching to D1 services...');
  
  const srcDir = './src';
  const jsFiles = findJSFiles(srcDir);
  
  let totalUpdated = 0;
  
  for (const file of jsFiles) {
    console.log(`\n🔍 Checking: ${file}`);
    const wasUpdated = updateFile(file);
    if (wasUpdated) {
      totalUpdated++;
    }
  }
  
  console.log(`\n✅ Migration completed! Updated ${totalUpdated} files.`);
  console.log('\nNext steps:');
  console.log('1. Test the application thoroughly');
  console.log('2. Remove KV configuration from wrangler.toml');
  console.log('3. Deploy to production');
}

function revertToKV() {
  console.log('🔄 Reverting to KV services...');
  
  const reverseServiceMapping = {};
  const reverseImportMapping = {};
  
  // Create reverse mappings
  for (const [old, new_] of Object.entries(serviceMapping)) {
    reverseServiceMapping[new_] = old;
  }
  
  for (const [old, new_] of Object.entries(importMapping)) {
    reverseImportMapping[new_] = old;
  }
  
  const srcDir = './src';
  const jsFiles = findJSFiles(srcDir);
  
  let totalReverted = 0;
  
  for (const file of jsFiles) {
    console.log(`\n🔍 Checking: ${file}`);
    let content = fs.readFileSync(file, 'utf8');
    let reverted = false;
    
    // Revert imports
    for (const [d1Import, kvImport] of Object.entries(reverseImportMapping)) {
      const d1ImportRegex = new RegExp(`from\\s+["']${d1Import.replace('.', '\\.')}["']`, 'g');
      if (d1ImportRegex.test(content)) {
        content = content.replace(d1ImportRegex, `from "${kvImport}"`);
        reverted = true;
        console.log(`  ✅ Reverted import: ${d1Import} → ${kvImport}`);
      }
    }
    
    // Revert class instantiations
    for (const [d1Service, kvService] of Object.entries(reverseServiceMapping)) {
      const serviceRegex = new RegExp(`new\\s+${d1Service}\\s*\\(`, 'g');
      if (serviceRegex.test(content)) {
        content = content.replace(serviceRegex, `new ${kvService}(`);
        reverted = true;
        console.log(`  ✅ Reverted service: ${d1Service} → ${kvService}`);
      }
    }
    
    if (reverted) {
      fs.writeFileSync(file, content, 'utf8');
      console.log(`📝 Reverted file: ${file}`);
      totalReverted++;
    }
  }
  
  console.log(`\n✅ Revert completed! Reverted ${totalReverted} files.`);
}

// Check command line arguments
const command = process.argv[2];

if (command === 'switch') {
  switchToD1();
} else if (command === 'revert') {
  revertToKV();
} else {
  console.log('Usage:');
  console.log('  node switch-to-d1.js switch  - Switch to D1 services');
  console.log('  node switch-to-d1.js revert  - Revert to KV services');
}