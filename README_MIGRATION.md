# KV to D1 Migration Guide

This guide explains how to migrate your project from Cloudflare KV storage to D1 serverless SQL database.

## Overview

The migration process involves:
1. Setting up D1 database
2. Running the migration script
3. Testing the new implementation
4. Switching to D1-only mode

## Step 1: Setup D1 Database

1. Run the setup script:
```bash
./setup-d1.sh
```

2. Update `wrangler.toml` with the database ID from the output:
```toml
[[d1_databases]]
binding = "DB"
database_name = "supersense-db"
database_id = "your-actual-database-id-here"
```

## Step 2: Deploy and Test

1. Deploy the application:
```bash
wrangler deploy
```

2. Test D1 connection:
```bash
curl -X GET https://your-worker.your-subdomain.workers.dev/api/migration/test-d1
```

3. Check migration status:
```bash
curl -X GET https://your-worker.your-subdomain.workers.dev/api/migration/status
```

## Step 3: Run Migration

1. Start the migration:
```bash
curl -X POST https://your-worker.your-subdomain.workers.dev/api/migration/run
```

2. Monitor the migration progress in the logs:
```bash
wrangler tail
```

3. Verify migration completion:
```bash
curl -X GET https://your-worker.your-subdomain.workers.dev/api/migration/status
```

## Step 4: Switch to D1 Services

The project includes both KV and D1 services. To switch to D1:

1. Update service imports in your controllers to use D1 versions:
   - `UserService` → `UserServiceD1`
   - `TierService` → `TierServiceD1`
   - `ApiKeyService` → `ApiKeyServiceD1`
   - `EmailQueueService` → `EmailQueueServiceD1`

2. Test all functionality with D1 services

3. Remove KV configuration from `wrangler.toml` when confident

## Service Mapping

| KV Service | D1 Service | Status |
|------------|------------|---------|
| `UserService` | `UserServiceD1` | ✅ Ready |
| `TierService` | `TierServiceD1` | ✅ Ready |
| `ApiKeyService` | `ApiKeyServiceD1` | ✅ Ready |
| `EmailQueueService` | `EmailQueueServiceD1` | ✅ Ready |

## Database Schema

The D1 database uses the following main tables:
- `users` - User accounts
- `domains` - Domain registrations
- `api_keys` - API key mappings
- `subscriptions` - Payment subscriptions
- `payments` - Payment records
- `webhook_events` - Webhook event logs
- `email_queue` - Email queue items
- `quota_usage` - API usage tracking
- `tier_settings` - Tier configurations

## Migration Endpoints

- `GET /api/migration/status` - Check migration status
- `POST /api/migration/run` - Run migration
- `GET /api/migration/test-d1` - Test D1 connection
- `POST /api/migration/init-schema` - Check schema

## Rollback Plan

If issues occur:
1. Keep KV configuration in `wrangler.toml`
2. Revert service imports to original KV versions
3. Redeploy application
4. Investigate and fix D1 issues
5. Re-run migration when ready

## Performance Considerations

D1 offers several advantages over KV:
- SQL queries for complex data relationships
- ACID transactions
- Better data consistency
- More efficient for relational data
- Lower latency for complex queries

## Monitoring

Monitor the migration and D1 performance:
- Check Cloudflare dashboard for D1 metrics
- Monitor application logs for errors
- Test all API endpoints after migration
- Verify data integrity

## Support

If you encounter issues:
1. Check the migration logs
2. Verify D1 database configuration
3. Test individual endpoints
4. Check data consistency between KV and D1