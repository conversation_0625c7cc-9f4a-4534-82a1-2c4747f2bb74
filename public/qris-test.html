<!DOCTYPE html>
<html>
  <head>
    <title>QRIS Payment Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .payment-form {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
      }
      input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #45a049;
      }
      .qr-container {
        text-align: center;
        margin-top: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        display: none;
      }
      .error {
        color: red;
        margin-top: 10px;
      }
    </style>
  </head>
  <body>
    <h1>QRIS Payment Test</h1>

    <div class="payment-form">
      <h2>Create QRIS Payment</h2>
      <div class="form-group">
        <label for="external_id">External ID:</label>
        <input type="text" id="external_id" value="qris-payment-001" />
      </div>
      <div class="form-group">
        <label for="amount">Amount (IDR):</label>
        <input type="number" id="amount" value="10000" />
      </div>
      <div class="form-group">
        <label for="description">Description:</label>
        <input type="text" id="description" value="Payment for order #123" />
      </div>
      <button onclick="createQRISPayment()">Create QRIS Payment</button>
      <div id="error" class="error"></div>
    </div>

    <div id="qr-container" class="qr-container">
      <h2>Scan QR Code to Pay</h2>
      <div id="amount-display"></div>
      <div id="order-id-display"></div>
      <img id="qr-code" alt="QRIS Payment QR Code" style="margin: 20px" />
      <div>
        Please scan this QR code using your mobile banking or e-wallet app
      </div>
      <div style="margin-top: 10px">
        <strong>Expires at: </strong><span id="expires-at"></span>
      </div>
    </div>

    <script>
      async function createQRISPayment() {
        const external_id = document.getElementById("external_id").value;
        const amount = parseInt(document.getElementById("amount").value);
        const description = document.getElementById("description").value;

        try {
          const response = await fetch("/api/xendit/qris", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              external_id,
              amount,
              description,
            }),
          });

          const result = await response.json();

          if (result.success) {
            // Show QR container
            document.getElementById("qr-container").style.display = "block";

            // Set QR code image
            document.getElementById("qr-code").src = result.data.qr_code_image;

            // Set payment details
            document.getElementById(
              "amount-display"
            ).textContent = `Amount: Rp. ${result.data.amount.toLocaleString(
              "id-ID"
            )}`;
            document.getElementById(
              "order-id-display"
            ).textContent = `Order ID: ${result.data.reference_id}`;

            // Format and display expiry time
            const expiryDate = new Date(result.data.expires_at);
            document.getElementById("expires-at").textContent =
              expiryDate.toLocaleString("id-ID");

            // Clear any previous errors
            document.getElementById("error").textContent = "";
          } else {
            document.getElementById("error").textContent = result.error;
          }
        } catch (error) {
          document.getElementById("error").textContent =
            "Failed to create QRIS payment: " + error.message;
        }
      }
    </script>
  </body>
</html>
