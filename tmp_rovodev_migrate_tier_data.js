// Migration script to populate t_tier_setting table with tier data
const tierData = {
  "starter": {
    "name": "Starter",
    "maxQuota": 10,
    "imagesQuota": 10,
    "contentQuota": 10,
    "titleQuota": 10,
    "domainLimit": 1,
    "price": 0,
    "expirationDays": 30,
    "addon1": false,
    "addon2": false,
    "addon1_price": 4.99,
    "addon2_price": 9.99,
    "features": ["Basic API access", "Community support", "1 domain"]
  },
  "basic": {
    "name": "Basic",
    "maxQuota": 100,
    "imagesQuota": 100,
    "contentQuota": 100,
    "titleQuota": 100,
    "domainLimit": 5,
    "price": 15,
    "expirationDays": 30,
    "addon1": false,
    "addon2": false,
    "addon1_price": 9.99,
    "addon2_price": 19.99,
    "features": ["Increased quota", "Email support", "Up to 5 domains"]
  },
  "pro": {
    "name": "Pro",
    "maxQuota": 500,
    "imagesQuota": 500,
    "contentQuota": 500,
    "titleQuota": 500,
    "domainLimit": 15,
    "price": 40,
    "expirationDays": 30,
    "addon1": false,
    "addon2": false,
    "addon1_price": 19.99,
    "addon2_price": 39.99,
    "features": ["High quota", "Priority support", "Up to 15 domains"]
  },
  "enterprise": {
    "name": "Enterprise",
    "maxQuota": 2000,
    "imagesQuota": 2000,
    "contentQuota": 2000,
    "titleQuota": 2000,
    "domainLimit": -1,
    "price": 99,
    "expirationDays": 30,
    "addon1": false,
    "addon2": false,
    "addon1_price": 29.99,
    "addon2_price": 49.99,
    "features": ["Maximum quota", "Priority support", "24/7 phone support", "Unlimited domains"]
  }
};

async function migrateTierData(env) {
  console.log('Starting tier data migration...');

  try {
    // First, create the table (execute statements separately)
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS t_tier_setting (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        max_quota INTEGER NOT NULL,
        images_quota INTEGER NOT NULL,
        content_quota INTEGER NOT NULL,
        title_quota INTEGER NOT NULL,
        domain_limit INTEGER NOT NULL,
        price REAL NOT NULL,
        expiration_days INTEGER NOT NULL,
        addon1 BOOLEAN DEFAULT FALSE,
        addon2 BOOLEAN DEFAULT FALSE,
        addon1_price REAL NOT NULL,
        addon2_price REAL NOT NULL,
        features TEXT NOT NULL,
        created_at TEXT NOT NULL DEFAULT (datetime('now')),
        updated_at TEXT NOT NULL DEFAULT (datetime('now'))
      )
    `;

    try {
      await env.DB.prepare(createTableSQL).run();
      console.log('✅ Table t_tier_setting created successfully');
    } catch (error) {
      console.log('ℹ️ Table t_tier_setting already exists or creation skipped');
    }

    // Create indexes separately
    try {
      await env.DB.prepare('CREATE INDEX IF NOT EXISTS idx_t_tier_setting_id ON t_tier_setting(id)').run();
      await env.DB.prepare('CREATE INDEX IF NOT EXISTS idx_t_tier_setting_price ON t_tier_setting(price)').run();
      console.log('✅ Indexes created successfully');
    } catch (error) {
      console.log('ℹ️ Indexes already exist or creation skipped');
    }
    
    // Clear existing data
    await env.DB.prepare('DELETE FROM t_tier_setting').run();
    console.log('✅ Cleared existing tier data');
    
    // Insert tier data
    const insertSQL = `
      INSERT INTO t_tier_setting (
        id, name, max_quota, images_quota, content_quota, title_quota,
        domain_limit, price, expiration_days, addon1, addon2,
        addon1_price, addon2_price, features
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const stmt = env.DB.prepare(insertSQL);
    
    for (const [tierId, tier] of Object.entries(tierData)) {
      await stmt.bind(
        tierId,
        tier.name,
        tier.maxQuota,
        tier.imagesQuota,
        tier.contentQuota,
        tier.titleQuota,
        tier.domainLimit,
        tier.price,
        tier.expirationDays,
        tier.addon1 ? 1 : 0,
        tier.addon2 ? 1 : 0,
        tier.addon1_price,
        tier.addon2_price,
        JSON.stringify(tier.features)
      ).run();
      
      console.log(`✅ Inserted tier: ${tierId} (${tier.name})`);
    }
    
    // Verify the data
    const result = await env.DB.prepare('SELECT * FROM t_tier_setting ORDER BY price').all();
    console.log('\n📊 Migration Results:');
    console.log(`Total tiers migrated: ${result.results.length}`);
    
    result.results.forEach(tier => {
      console.log(`- ${tier.id}: ${tier.name} ($${tier.price})`);
    });
    
    return {
      success: true,
      message: `Successfully migrated ${result.results.length} tiers`,
      data: result.results
    };
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
}

// Export for use in routes
export { migrateTierData, tierData };