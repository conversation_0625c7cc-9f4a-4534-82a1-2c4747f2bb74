// migrate-kv-to-d1.js
// Migration script to move data from KV storage to D1 database

import { DatabaseService } from "./src/services/databaseService.js";

export class KVToD1Migrator {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
    this.kv = env.USERS_KV;
  }

  async migrateAllData() {
    console.log("🚀 Starting KV to D1 migration...");
    
    try {
      // Initialize database schema first
      await this.initializeDatabase();
      
      // Migrate different data types
      await this.migrateTierSettings();
      await this.migrateUsers();
      await this.migrateSubscriptions();
      await this.migratePayments();
      await this.migrateWebhookEvents();
      await this.migrateEmailQueue();
      await this.migrateUserTiers();
      await this.migrateEmailTiers();
      await this.migrateQuotaUsage();
      
      console.log("✅ Migration completed successfully!");
      
      // Verify migration
      await this.verifyMigration();
      
    } catch (error) {
      console.error("❌ Migration failed:", error);
      throw error;
    }
  }

  async initializeDatabase() {
    console.log("🔄 Initializing database schema...");
    
    // The schema should already be applied via wrangler d1 execute
    // This is just a verification step
    try {
      await this.db.getTierSettings();
      console.log("✅ Database schema verified");
    } catch (error) {
      console.log("⚠️ Database schema may need to be initialized");
      throw error;
    }
  }

  async migrateTierSettings() {
    console.log("🔄 Migrating tier settings...");
    
    try {
      const tierSettings = await this.kv.get("settings:tiers", "json");
      
      if (tierSettings) {
        await this.db.createOrUpdateTierSettings(
          tierSettings.config,
          tierSettings.version || "1.0"
        );
        console.log("✅ Tier settings migrated");
      } else {
        console.log("ℹ️ No tier settings found in KV");
      }
    } catch (error) {
      console.error("Error migrating tier settings:", error);
    }
  }

  async migrateUsers() {
    console.log("🔄 Migrating users...");
    
    try {
      // Get all user keys
      const { keys: userKeys } = await this.kv.list({ prefix: "user:" });
      const { keys: emailKeys } = await this.kv.list({ prefix: "email:" });
      const { keys: portalUserKeys } = await this.kv.list({ prefix: "portal_user:" });
      
      const migratedUsers = new Set();
      
      // Migrate regular users
      for (const key of userKeys) {
        try {
          const userData = await this.kv.get(key.name, "json");
          if (userData && !migratedUsers.has(userData.id)) {
            await this.migrateUser(userData);
            migratedUsers.add(userData.id);
          }
        } catch (error) {
          console.error(`Error migrating user ${key.name}:`, error);
        }
      }
      
      // Migrate portal users
      for (const key of portalUserKeys) {
        try {
          const userData = await this.kv.get(key.name, "json");
          if (userData && !migratedUsers.has(userData.id)) {
            await this.migratePortalUser(userData);
            migratedUsers.add(userData.id);
          }
        } catch (error) {
          console.error(`Error migrating portal user ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${migratedUsers.size} users`);
    } catch (error) {
      console.error("Error migrating users:", error);
    }
  }

  async migrateUser(userData) {
    // Create user record
    const user = {
      id: userData.id,
      email: userData.email,
      password: userData.password,
      type: 'api',
      status: 'active',
      created_at: userData.created_at || userData.createdAt || new Date().toISOString(),
      updated_at: userData.updated_at || userData.updatedAt || new Date().toISOString()
    };
    
    await this.db.createUser(user);
    
    // Migrate domains
    if (userData.domains && Array.isArray(userData.domains)) {
      for (const domainData of userData.domains) {
        await this.migrateDomain(domainData, userData.id);
      }
    }
  }

  async migratePortalUser(userData) {
    // Create portal user record
    const user = {
      id: userData.id,
      email: userData.email,
      password: null,
      type: 'portal',
      status: userData.status || 'active',
      created_at: userData.created_at || userData.createdAt || new Date().toISOString(),
      updated_at: userData.updated_at || userData.updatedAt || new Date().toISOString()
    };
    
    await this.db.createUser(user);
    
    // Migrate portal credentials if they exist
    try {
      const credentials = await this.kv.get(`portal_credentials:${userData.id}`, "json");
      if (credentials) {
        await this.db.createPortalCredentials(userData.id, {
          plain_password: credentials.plainPassword,
          hashed_password: credentials.hashedPassword,
          created_at: credentials.createdAt || new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`Error migrating credentials for user ${userData.id}:`, error);
    }
  }

  async migrateDomain(domainData, userId) {
    const domain = {
      id: domainData.id || `domain_${Date.now()}_${Math.random()}`,
      user_id: userId,
      domain: domainData.domain,
      api_key: domainData.api_key,
      status: domainData.status || 'active',
      tier: domainData.tier || 'free',
      created_at: domainData.created_at || domainData.createdAt || new Date().toISOString(),
      activated_at: domainData.activated_at || domainData.activatedAt
    };
    
    await this.db.createDomain(domain);
    
    // Create API key mapping
    await this.db.createApiKeyMapping(domainData.api_key, userId, domain.id);
  }

  async migrateSubscriptions() {
    console.log("🔄 Migrating subscriptions...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "subscription:" });
      
      for (const key of keys) {
        try {
          const subscriptionData = await this.kv.get(key.name, "json");
          if (subscriptionData) {
            await this.db.createSubscription({
              subscription_id: subscriptionData.subscriptionId,
              plan_id: subscriptionData.planId,
              product_id: subscriptionData.productId,
              user_id: subscriptionData.userId,
              tier: subscriptionData.tier,
              base_price: subscriptionData.basePrice,
              addons: subscriptionData.addons,
              total_price: subscriptionData.totalPrice,
              status: subscriptionData.status,
              created_at: subscriptionData.created_at || subscriptionData.createdAt || new Date().toISOString(),
              activated_at: subscriptionData.activated_at || subscriptionData.activatedAt,
              updated_at: subscriptionData.updated_at || subscriptionData.updatedAt || new Date().toISOString(),
              next_billing_time: subscriptionData.nextBillingTime
            });
          }
        } catch (error) {
          console.error(`Error migrating subscription ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} subscriptions`);
    } catch (error) {
      console.error("Error migrating subscriptions:", error);
    }
  }

  async migratePayments() {
    console.log("🔄 Migrating payments...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "payment:" });
      
      for (const key of keys) {
        try {
          const paymentData = await this.kv.get(key.name, "json");
          if (paymentData) {
            await this.db.createPayment({
              id: paymentData.id,
              user_id: paymentData.userId,
              subscription_id: paymentData.subscriptionId,
              amount_currency: paymentData.amount?.currency_code,
              amount_value: paymentData.amount?.value,
              time: paymentData.time,
              recorded_at: paymentData.recordedAt || new Date().toISOString(),
              status: 'completed'
            });
          }
        } catch (error) {
          console.error(`Error migrating payment ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} payments`);
    } catch (error) {
      console.error("Error migrating payments:", error);
    }
  }

  async migrateWebhookEvents() {
    console.log("🔄 Migrating webhook events...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "webhook_event:" });
      
      for (const key of keys) {
        try {
          const eventData = await this.kv.get(key.name, "json");
          if (eventData) {
            await this.db.createWebhookEvent({
              id: eventData.id,
              event_type: eventData.event_type,
              event_version: eventData.event_version,
              create_time: eventData.create_time,
              resource_type: eventData.resource_type,
              resource_version: eventData.resource_version,
              summary: eventData.summary,
              resource_data: eventData.resource,
              received_at: eventData.receivedAt || new Date().toISOString(),
              processed: false
            });
          }
        } catch (error) {
          console.error(`Error migrating webhook event ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} webhook events`);
    } catch (error) {
      console.error("Error migrating webhook events:", error);
    }
  }

  async migrateEmailQueue() {
    console.log("🔄 Migrating email queue...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "email_queue:" });
      
      for (const key of keys) {
        try {
          const queueData = await this.kv.get(key.name, "json");
          if (queueData) {
            await this.db.createEmailQueueItem({
              id: queueData.id,
              user_id: queueData.user_id,
              type: queueData.type,
              status: queueData.status,
              data: queueData.data,
              attempts: queueData.attempts || 0,
              queued_at: queueData.queued_at,
              process_after: queueData.process_after,
              sent_at: queueData.sent_at,
              error_message: queueData.error_message
            });
          }
        } catch (error) {
          console.error(`Error migrating email queue item ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} email queue items`);
    } catch (error) {
      console.error("Error migrating email queue:", error);
    }
  }

  async migrateUserTiers() {
    console.log("🔄 Migrating user tiers...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "user_tier:" });
      
      for (const key of keys) {
        try {
          const tierData = await this.kv.get(key.name, "json");
          if (tierData) {
            const userId = key.name.replace("user_tier:", "");
            await this.db.setUserTier(userId, tierData.tier);
          }
        } catch (error) {
          console.error(`Error migrating user tier ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} user tiers`);
    } catch (error) {
      console.error("Error migrating user tiers:", error);
    }
  }

  async migrateEmailTiers() {
    console.log("🔄 Migrating email tiers...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "email_tier:" });
      
      for (const key of keys) {
        try {
          const tierData = await this.kv.get(key.name, "json");
          if (tierData) {
            const email = key.name.replace("email_tier:", "");
            await this.db.setEmailTier(email, tierData.tier);
          }
        } catch (error) {
          console.error(`Error migrating email tier ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} email tiers`);
    } catch (error) {
      console.error("Error migrating email tiers:", error);
    }
  }

  async migrateQuotaUsage() {
    console.log("🔄 Migrating quota usage...");
    
    try {
      const { keys } = await this.kv.list({ prefix: "quota_usage:" });
      
      for (const key of keys) {
        try {
          const usageData = await this.kv.get(key.name, "json");
          if (usageData) {
            // Parse key: quota_usage:userId:type
            const parts = key.name.split(":");
            if (parts.length >= 3) {
              const userId = parts[1];
              const type = parts[2];
              
              await this.db.updateQuotaUsage(userId, type, {
                count: usageData.count || 0,
                last_reset: usageData.lastReset,
                reset_date: usageData.resetDate
              });
            }
          }
        } catch (error) {
          console.error(`Error migrating quota usage ${key.name}:`, error);
        }
      }
      
      console.log(`✅ Migrated ${keys.length} quota usage records`);
    } catch (error) {
      console.error("Error migrating quota usage:", error);
    }
  }

  async verifyMigration() {
    console.log("🔄 Verifying migration...");
    
    try {
      // Count records in D1
      const userCount = await this.db.db.prepare("SELECT COUNT(*) as count FROM users").first();
      const domainCount = await this.db.db.prepare("SELECT COUNT(*) as count FROM domains").first();
      const subscriptionCount = await this.db.db.prepare("SELECT COUNT(*) as count FROM subscriptions").first();
      
      console.log("📊 Migration verification:");
      console.log(`   Users: ${userCount.count}`);
      console.log(`   Domains: ${domainCount.count}`);
      console.log(`   Subscriptions: ${subscriptionCount.count}`);
      
      console.log("✅ Migration verification completed");
    } catch (error) {
      console.error("Error verifying migration:", error);
    }
  }
}

// Export function to run migration
export async function runMigration(env) {
  const migrator = new KVToD1Migrator(env);
  await migrator.migrateAllData();
}