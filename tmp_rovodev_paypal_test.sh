#!/bin/bash

# PayPal Subscription Full Flow Test Script
# Email: <EMAIL>
# Tier: pro

BASE_URL="http://localhost:3000"
EMAIL="<EMAIL>"
TIER="pro"

echo "🚀 PAYPAL SUBSCRIPTION FULL FLOW TEST"
echo "======================================"
echo "📧 Email: $EMAIL"
echo "🎯 Tier: $TIER"
echo "🌐 Base URL: $BASE_URL"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to make HTTP requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "\n${BLUE}🔄 $description${NC}"
    echo "📤 $method $BASE_URL$endpoint"
    
    if [ -n "$data" ]; then
        echo "📋 Data: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            "$BASE_URL$endpoint")
    fi
    
    # Split response and status code
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "📥 Response ($http_code): $response_body"
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "${GREEN}✅ Success${NC}"
        echo "$response_body"
        return 0
    else
        echo -e "${RED}❌ Failed (HTTP $http_code)${NC}"
        echo "$response_body"
        return 1
    fi
}

# Step 1: Check tier settings
echo -e "\n${YELLOW}🏁 STEP 1: Check Tier Settings${NC}"
echo "================================================"

if make_request "GET" "/api/tiers/settings" "" "Getting tier settings"; then
    echo -e "${GREEN}✅ STEP 1 PASSED${NC}"
else
    echo -e "${RED}❌ STEP 1 FAILED${NC}"
    exit 1
fi

# Step 2: Create PayPal subscription
echo -e "\n${YELLOW}🏁 STEP 2: Create PayPal Subscription${NC}"
echo "================================================"

subscription_data='{
    "email": "'$EMAIL'",
    "tier": "'$TIER'",
    "addons": {
        "addon1": false,
        "addon2": true
    }
}'

if response=$(make_request "POST" "/api/subscriptions/" "$subscription_data" "Creating PayPal subscription"); then
    echo -e "${GREEN}✅ STEP 2 PASSED${NC}"
    
    # Extract subscription ID and approval URL
    subscription_id=$(echo "$response" | grep -o '"subscriptionId":"[^"]*"' | cut -d'"' -f4)
    approval_url=$(echo "$response" | grep -o '"approvalUrl":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$subscription_id" ] && [ "$subscription_id" != "null" ]; then
        echo "📋 Subscription ID: $subscription_id"
        echo "🔗 Approval URL: $approval_url"
        
        # Save for later steps
        echo "$subscription_id" > /tmp/paypal_subscription_id
        echo "$approval_url" > /tmp/paypal_approval_url
    else
        echo -e "${RED}❌ Could not extract subscription ID${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ STEP 2 FAILED${NC}"
    exit 1
fi

# Step 3: Check subscription status
echo -e "\n${YELLOW}🏁 STEP 3: Check Subscription Status${NC}"
echo "================================================"

if [ -f /tmp/paypal_subscription_id ]; then
    subscription_id=$(cat /tmp/paypal_subscription_id)
    
    if make_request "GET" "/api/subscriptions/status/$subscription_id" "" "Checking subscription status"; then
        echo -e "${GREEN}✅ STEP 3 PASSED${NC}"
    else
        echo -e "${RED}❌ STEP 3 FAILED${NC}"
    fi
else
    echo -e "${RED}❌ No subscription ID found${NC}"
fi

# Step 4: Display approval instructions
echo -e "\n${YELLOW}🏁 STEP 4: User Approval Required${NC}"
echo "================================================"

if [ -f /tmp/paypal_approval_url ]; then
    approval_url=$(cat /tmp/paypal_approval_url)
    
    echo -e "${BLUE}🔗 APPROVAL URL:${NC}"
    echo "$approval_url"
    echo ""
    echo -e "${YELLOW}⚠️  MANUAL STEPS REQUIRED:${NC}"
    echo "1. Open the approval URL above in your browser"
    echo "2. Log in to your PayPal sandbox account"
    echo "3. Approve the subscription"
    echo "4. You will be redirected back to the success URL"
    echo ""
    echo -e "${BLUE}Press Enter after completing the approval process...${NC}"
    read -r
else
    echo -e "${RED}❌ No approval URL found${NC}"
    exit 1
fi

# Step 5: Handle subscription success
echo -e "\n${YELLOW}🏁 STEP 5: Handle Subscription Success${NC}"
echo "================================================"

if [ -f /tmp/paypal_subscription_id ]; then
    subscription_id=$(cat /tmp/paypal_subscription_id)
    
    if make_request "POST" "/api/subscriptions/success/$subscription_id" "" "Handling subscription success"; then
        echo -e "${GREEN}✅ STEP 5 PASSED${NC}"
    else
        echo -e "${RED}❌ STEP 5 FAILED${NC}"
    fi
else
    echo -e "${RED}❌ No subscription ID found${NC}"
fi

# Step 6: Verify user tier upgrade
echo -e "\n${YELLOW}🏁 STEP 6: Verify User Tier Upgrade${NC}"
echo "================================================"

encoded_email=$(echo "$EMAIL" | sed 's/@/%40/g')

if response=$(make_request "GET" "/api/users/tier/$encoded_email" "" "Checking user tier"); then
    user_tier=$(echo "$response" | jq -r '.data.tier // empty')
    
    if [ "$user_tier" = "$TIER" ]; then
        echo -e "${GREEN}🎉 SUCCESS! User has been upgraded to '$TIER' tier${NC}"
        echo -e "${GREEN}✅ STEP 6 PASSED${NC}"
    else
        echo -e "${RED}❌ Tier mismatch. Expected: '$TIER', Got: '$user_tier'${NC}"
        echo -e "${RED}❌ STEP 6 FAILED${NC}"
    fi
else
    echo -e "${RED}❌ STEP 6 FAILED${NC}"
fi

# Step 7: Test additional endpoints
echo -e "\n${YELLOW}🏁 STEP 7: Test Additional Endpoints${NC}"
echo "================================================"

echo -e "\n🧪 Testing subscription router..."
if make_request "GET" "/api/subscriptions/test" "" "Testing subscription router"; then
    echo -e "${GREEN}✅ Subscription router test passed${NC}"
else
    echo -e "${RED}❌ Subscription router test failed${NC}"
fi

echo -e "\n🧪 Testing user subscriptions list..."
if make_request "GET" "/api/subscriptions/user/$encoded_email" "" "Getting user subscriptions"; then
    echo -e "${GREEN}✅ User subscriptions test passed${NC}"
else
    echo -e "${RED}❌ User subscriptions test failed${NC}"
fi

# Cleanup
echo -e "\n${BLUE}🧹 Cleaning up temporary files...${NC}"
rm -f /tmp/paypal_subscription_id /tmp/paypal_approval_url

# Final summary
echo -e "\n${BLUE}======================================"
echo "📊 PAYPAL SUBSCRIPTION TEST COMPLETE"
echo "======================================${NC}"
echo -e "${GREEN}🎉 If all steps passed, your PayPal subscription flow is working correctly!${NC}"
echo -e "${YELLOW}⚠️  Remember to check the PayPal sandbox dashboard for transaction details.${NC}"
echo ""
echo "Test completed for:"
echo "📧 Email: $EMAIL"
echo "🎯 Tier: $TIER"
echo "🌐 Base URL: $BASE_URL"