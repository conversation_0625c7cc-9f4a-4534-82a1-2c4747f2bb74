// kv-cli.js
const readline = require('readline');
// Fix for node-fetch import
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const PORT = 3000;
const BASE_URL = `http://127.0.0.1:${PORT}/api/users/debug`;

async function fetchData() {
  try {
    console.log('Fetching data from:', `${BASE_URL}/kv`);
    const response = await fetch(`${BASE_URL}/kv`);
    console.log('Response status:', response.status);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Data received:', JSON.stringify(data, null, 2));
    return data.data;
  } catch (error) {
    console.error('Error fetching data:', error.message);
    if (error.message.includes('ECONNREFUSED')) {
      console.error('\nMake sure your wrangler dev server is running!');
      console.error('Run "npm run dev" in another terminal first.');
    }
    return null;
  }
}

async function deleteKey(key) {
  try {
    const response = await fetch(`${BASE_URL}/kv/delete`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ key })
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    console.log(`Deleted key: ${key}`);
  } catch (error) {
    console.error('Error deleting key:', error.message);
  }
}

function printTable(data) {
  if (!data || data.length === 0) return;
  
  // Calculate column widths
  const columns = {
    key: Math.max(...data.map(item => item.key.length), 'KEY'.length),
    type: Math.max(...data.map(item => item.key.split(':')[0].length), 'TYPE'.length)
  };

  // Print header
  console.log('\n' + '='.repeat(columns.key + columns.type + 20));
  console.log(
    'TYPE'.padEnd(columns.type + 2) +
    'KEY'.padEnd(columns.key + 2) +
    'VALUE'
  );
  console.log('='.repeat(columns.key + columns.type + 20));

  // Print rows
  data.forEach(item => {
    const type = item.key.split(':')[0];
    console.log(
      type.padEnd(columns.type + 2) +
      item.key.padEnd(columns.key + 2) +
      JSON.stringify(item.value)
    );
  });
  console.log('='.repeat(columns.key + columns.type + 20) + '\n');
}

function printHelp() {
  console.log(`
Available commands:
  ls                  List all keys and values
  ls <prefix>         List keys with specific prefix (user, email, etc.)
  get <key>           Get value for specific key
  del <key>           Delete specific key
  clear               Clear screen
  help                Show this help
  exit                Exit CLI

Examples:
  ls                  # List all keys
  ls user            # List only user keys
  get user:123       # Show value for user:123
  del user:123       # Delete key user:123
  `);
}

async function handleCommand(cmd) {
  const [command, ...args] = cmd.trim().split(' ');

  switch (command.toLowerCase()) {
    case 'ls': {
      const data = await fetchData();
      if (!data || !data.raw) return;

      const prefix = args[0];
      const items = data.raw.filter(item => 
        !prefix || item.key.startsWith(`${prefix}:`)
      );

      if (items.length === 0) {
        console.log('\nNo keys found' + (prefix ? ` with prefix "${prefix}:"` : ''));
        return;
      }

      printTable(items);
      console.log(`Total: ${items.length} keys\n`);
      break;
    }

    case 'get': {
      const data = await fetchData();
      if (!data) return;

      const key = args[0];
      if (!key) {
        console.log('Please specify a key to get');
        return;
      }

      const item = data.raw.find(i => i.key === key);
      if (item) {
        console.log('\nValue:');
        console.log(JSON.stringify(item.value, null, 2));
      } else {
        console.log('Key not found');
      }
      break;
    }

    case 'del': {
      const key = args[0];
      if (!key) {
        console.log('Please specify a key to delete');
        return;
      }
      await deleteKey(key);
      break;
    }

    case 'clear':
      console.clear();
      break;

    case 'help':
      printHelp();
      break;

    case 'exit':
      rl.close();
      process.exit(0);
      break;

    default:
      console.log('Unknown command. Type "help" for available commands.');
  }
}

async function startCLI() {
  // Non-interactive mode
  if (process.argv.length > 2) {
    const command = process.argv.slice(2).join(' ');
    await handleCommand(command);
    process.exit(0);
  }

  // Interactive mode
  console.clear();
  console.log('Local KV CLI - Type "help" for available commands\n');

  rl.on('line', async (line) => {
    await handleCommand(line);
    rl.prompt();
  });

  rl.on('close', () => {
    console.log('Goodbye!');
    process.exit(0);
  });

  rl.setPrompt('kv> ');
  rl.prompt();
}

startCLI();