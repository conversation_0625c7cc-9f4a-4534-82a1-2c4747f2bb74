#!/bin/bash

# Safe User Deletion Script for Testing
# This script uses the existing API endpoint to safely delete a user

# Configuration
API_BASE_URL="http://localhost:3000"  # Change to your actual API URL
EMAIL=""  # Set the email address you want to delete

# Function to delete user via API
delete_user_via_api() {
    local email="$1"
    
    if [ -z "$email" ]; then
        echo "❌ Error: Email address is required"
        echo "Usage: $0 <<EMAIL>>"
        exit 1
    fi
    
    echo "🗑️ Deleting user: $email"
    echo "📡 Making API request..."
    
    # Make the deletion request
    response=$(curl -s -w "\n%{http_code}" \
        -X DELETE \
        "$API_BASE_URL/api/portal/users?email=$email" \
        -H "Content-Type: application/json")
    
    # Extract response body and status code
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "📊 HTTP Status: $http_code"
    echo "📄 Response: $response_body"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ User deleted successfully!"
        echo "🔄 You can now re-register with the same email address"
    else
        echo "❌ Failed to delete user"
        echo "🔍 Check the response above for details"
    fi
}

# Check if email is provided as argument
if [ $# -eq 1 ]; then
    EMAIL="$1"
elif [ -z "$EMAIL" ]; then
    echo "❌ Error: Email address is required"
    echo "Usage: $0 <<EMAIL>>"
    echo "Or edit this script and set the EMAIL variable"
    exit 1
fi

# Validate email format (basic check)
if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
    echo "❌ Error: Invalid email format: $EMAIL"
    exit 1
fi

# Confirm deletion
echo "⚠️  WARNING: This will permanently delete the user and all associated data!"
echo "📧 Email: $EMAIL"
echo "🌐 API URL: $API_BASE_URL"
echo ""
read -p "Are you sure you want to proceed? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    delete_user_via_api "$EMAIL"
else
    echo "❌ Deletion cancelled"
    exit 0
fi
