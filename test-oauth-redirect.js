// Test Google OAuth callback redirect URLs
console.log("Testing Google OAuth callback redirects...");

// Test for existing user
const testExistingUser = async () => {
  console.log("\n=== Testing Existing User (<EMAIL>) ===");
  try {
    const response = await fetch('http://localhost:3000/api/portal/user?email=<EMAIL>');
    const result = await response.json();
    
    if (result.success) {
      console.log("✅ Existing user found:");
      console.log(`   ID: ${result.user.id}`);
      console.log(`   Email: ${result.user.email}`);
      console.log(`   Display Name: ${result.user.profile.displayName}`);
      console.log(`   Is Active: ${result.user.isActive}`);
      console.log(`   Google ID: ${result.user.googleId}`);
      
      // Simulate callback redirect for existing user
      const frontendUrl = "http://localhost:3001";
      const mockToken = "jwt_token_here";
      const redirectUrl = `${frontendUrl}/dashboard?token=${mockToken}&isNewUser=false&email=${encodeURIComponent(result.user.email)}&displayName=${encodeURIComponent(result.user.profile.displayName)}`;
      
      console.log("\n📍 Expected redirect URL for existing user:");
      console.log(redirectUrl);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};

// Test for new user
const testNewUser = async () => {
  console.log("\n=== Testing New User (<EMAIL>) ===");
  try {
    const response = await fetch('http://localhost:3000/api/portal/user?email=<EMAIL>');
    const result = await response.json();
    
    if (result.success) {
      console.log("✅ New user found:");
      console.log(`   ID: ${result.user.id}`);
      console.log(`   Email: ${result.user.email}`);
      console.log(`   Display Name: ${result.user.profile.displayName}`);
      console.log(`   Is Active: ${result.user.isActive}`);
      console.log(`   Google ID: ${result.user.googleId}`);
      
      // Simulate callback redirect for new user
      const frontendUrl = "http://localhost:3001";
      const mockToken = "jwt_token_here";
      const redirectUrl = `${frontendUrl}/dashboard?token=${mockToken}&isNewUser=true&email=${encodeURIComponent(result.user.email)}&displayName=${encodeURIComponent(result.user.profile.displayName)}`;
      
      console.log("\n📍 Expected redirect URL for new user:");
      console.log(redirectUrl);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};

// Run tests
testExistingUser().then(() => testNewUser());
