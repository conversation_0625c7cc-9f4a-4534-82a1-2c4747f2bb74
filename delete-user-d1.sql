-- Safe User Deletion from D1 Database
-- Replace '<EMAIL>' with the actual email address

-- Step 1: Find the user and get their ID
-- Run this first to confirm the user exists and get their ID
SELECT id, email, type, status, created_at 
FROM users 
WHERE email = '<EMAIL>';

-- Step 2: Check what data will be deleted (optional verification)
-- This shows all related data that will be automatically deleted due to CASCADE

-- Check domains
SELECT d.id, d.domain, d.api_key, d.status, d.tier 
FROM domains d 
JOIN users u ON d.user_id = u.id 
WHERE u.email = '<EMAIL>';

-- Check API keys
SELECT ak.api_key, ak.created_at 
FROM api_keys ak 
JOIN users u ON ak.user_id = u.id 
WHERE u.email = '<EMAIL>';

-- Check subscriptions
SELECT s.subscription_id, s.tier, s.status, s.total_price 
FROM subscriptions s 
JOIN users u ON s.user_id = u.id 
WHERE u.email = '<EMAIL>';

-- Check quota usage
SELECT qu.type, qu.count, qu.last_reset 
FROM quota_usage qu 
JOIN users u ON qu.user_id = u.id 
WHERE u.email = '<EMAIL>';

-- Check usage history count
SELECT COUNT(*) as usage_records 
FROM usage_history uh 
JOIN users u ON uh.user_id = u.id 
WHERE u.email = '<EMAIL>';

-- Step 3: Delete the user (this will CASCADE delete all related data)
-- ⚠️ WARNING: This is irreversible! Make sure you have the correct email!

DELETE FROM users WHERE email = '<EMAIL>';

-- Step 4: Verify deletion
-- These should return no results if deletion was successful
SELECT COUNT(*) as remaining_users FROM users WHERE email = '<EMAIL>';
SELECT COUNT(*) as remaining_domains FROM domains d JOIN users u ON d.user_id = u.id WHERE u.email = '<EMAIL>';

-- Step 5: Clean up email_tier table (not automatically cleaned due to no foreign key)
DELETE FROM email_tiers WHERE email = '<EMAIL>';

-- Optional: Check for any orphaned records (should be empty due to CASCADE)
SELECT 'orphaned_domains' as table_name, COUNT(*) as count FROM domains WHERE user_id NOT IN (SELECT id FROM users)
UNION ALL
SELECT 'orphaned_api_keys' as table_name, COUNT(*) as count FROM api_keys WHERE user_id NOT IN (SELECT id FROM users)
UNION ALL
SELECT 'orphaned_subscriptions' as table_name, COUNT(*) as count FROM subscriptions WHERE user_id NOT IN (SELECT id FROM users)
UNION ALL
SELECT 'orphaned_quota_usage' as table_name, COUNT(*) as count FROM quota_usage WHERE user_id NOT IN (SELECT id FROM users)
UNION ALL
SELECT 'orphaned_usage_history' as table_name, COUNT(*) as count FROM usage_history WHERE user_id NOT IN (SELECT id FROM users);
