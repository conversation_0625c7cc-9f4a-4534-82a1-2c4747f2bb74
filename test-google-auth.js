// Test script to simulate Google OAuth flow
const testCreatePortalUser = async () => {
  try {
    const response = await fetch('http://localhost:3000/api/portal/auth/test-create-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        googleId: '123456789',
        displayName: 'Santana Lawalata'
      })
    });

    const result = await response.json();
    console.log('Result:', result);
  } catch (error) {
    console.error('Error:', error);
  }
};

testCreatePortalUser();
