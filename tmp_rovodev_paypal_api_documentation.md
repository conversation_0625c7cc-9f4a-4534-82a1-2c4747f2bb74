# PayPal API Endpoints Documentation

## Base URL
```
/api/paypal
```

## Table of Contents
1. [Subscription Endpoints](#subscription-endpoints)
2. [Purchase Endpoints](#purchase-endpoints) 
3. [Webhook Endpoints](#webhook-endpoints)
4. [Flow Diagrams](#flow-diagrams)

---

## Subscription Endpoints

### 1. Create Subscription
**POST** `/api/paypal/subscriptions`

Creates a new PayPal subscription for recurring payments.

#### Request Payload
```json
{
  "tier": "pro",
  "email": "<EMAIL>",
  "addons": {
    "addon1": false,
    "addon2": true
  }
}
```

#### Request Fields
- `tier` (string, required): Subscription tier (e.g., "starter", "pro", "enterprise")
- `email` (string, required): User email address
- `addons` (object, optional): Add-on configuration
  - `addon1` (boolean): Enable addon 1
  - `addon2` (boolean): Enable addon 2

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "subscriptionId": "I-BW452GLLEP1G",
    "approvalUrl": "https://www.sandbox.paypal.com/webapps/billing/subscriptions/subscribe?ba_token=BA-2M539689GH908273Y",
    "tier": "pro",
    "basePrice": 40,
    "addons": {
      "addon1": false,
      "addon2": true
    },
    "totalPrice": 50,
    "status": "pending"
  }
}
```

#### Error Response (400)
```json
{
  "success": false,
  "message": "Email is required",
  "details": "Error stack trace"
}
```

---

### 2. Subscription Success Callback
**GET** `/api/paypal/subscriptions/success`

Handles successful PayPal subscription approval and activates the user's tier.

#### Query Parameters
- `ba_token` (string, required): Billing agreement token from PayPal

#### Flow
1. Extracts `ba_token` from URL parameters
2. Retrieves subscription data from KV storage using the token
3. Immediately upgrades user's tier
4. Cleans up temporary token mapping
5. Redirects to frontend dashboard

#### Success Redirect
```
{FRONTEND_URL}/dashboard?subscription_status=success&subscription_id={subscriptionId}
```

#### Error Redirect
```
{FRONTEND_URL}/dashboard?subscription_status=error&error={errorMessage}
```

---

### 3. Subscription Cancel Callback
**GET** `/api/paypal/subscriptions/cancel`

Handles PayPal subscription cancellation.

#### Success Redirect
```
{FRONTEND_URL}/dashboard?subscription_status=cancelled
```

---

### 4. Update Subscription Addons
**PATCH** `/api/paypal/subscriptions/:subscriptionId/addons`

Updates subscription addon configuration.

#### Headers
- `x-sps-key` (string, required): API key for authentication

#### Request Payload
```json
{
  "subscriptionId": "I-BW452GLLEP1G",
  "addons": {
    "addon1": true,
    "addon2": false
  }
}
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "success": true,
    "subscriptionId": "I-BW452GLLEP1G",
    "newPrice": 45,
    "addons": {
      "addon1": true,
      "addon2": false
    }
  }
}
```

---

### 5. Get Subscription Status
**GET** `/api/paypal/subscriptions/:subscriptionId/status`

Retrieves current subscription status from PayPal and local storage.

#### URL Parameters
- `subscriptionId` (string, required): PayPal subscription ID

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "subscriptionId": "I-BW452GLLEP1G",
    "status": "ACTIVE",
    "planId": "P-5ML4271244454362WXNWU5NQ",
    "startTime": "2023-01-01T00:00:00Z",
    "nextBillingTime": "2023-02-01T00:00:00Z",
    "lastPaymentTime": "2023-01-01T00:00:00Z",
    "failedPayments": 0,
    "tier": "pro",
    "price": 50,
    "localStatus": "active",
    "createdAt": "2023-01-01T00:00:00Z",
    "lastUpdated": "2023-01-15T10:30:00Z"
  }
}
```

---

## Purchase Endpoints

### 1. Create One-Time Purchase
**POST** `/api/paypal/purchases`

Creates a PayPal one-time purchase order for lifetime access.

#### Request Payload
```json
{
  "tier": "pro",
  "email": "<EMAIL>",
  "amount": 99.99,
  "addons": {
    "addon1": false,
    "addon2": true
  }
}
```

#### Request Fields
- `tier` (string, optional): Tier for reference
- `email` (string, required): User email address
- `amount` (number, required): Purchase amount in USD
- `addons` (object, optional): Add-on configuration for reference

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "orderId": "8XW83405XN8949048",
    "approvalUrl": "https://www.sandbox.paypal.com/checkoutnow?token=8XW83405XN8949048",
    "tier": "pro",
    "totalPrice": 99.99,
    "status": "pending"
  }
}
```

#### Error Response (400/500)
```json
{
  "success": false,
  "message": "Valid amount is required"
}
```

---

### 2. Purchase Success Callback
**GET** `/api/paypal/purchases/success`

Handles successful PayPal purchase completion.

#### Query Parameters
- `token` (string, required): PayPal order ID

#### Flow
1. Extracts order ID from `token` parameter
2. Retrieves purchase data from KV storage
3. Captures the payment via PayPal API
4. Upgrades user's tier with lifetime access
5. Cleans up temporary order data
6. Redirects to frontend

#### Success Redirect
```
{FRONTEND_URL}/dashboard?purchase_status=success&order_id={orderId}
```

---

### 3. Get Purchase Status
**GET** `/api/paypal/purchases/status`

Retrieves order status from PayPal.

#### Query Parameters
- `orderId` (string, required): PayPal order ID

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "orderId": "8XW83405XN8949048",
    "status": "COMPLETED",
    "intent": "CAPTURE",
    "createdTime": "2023-01-01T00:00:00Z",
    "updateTime": "2023-01-01T00:05:00Z",
    "purchaseUnits": [...],
    "payer": {...},
    "links": [...],
    "localData": {
      "email": "<EMAIL>",
      "tier": "pro",
      "addons": {...},
      "amount": 99.99,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

---

## Webhook Endpoints

### PayPal Webhook Handler
**POST** `/api/paypal/webhooks`

Handles PayPal subscription webhooks for automated tier management.

#### Headers (Required)
- `paypal-transmission-sig`: PayPal signature
- `paypal-cert-url`: PayPal certificate URL
- `paypal-transmission-id`: PayPal transmission ID
- `paypal-transmission-time`: PayPal transmission time

#### Webhook Payload Example
```json
{
  "event_type": "BILLING.SUBSCRIPTION.ACTIVATED",
  "resource": {
    "id": "I-BW452GLLEP1G",
    "status": "ACTIVE",
    "billing_info": {
      "next_billing_time": "2023-02-01T00:00:00Z"
    }
  }
}
```

#### Supported Event Types
- `BILLING.SUBSCRIPTION.ACTIVATED`: Subscription activated
- `BILLING.SUBSCRIPTION.UPDATED`: Subscription updated
- `BILLING.SUBSCRIPTION.CANCELLED`: Subscription cancelled
- `BILLING.SUBSCRIPTION.EXPIRED`: Subscription expired

#### Success Response (200)
```json
{
  "success": true
}
```

---

## Flow Diagrams

### Subscription Flow
```
1. Frontend → POST /api/paypal/subscriptions
   ↓
2. Backend creates PayPal subscription
   ↓
3. Backend returns approval URL
   ↓
4. User redirected to PayPal for approval
   ↓
5. PayPal redirects to /api/paypal/subscriptions/success
   ↓
6. Backend activates user tier
   ↓
7. User redirected to frontend dashboard
```

### Purchase Flow
```
1. Frontend → POST /api/paypal/purchases
   ↓
2. Backend creates PayPal order
   ↓
3. Backend returns approval URL
   ↓
4. User redirected to PayPal for payment
   ↓
5. PayPal redirects to /api/paypal/purchases/success
   ↓
6. Backend captures payment & upgrades tier
   ↓
7. User redirected to frontend dashboard
```

### Webhook Flow
```
1. PayPal sends webhook to /api/paypal/webhooks
   ↓
2. Backend verifies webhook signature
   ↓
3. Backend processes event (activate/cancel/update)
   ↓
4. Backend updates user tier accordingly
   ↓
5. Backend responds with success
```

---

## Integration Notes

### Frontend Integration
1. **Create Subscription**: Call POST endpoint and redirect user to `approvalUrl`
2. **Handle Callbacks**: Listen for redirects with status parameters
3. **Status Checking**: Periodically check subscription status
4. **Error Handling**: Handle error redirects appropriately

### Environment Variables Required
- `PAYPAL_CLIENT_ID`: PayPal application client ID
- `PAYPAL_CLIENT_SECRET`: PayPal application client secret
- `PAYPAL_SANDBOX`: Set to "true" for sandbox mode
- `PAYPAL_WEBHOOK_ID`: PayPal webhook ID for signature verification
- `APP_URL`: Your application base URL
- `FRONTEND_URL`: Frontend application URL for redirects

### Error Codes
- `400`: Bad request (missing required fields)
- `401`: Unauthorized (invalid API key)
- `404`: Not found (invalid subscription/order ID)
- `500`: Internal server error (PayPal API issues)