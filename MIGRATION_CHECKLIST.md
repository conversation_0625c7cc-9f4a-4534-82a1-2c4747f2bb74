# KV to D1 Migration Checklist

## Pre-Migration Setup ✅

- [x] Created D1 database schema (`schema.sql`)
- [x] Created D1 service classes (`databaseService.js`, `*ServiceD1.js`)
- [x] Created migration script (`migrate-kv-to-d1.js`)
- [x] Created migration routes (`migrationRoutes.js`)
- [x] Updated `wrangler.toml` with D1 configuration
- [x] Created setup script (`setup-d1.sh`)

## Migration Steps

### 1. Database Setup
- [ ] Run `npm run setup-d1` to create D1 database
- [ ] Update `wrangler.toml` with actual database ID
- [ ] Deploy application: `wrangler deploy`

### 2. Test D1 Connection
- [ ] Test D1 connection: `GET /api/migration/test-d1`
- [ ] Check schema: `POST /api/migration/init-schema`
- [ ] Verify tables are created

### 3. Run Migration
- [ ] Check current status: `GET /api/migration/status`
- [ ] Start migration: `POST /api/migration/run`
- [ ] Monitor logs: `wrangler tail`
- [ ] Verify migration completion: `GET /api/migration/status`

### 4. Switch to D1 Services
- [ ] Run switch script: `npm run switch-to-d1`
- [ ] Test all API endpoints
- [ ] Verify data consistency

### 5. Testing Phase
- [ ] Test user registration
- [ ] Test API key validation
- [ ] Test tier management
- [ ] Test email queue processing
- [ ] Test subscription handling
- [ ] Test webhook processing
- [ ] Test portal functionality

### 6. Production Deployment
- [ ] Deploy with D1 services: `wrangler deploy`
- [ ] Monitor application logs
- [ ] Test critical user flows
- [ ] Verify performance metrics

### 7. Cleanup (After Successful Migration)
- [ ] Remove KV configuration from `wrangler.toml`
- [ ] Remove old KV service files (optional)
- [ ] Update documentation

## Rollback Plan

If issues occur during migration:

1. **Immediate Rollback**
   ```bash
   npm run revert-to-kv
   wrangler deploy
   ```

2. **Verify Rollback**
   - Test critical functionality
   - Check KV data integrity
   - Monitor error rates

3. **Investigation**
   - Review migration logs
   - Check D1 data consistency
   - Identify root cause

## Key Endpoints to Test

### User Management
- `POST /api/users/register`
- `POST /api/users/add-domain`
- `GET /api/users/profile`

### API Usage
- `POST /api/usage/track`
- `GET /api/usage/quota`

### Tier Management
- `GET /api/tiers/settings`
- `POST /api/tiers/upgrade`

### Portal
- `POST /api/portal/auth/register`
- `POST /api/portal/auth/login`

### Webhooks
- `POST /api/webhooks/paypal`
- `POST /api/webhooks/xendit`

## Data Validation

After migration, verify:
- [ ] User count matches between KV and D1
- [ ] Domain registrations are preserved
- [ ] API key mappings work correctly
- [ ] Subscription data is intact
- [ ] Email queue items are migrated
- [ ] Tier settings are preserved

## Performance Monitoring

Monitor these metrics post-migration:
- [ ] API response times
- [ ] Database query performance
- [ ] Error rates
- [ ] Memory usage
- [ ] CPU utilization

## Success Criteria

Migration is successful when:
- [ ] All tests pass
- [ ] No data loss detected
- [ ] Performance is maintained or improved
- [ ] All functionality works as expected
- [ ] No critical errors in logs

## Emergency Contacts

- Database Admin: [Your Contact]
- DevOps Team: [Your Contact]
- Product Owner: [Your Contact]

## Notes

- Keep KV configuration during migration for rollback capability
- Monitor Cloudflare dashboard for D1 metrics
- Document any issues encountered for future reference