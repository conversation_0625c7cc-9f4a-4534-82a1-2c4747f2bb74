-- D1 Database Schema for Migration from KV Storage
-- This schema recreates all KV storage patterns as SQL tables

-- Users table - replaces email:{email} and user:{id} patterns
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password TEXT,
    type TEXT DEFAULT 'api', -- 'api' or 'portal'
    status TEXT DEFAULT 'active',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    -- Portal-specific fields
    last_login TEXT,
    is_active INTEGER DEFAULT 1, -- 0 = inactive, 1 = active
    activated_at TEXT,
    google_id TEXT,
    picture TEXT,
    username TEXT, -- Username for portal users
    display_name TEXT -- Display name for portal users
);

-- Domains table - replaces domain:{domain} pattern and user domains array
CREATE TABLE IF NOT EXISTS domains (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    domain TEXT UNIQUE NOT NULL,
    api_key TEXT UNIQUE NOT NULL,
    status TEXT DEFAULT 'active',
    tier TEXT DEFAULT 'free',
    created_at TEXT NOT NULL,
    activated_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- API Keys mapping table - replaces apikey:{apiKey} pattern
CREATE TABLE IF NOT EXISTS api_keys (
    api_key TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    domain_id TEXT NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE
);

-- Portal credentials table - replaces portal_credentials:{userId} pattern
CREATE TABLE IF NOT EXISTS portal_credentials (
    user_id TEXT PRIMARY KEY,
    plain_password TEXT,
    hashed_password TEXT NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Activation tokens table - replaces activation_token:{token} pattern
CREATE TABLE IF NOT EXISTS activation_tokens (
    token TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    created_at TEXT NOT NULL,
    expires_at TEXT,
    used BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Password reset tokens table - replaces used_reset_token:{token} pattern
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    token TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    created_at TEXT NOT NULL,
    expires_at TEXT NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    used_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Subscriptions table - replaces subscription:{subscriptionId} pattern
CREATE TABLE IF NOT EXISTS subscriptions (
    subscription_id TEXT PRIMARY KEY,
    plan_id TEXT,
    product_id TEXT,
    user_id TEXT NOT NULL,
    tier TEXT NOT NULL,
    base_price REAL NOT NULL,
    addons TEXT, -- JSON string for addon configuration
    total_price REAL NOT NULL,
    status TEXT NOT NULL,
    created_at TEXT NOT NULL,
    activated_at TEXT,
    updated_at TEXT NOT NULL,
    next_billing_time TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Payments table - replaces payment:{paymentId} pattern
CREATE TABLE IF NOT EXISTS payments (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    subscription_id TEXT,
    amount_currency TEXT,
    amount_value TEXT,
    time TEXT NOT NULL,
    recorded_at TEXT NOT NULL,
    status TEXT DEFAULT 'completed',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(subscription_id) ON DELETE SET NULL
);

-- Webhook events table - replaces webhook_event:{eventId} pattern
CREATE TABLE IF NOT EXISTS webhook_events (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    event_version TEXT,
    create_time TEXT NOT NULL,
    resource_type TEXT,
    resource_version TEXT,
    summary TEXT,
    resource_data TEXT, -- JSON string for resource data
    received_at TEXT NOT NULL,
    processed BOOLEAN DEFAULT FALSE
);

-- Tier settings table - replaces settings:tiers pattern
CREATE TABLE IF NOT EXISTS tier_settings (
    id INTEGER PRIMARY KEY,
    config TEXT NOT NULL, -- JSON string for tier configuration
    updated_at TEXT NOT NULL,
    version TEXT NOT NULL
);

-- User tiers table - replaces user_tier:{userId} pattern
CREATE TABLE IF NOT EXISTS user_tiers (
    user_id TEXT PRIMARY KEY,
    tier TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Email tiers table - replaces email_tier:{email} pattern
CREATE TABLE IF NOT EXISTS email_tiers (
    email TEXT PRIMARY KEY,
    tier TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- Quota usage table - replaces quota_usage:{userId}:{type} pattern
CREATE TABLE IF NOT EXISTS quota_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    type TEXT NOT NULL, -- 'images', 'content', 'title'
    count INTEGER DEFAULT 0,
    last_reset TEXT,
    reset_date TEXT,
    UNIQUE(user_id, type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Email queue table - replaces email_queue:{id} pattern
CREATE TABLE IF NOT EXISTS email_queue (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    type TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- 'pending', 'sent', 'failed'
    data TEXT NOT NULL, -- JSON string for email data
    attempts INTEGER DEFAULT 0,
    queued_at TEXT NOT NULL,
    process_after TEXT NOT NULL,
    sent_at TEXT,
    error_message TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Usage history table for tracking API usage
CREATE TABLE IF NOT EXISTS usage_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    api_key TEXT NOT NULL,
    domain TEXT NOT NULL,
    endpoint TEXT NOT NULL,
    type TEXT NOT NULL, -- 'images', 'content', 'title'
    timestamp TEXT NOT NULL,
    success BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tier settings table - for individual tier configurations
CREATE TABLE IF NOT EXISTS t_tier_setting (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    max_quota INTEGER NOT NULL,
    images_quota INTEGER NOT NULL,
    content_quota INTEGER NOT NULL,
    title_quota INTEGER NOT NULL,
    domain_limit INTEGER NOT NULL, -- -1 for unlimited
    price REAL NOT NULL,
    expiration_days INTEGER NOT NULL,
    addon1 BOOLEAN DEFAULT FALSE,
    addon2 BOOLEAN DEFAULT FALSE,
    addon1_price REAL NOT NULL,
    addon2_price REAL NOT NULL,
    features TEXT NOT NULL, -- JSON array of features
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_domains_user_id ON domains(user_id);
CREATE INDEX IF NOT EXISTS idx_domains_domain ON domains(domain);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_processed ON webhook_events(processed);
CREATE INDEX IF NOT EXISTS idx_quota_usage_user_type ON quota_usage(user_id, type);
CREATE INDEX IF NOT EXISTS idx_email_queue_status ON email_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_queue_process_after ON email_queue(process_after);
CREATE INDEX IF NOT EXISTS idx_usage_history_user_id ON usage_history(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_history_timestamp ON usage_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_t_tier_setting_id ON t_tier_setting(id);
CREATE INDEX IF NOT EXISTS idx_t_tier_setting_price ON t_tier_setting(price);