#!/bin/bash

# Wrangler D1 User Deletion Script
# This script uses Wrangler CLI to safely delete a user from D1 database

# Configuration
DB_NAME="supersense-db"
EMAIL=""  # Set the email address you want to delete

# Function to execute D1 query
execute_d1_query() {
    local query="$1"
    local description="$2"
    
    echo "🔍 $description"
    echo "📝 Query: $query"
    echo ""
    
    wrangler d1 execute "$DB_NAME" --command "$query"
    echo ""
}

# Function to delete user from D1
delete_user_from_d1() {
    local email="$1"
    
    if [ -z "$email" ]; then
        echo "❌ Error: Email address is required"
        echo "Usage: $0 <<EMAIL>>"
        exit 1
    fi
    
    echo "🗑️ Starting D1 user deletion process for: $email"
    echo "🗄️ Database: $DB_NAME"
    echo ""
    
    # Step 1: Verify user exists
    echo "📋 Step 1: Verifying user exists..."
    execute_d1_query "SELECT id, email, type, status, created_at FROM users WHERE email = '$email';" "Finding user record"
    
    read -p "❓ Did you see the user record above? Continue with deletion? (y/N): " confirm_user
    if [[ ! $confirm_user =~ ^[Yy]$ ]]; then
        echo "❌ Deletion cancelled - user not found or cancelled by user"
        exit 0
    fi
    
    # Step 2: Show what will be deleted
    echo "📋 Step 2: Showing related data that will be deleted..."
    
    execute_d1_query "SELECT COUNT(*) as domain_count FROM domains d JOIN users u ON d.user_id = u.id WHERE u.email = '$email';" "Counting domains"
    
    execute_d1_query "SELECT COUNT(*) as api_key_count FROM api_keys ak JOIN users u ON ak.user_id = u.id WHERE u.email = '$email';" "Counting API keys"
    
    execute_d1_query "SELECT COUNT(*) as subscription_count FROM subscriptions s JOIN users u ON s.user_id = u.id WHERE u.email = '$email';" "Counting subscriptions"
    
    execute_d1_query "SELECT COUNT(*) as usage_count FROM usage_history uh JOIN users u ON uh.user_id = u.id WHERE u.email = '$email';" "Counting usage history"
    
    # Step 3: Final confirmation
    echo "⚠️  WARNING: This will permanently delete the user and ALL associated data!"
    echo "📧 Email: $email"
    echo "🗄️ Database: $DB_NAME"
    echo ""
    echo "The following will be automatically deleted due to CASCADE constraints:"
    echo "  • User record"
    echo "  • All domains owned by the user"
    echo "  • All API keys for the user"
    echo "  • All subscriptions"
    echo "  • All portal credentials"
    echo "  • All user tiers"
    echo "  • All quota usage records"
    echo "  • All usage history"
    echo "  • Email queue entries (set to NULL)"
    echo ""
    
    read -p "🚨 Are you absolutely sure you want to proceed? Type 'DELETE' to confirm: " final_confirm
    if [ "$final_confirm" != "DELETE" ]; then
        echo "❌ Deletion cancelled - confirmation not received"
        exit 0
    fi
    
    # Step 4: Execute deletion
    echo "🗑️ Step 4: Executing user deletion..."
    execute_d1_query "DELETE FROM users WHERE email = '$email';" "Deleting user and all related data"
    
    # Step 5: Clean up email_tiers (not automatically cleaned)
    echo "🧹 Step 5: Cleaning up email tiers..."
    execute_d1_query "DELETE FROM email_tiers WHERE email = '$email';" "Cleaning up email tiers"
    
    # Step 6: Verify deletion
    echo "✅ Step 6: Verifying deletion..."
    execute_d1_query "SELECT COUNT(*) as remaining_users FROM users WHERE email = '$email';" "Checking for remaining user records"
    
    execute_d1_query "SELECT 'orphaned_domains' as table_name, COUNT(*) as count FROM domains WHERE user_id NOT IN (SELECT id FROM users) UNION ALL SELECT 'orphaned_api_keys', COUNT(*) FROM api_keys WHERE user_id NOT IN (SELECT id FROM users);" "Checking for orphaned records"
    
    echo "🎉 User deletion process completed!"
    echo "✅ You can now re-register with the same email address: $email"
}

# Check if email is provided as argument
if [ $# -eq 1 ]; then
    EMAIL="$1"
elif [ -z "$EMAIL" ]; then
    echo "❌ Error: Email address is required"
    echo "Usage: $0 <<EMAIL>>"
    echo "Or edit this script and set the EMAIL variable"
    exit 1
fi

# Validate email format (basic check)
if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
    echo "❌ Error: Invalid email format: $EMAIL"
    exit 1
fi

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "❌ Error: Wrangler CLI is not installed"
    echo "📦 Install it with: npm install -g wrangler"
    exit 1
fi

# Execute deletion
delete_user_from_d1 "$EMAIL"
