# Xendit Payment Gateway API Documentation

## 📋 Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Environment Variables](#environment-variables)
- [Customer Management](#customer-management)
- [Invoice Management](#invoice-management)
- [Payment Methods](#payment-methods)
- [QRIS Payments](#qris-payments)
- [Virtual Account](#virtual-account)
- [Payment Requests](#payment-requests)
- [Account & Balance](#account--balance)
- [Webhooks](#webhooks)
- [Subscriptions](#subscriptions)
- [Error Handling](#error-handling)
- [Testing](#testing)

---

## Overview

This documentation covers the complete Xendit payment gateway integration for the RovoDev API system. Xendit is Indonesia's leading payment gateway that supports various payment methods including QRIS, Virtual Accounts, Bank Transfers, E-wallets, and more.

### Base URL
```
https://your-domain.com/api/xendit
```

### Supported Payment Methods
- **QRIS** - Quick Response Indonesian Standard (QR Code payments)
- **Virtual Account** - Bank transfer via virtual account numbers
- **Bank Transfer** - Direct bank transfers
- **E-wallets** - Dana, OVO, GoPay, LinkAja, etc.
- **Credit/Debit Cards** - Visa, Mastercard, JCB
- **Direct Debit** - Bank account direct debit

---

## Authentication

### API Key Authentication
All Xendit API calls require authentication using your Xendit secret key:

```javascript
Authorization: Basic base64(xendit_secret_key:)
```

### Bearer Token (For Some Endpoints)
Some endpoints require Bearer token authentication:

```javascript
Authorization: Bearer your_jwt_token
```

---

## Environment Variables

Required environment variables for Xendit integration:

```bash
# Xendit Configuration
XENDIT_SECRET_KEY=xnd_development_your_secret_key
XENDIT_WEBHOOK_TOKEN=your_webhook_verification_token
APP_URL=https://your-domain.com

# Optional
XENDIT_PUBLIC_KEY=xnd_public_your_public_key
```

---

## Customer Management

### Create Customer
**POST** `/api/xendit/customer`

Creates a new customer in Xendit system for payment processing.

**Request Body:**
```json
{
  "reference_id": "customer_001",
  "email": "<EMAIL>",
  "given_names": "John",
  "surname": "Doe",
  "mobile_number": "+************",
  "type": "INDIVIDUAL",
  "addresses": [
    {
      "country": "ID",
      "street_line1": "Jl. Sudirman No. 1",
      "city": "Jakarta",
      "postal_code": "12190"
    }
  ],
  "description": "Premium customer",
  "metadata": {
    "source": "website"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cust-*********",
    "reference_id": "customer_001",
    "email": "<EMAIL>",
    "given_names": "John",
    "surname": "Doe",
    "mobile_number": "+************",
    "type": "INDIVIDUAL",
    "created": "2025-01-15T10:30:00.000Z"
  }
}
```

### Get Customers List
**GET** `/api/xendit/customers`

Retrieves a paginated list of customers.

**Query Parameters:**
- `limit` (optional): Number of customers to retrieve (default: 10, max: 100)
- `after_id` (optional): Customer ID for pagination

**Example:**
```bash
GET /api/xendit/customers?limit=20&after_id=cust-*********
```

### Get Customer Details
**GET** `/api/xendit/customers/{id}`

Retrieves details of a specific customer.

**Example:**
```bash
GET /api/xendit/customers/cust-*********
```

### Update Customer
**PATCH** `/api/xendit/customers/{id}`

Updates customer information.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "given_names": "John Updated",
  "mobile_number": "+************"
}
```

---

## Invoice Management

### Create Invoice
**POST** `/api/xendit/invoice`

Creates a Xendit invoice for payment processing.

**Headers:**
```
Authorization: Bearer your_jwt_token
```

**Request Body:**
```json
{
  "external_id": "invoice_001",
  "amount": 150000,
  "payer_email": "<EMAIL>",
  "description": "Upgrade to Pro Plan",
  "currency": "IDR",
  "payment_methods": ["QRIS", "BANK_TRANSFER", "EWALLET"],
  "should_send_email": true,
  "success_redirect_url": "https://your-domain.com/success",
  "failure_redirect_url": "https://your-domain.com/failed"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "inv-*********",
    "external_id": "invoice_001",
    "invoice_url": "https://checkout.xendit.co/web/inv-*********",
    "amount": 150000,
    "status": "PENDING",
    "created": "2025-01-15T10:30:00.000Z",
    "expiry_date": "2025-01-16T10:30:00.000Z"
  }
}
```

### Get Invoices List
**GET** `/api/xendit/invoices`

Retrieves a list of invoices with optional filtering.

**Query Parameters:**
- `limit` (optional): Number of invoices (default: 10)
- `after_id` (optional): Invoice ID for pagination
- `status` (optional): Filter by status (PENDING, PAID, EXPIRED)

**Example:**
```bash
GET /api/xendit/invoices?status=PAID&limit=50
```

### Get Invoice Details
**GET** `/api/xendit/invoices/{id}`

Retrieves details of a specific invoice.

### Update Invoice
**PATCH** `/api/xendit/invoices/{id}`

Updates invoice information (only for PENDING invoices).

**Request Body:**
```json
{
  "description": "Updated description",
  "amount": 200000
}
```

### Expire Invoice
**POST** `/api/xendit/invoices/{id}/expire`

Manually expires a pending invoice.

---

## Payment Methods

### QRIS Payments

#### Create QRIS Payment
**POST** `/api/xendit/qris`

Creates a QRIS (QR Code) payment.

**Request Body:**
```json
{
  "external_id": "qris_payment_001",
  "amount": 50000,
  "description": "Payment for order #123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "qr_*********",
    "reference_id": "qris_payment_001",
    "type": "DYNAMIC",
    "currency": "IDR",
    "amount": 50000,
    "qr_string": "00020101021226670016ID.CO.QRIS.WWW...",
    "status": "ACTIVE",
    "created": "2025-01-15T10:30:00.000Z",
    "expires_at": "2025-01-15T11:30:00.000Z"
  }
}
```

#### Generate QR Code Image
**POST** `/api/xendit/qris/qr-code`

Generates a QR code image from QR string.

**Request Body:**
```json
{
  "qr_string": "00020101021226670016ID.CO.QRIS.WWW..."
}
```

**Response:** PNG image binary data

### Virtual Account

#### Create Virtual Account
**POST** `/api/xendit/virtual-account`

Creates a virtual account for bank transfer payments.

**Request Body:**
```json
{
  "external_id": "va_payment_001",
  "bank_code": "BCA",
  "name": "John Doe",
  "amount": 100000,
  "is_closed": true,
  "description": "Payment for subscription",
  "expiration_date": "2025-01-16T23:59:59.000Z"
}
```

**Supported Bank Codes:**
- `BCA` - Bank Central Asia (min: 50,000 IDR)
- `BNI` - Bank Negara Indonesia
- `BRI` - Bank Rakyat Indonesia
- `MANDIRI` - Bank Mandiri
- `PERMATA` - Bank Permata
- `BSI` - Bank Syariah Indonesia
- `CIMB` - CIMB Niaga
- `SAHABAT_SAMPOERNA` - Bank Sahabat Sampoerna

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "va_*********",
    "external_id": "va_payment_001",
    "bank_code": "BCA",
    "merchant_code": "88888",
    "name": "John Doe",
    "account_number": "88888*********0",
    "amount": 100000,
    "is_closed": true,
    "status": "PENDING",
    "expiration_date": "2025-01-16T23:59:59.000Z"
  }
}
```

### Payment Requests

#### Create Payment Request
**POST** `/api/xendit/payment-request`

Creates a payment request with specific payment method.

**Request Body:**
```json
{
  "amount": 75000,
  "currency": "IDR",
  "payment_method": {
    "type": "QRIS",
    "reusability": "ONE_TIME_USE"
  },
  "customer_id": "cust-*********",
  "description": "Payment for premium features",
  "reference_id": "payment_req_001",
  "metadata": {
    "order_id": "ORD-001"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "pr_*********",
    "reference_id": "payment_req_001",
    "amount": 75000,
    "currency": "IDR",
    "status": "PENDING",
    "payment_method": {
      "id": "pm_*********",
      "type": "QRIS",
      "qr_string": "00020101021226670016ID.CO.QRIS.WWW..."
    },
    "created": "2025-01-15T10:30:00.000Z"
  }
}
```

---

## Account & Balance

### Get Account Balance
**GET** `/api/xendit/balance`

Retrieves current account balance.

**Response:**
```json
{
  "success": true,
  "data": {
    "balance": 5000000,
    "currency": "IDR",
    "last_updated": "2025-01-15T10:30:00.000Z"
  }
}
```

### Get Account Information
**GET** `/api/xendit/account`

Retrieves account information and settings.

**Response:**
```json
{
  "success": true,
  "data": {
    "business_id": "business_*********",
    "business_name": "RovoDev Company",
    "country": "ID",
    "status": "LIVE",
    "created": "2024-01-01T00:00:00.000Z"
  }
}
```

---

## Webhooks

### Webhook Configuration

Configure webhooks in your Xendit dashboard:
- **URL:** `https://your-domain.com/api/xendit/webhook`
- **Events:** Payment status changes, invoice updates
- **Authentication:** Callback token verification

### Webhook Endpoints

#### General Webhook Handler
**POST** `/api/xendit/webhook`

Handles all Xendit webhook notifications.

#### Payment-Specific Webhook
**POST** `/api/xendit/payment-webhook`

Dedicated endpoint for payment notifications.

### Webhook Payload Example

```json
{
  "id": "payment_*********",
  "external_id": "invoice_001",
  "amount": 150000,
  "status": "PAID",
  "paid_amount": 150000,
  "paid_at": "2025-01-15T10:35:00.000Z",
  "payer_email": "<EMAIL>",
  "description": "Upgrade to Pro Plan",
  "payment_method": "QR_CODE",
  "payment_channel": "QRIS",
  "payment_details": {
    "source": "DANA",
    "receipt_id": "TXN*********"
  },
  "created": "2025-01-15T10:30:00.000Z",
  "updated": "2025-01-15T10:35:00.000Z"
}
```

### Webhook Security

Verify webhook authenticity using the `x-callback-token` header:

```javascript
const callbackToken = request.headers.get("x-callback-token");
if (callbackToken !== process.env.XENDIT_WEBHOOK_TOKEN) {
  return new Response("Unauthorized", { status: 401 });
}
```

---

## Subscriptions

### Create Xendit Subscription
**POST** `/api/xendit/subscriptions`

Creates a subscription using Xendit payment gateway.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "tier": "pro",
  "addons": {
    "addon1": false,
    "addon2": true
  }
}
```

### Subscription Success Callback
**GET** `/api/xendit/subscriptions/success`

Handles successful subscription payment completion.

**Query Parameters:**
- `ref`: Reference ID for the subscription

### Subscription Webhooks
**POST** `/api/xendit/webhooks`

Handles subscription-related webhook notifications.

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "error": "external_id and amount are required"
}
```

#### 401 Unauthorized
```json
{
  "success": false,
  "error": "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
}
```

#### 404 Not Found
```json
{
  "success": false,
  "error": "Invoice not found"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Failed to create payment",
  "details": "Detailed error message from Xendit API"
}
```

### Error Codes from Xendit

| Code | Description |
|------|-------------|
| `INVALID_API_KEY` | API key is invalid or expired |
| `DUPLICATE_EXTERNAL_ID` | External ID already exists |
| `INVALID_AMOUNT` | Amount is invalid or below minimum |
| `UNSUPPORTED_BANK` | Bank code not supported |
| `EXPIRED_INVOICE` | Invoice has expired |
| `INSUFFICIENT_BALANCE` | Account balance insufficient |

---

## Testing

### Test Environment

Use Xendit's test environment for development:

```bash
XENDIT_SECRET_KEY=xnd_development_your_test_key
```

### Test Payment Methods

#### Test QRIS Payment
```bash
curl -X POST "https://your-domain.com/api/xendit/qris" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "test_qris_001",
    "amount": 10000,
    "description": "Test QRIS payment"
  }'
```

#### Test Virtual Account
```bash
curl -X POST "https://your-domain.com/api/xendit/virtual-account" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "test_va_001",
    "bank_code": "BCA",
    "name": "Test User",
    "amount": 50000
  }'
```

#### Test Invoice Creation
```bash
curl -X POST "https://your-domain.com/api/xendit/invoice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_test_token" \
  -d '{
    "external_id": "test_invoice_001",
    "amount": 100000,
    "payer_email": "<EMAIL>",
    "description": "Test invoice"
  }'
```

### Test Webhook
```bash
curl -X POST "https://your-domain.com/api/xendit/webhook" \
  -H "Content-Type: application/json" \
  -H "x-callback-token: your_test_webhook_token" \
  -d '{
    "id": "test_payment_001",
    "external_id": "test_invoice_001",
    "status": "PAID",
    "amount": 100000,
    "paid_amount": 100000,
    "payer_email": "<EMAIL>"
  }'
```

---

## Payment Flow Examples

### Complete QRIS Payment Flow

1. **Create Customer** (optional)
```javascript
const customer = await fetch('/api/xendit/customer', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    reference_id: 'customer_001',
    email: '<EMAIL>',
    given_names: 'John Doe'
  })
});
```

2. **Create QRIS Payment**
```javascript
const qrisPayment = await fetch('/api/xendit/qris', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    external_id: 'payment_001',
    amount: 50000,
    description: 'Product purchase'
  })
});
```

3. **Generate QR Code Image**
```javascript
const qrImage = await fetch('/api/xendit/qris/qr-code', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    qr_string: qrisPayment.data.qr_string
  })
});
```

4. **Check Payment Status**
```javascript
const status = await fetch(`/api/xendit/payment-status?invoice_id=${paymentId}`);
```

### Complete Invoice Payment Flow

1. **Create Invoice**
```javascript
const invoice = await fetch('/api/xendit/invoice', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_token'
  },
  body: JSON.stringify({
    external_id: 'invoice_001',
    amount: 150000,
    payer_email: '<EMAIL>',
    description: 'Subscription upgrade'
  })
});
```

2. **Redirect to Payment Page**
```javascript
window.location.href = invoice.data.invoice_url;
```

3. **Handle Webhook Notification**
```javascript
// Webhook will automatically process payment completion
// and upgrade user tier based on payment amount/description
```

---

## Best Practices

### Security
1. **Always validate webhook tokens**
2. **Use HTTPS for all endpoints**
3. **Store API keys securely**
4. **Implement rate limiting**
5. **Log all payment transactions**

### Performance
1. **Cache customer data when possible**
2. **Use pagination for large datasets**
3. **Implement proper error handling**
4. **Monitor API response times**

### User Experience
1. **Provide clear payment instructions**
2. **Show payment status in real-time**
3. **Handle payment failures gracefully**
4. **Send confirmation emails**

### Monitoring
1. **Track payment success rates**
2. **Monitor webhook delivery**
3. **Set up alerts for failed payments**
4. **Regular balance checks**

---

## Support & Resources

### Xendit Documentation
- [Official API Documentation](https://developers.xendit.co/)
- [Payment Methods Guide](https://developers.xendit.co/api-reference/)
- [Webhook Documentation](https://developers.xendit.co/api-reference/#webhooks)

### RovoDev Support
- **Email:** <EMAIL>
- **Documentation:** This file
- **GitHub Issues:** Create an issue for bugs or feature requests

### Emergency Contacts
- **Xendit Support:** <EMAIL>
- **Technical Issues:** Check Xendit status page
- **Payment Disputes:** Contact Xendit customer service

---

*Last Updated: January 2025*
*Version: 1.0.0*