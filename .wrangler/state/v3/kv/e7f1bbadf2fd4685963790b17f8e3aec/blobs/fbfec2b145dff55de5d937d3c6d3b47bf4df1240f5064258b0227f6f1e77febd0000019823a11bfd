WEBHOOK TEST LOG - 2025-07-19T17:00:17.784Z
Payload: {
  "id": "687bcf1ba59bfcd85c7476d0",
  "external_id": "sub_1752944411450_6f4d2j7m7",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T17:00:14.860Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752944411450_6f4d2j7m7",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "created": "2025-07-19T17:00:11.949Z",
  "updated": "2025-07-19T17:00:16.514Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_c08d548c-8249-445d-a5a1-996057709689",
  "payment_method_id": "pm-e184d266-5d41-4154-b1a6-49ec75ad65c5",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}