{"id": "portal_email_queue:1752396291610_ejbeqn2dq", "email": "<EMAIL>", "username": "<EMAIL>", "type": "portal_welcome", "status": "retry_scheduled", "createdAt": "2025-07-13T08:44:51.610Z", "attempts": 1, "retryAt": "2025-07-13T08:44:54.566Z", "lastError": "Portal Email API returned 401: {\"error\":{\"code\":\"TM_4001\",\"details\":[{\"code\":\"SERR_157\",\"message\":\"Invalid API Token found\"}],\"message\":\"Access Denied\"}}\n\n"}