WEBHOOK TEST LOG - 2025-07-19T18:41:46.316Z
Payload: {
  "id": "687be6dfa59bfcd85c74917e",
  "external_id": "sub_1752950495302_ybcbx155n",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T18:41:43.204Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:41:35.986Z",
  "updated": "2025-07-19T18:41:45.052Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_8be3eb11-c199-4faf-908b-00100a00895d",
  "payment_method_id": "pm-91182f8d-bea6-4942-8c10-85d3c7075ce1",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}