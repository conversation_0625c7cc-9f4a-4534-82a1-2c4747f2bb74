WEBHOOK TEST LOG - 2025-07-19T17:36:59.498Z
Payload: {
  "id": "687bd7b5a59bfcd85c748173",
  "external_id": "sub_1752946612921_32i9gbzrp",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T17:36:56.292Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T17:36:53.604Z",
  "updated": "2025-07-19T17:36:58.245Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_d6921689-965d-40f9-9a35-d7e27a29a183",
  "payment_method_id": "pm-fa334cac-00b8-49ea-b2c2-7e35d2d20694",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}