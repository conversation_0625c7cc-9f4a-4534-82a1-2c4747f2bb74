WEBHOOK TEST LOG - 2025-07-19T15:42:57.800Z
Payload: {
  "id": "687bbceda59bfcd85c7460ab",
  "external_id": "sub_1752939757037_zgity5ig6",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T15:42:54.820Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752939757037_zgity5ig6",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "created": "2025-07-19T15:42:38.065Z",
  "updated": "2025-07-19T15:42:56.512Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_2022f9bc-556b-47e4-8635-aefe8ff90352",
  "payment_method_id": "pm-3288197c-e8a4-4400-aa38-c70a0e76af50",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}