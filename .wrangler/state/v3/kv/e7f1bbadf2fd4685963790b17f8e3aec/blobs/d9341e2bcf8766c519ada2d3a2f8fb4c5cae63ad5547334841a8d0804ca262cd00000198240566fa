WEBHOOK TEST LOG - 2025-07-19T18:49:50.582Z
Payload: {
  "id": "687be8c5a59bfcd85c749378",
  "external_id": "sub_1752950981447_qv8u0ish2",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T18:49:46.898Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:49:42.197Z",
  "updated": "2025-07-19T18:49:48.844Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_6cb02e41-8570-4d45-b247-5624034d5183",
  "payment_method_id": "pm-9da1d7e8-58cd-40dd-8a7d-0f9c3a9f4b36",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}