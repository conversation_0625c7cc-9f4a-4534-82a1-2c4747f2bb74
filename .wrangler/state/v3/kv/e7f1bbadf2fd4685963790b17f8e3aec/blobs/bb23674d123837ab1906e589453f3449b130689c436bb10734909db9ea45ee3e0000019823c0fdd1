WEBHOOK TEST LOG - 2025-07-19T17:35:07.211Z
Payload: {
  "id": "687bd744a59bfcd85c748112",
  "external_id": "sub_1752946500578_gyvmjd13x",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T17:35:04.522Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/xendit/callback?status=success",
  "failure_redirect_url": "http://localhost:3000/api/xendit/callback?status=cancel",
  "created": "2025-07-19T17:35:01.348Z",
  "updated": "2025-07-19T17:35:06.535Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_04a02f0e-c0f5-40e8-ae03-523d9d25bfc4",
  "payment_method_id": "pm-4b4c2d27-dd7f-46ab-be66-16c3814f9d0a",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}