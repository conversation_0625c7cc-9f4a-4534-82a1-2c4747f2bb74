WEBHOOK TEST LOG - 2025-07-19T18:46:28.140Z
Payload: {
  "id": "687be7fda59bfcd85c7492c1",
  "external_id": "sub_1752950781010_6n7673ner",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 1485000,
  "paid_amount": 1485000,
  "paid_at": "2025-07-19T18:46:25.019Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for enterprise tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:46:21.888Z",
  "updated": "2025-07-19T18:46:26.911Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_77d18546-ab23-4775-a96e-75b30136173f",
  "payment_method_id": "pm-b1590863-6e0f-4bed-90df-d27fd660772f",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}