WEBHOOK TEST LOG - 2025-07-19T16:37:49.661Z
Payload: {
  "id": "687bbafba59bfcd85c745e88",
  "amount": 225000,
  "status": "PAID",
  "created": "2025-07-19T15:34:19.614Z",
  "is_high": false,
  "paid_at": "2025-07-19T15:34:23.098Z",
  "updated": "2025-07-19T15:34:24.881Z",
  "user_id": "679e3e04efb310294787b54c",
  "currency": "IDR",
  "payment_id": "qrpy_2386d723-e212-4d1c-8c73-7fb66dcf1cab",
  "description": "Subscription for basic tier",
  "external_id": "sub_1752939258652_r9ie02lf3",
  "paid_amount": 225000,
  "payer_email": "<EMAIL>",
  "merchant_name": "<PERSON><PERSON>",
  "payment_method": "QR_CODE",
  "payment_channel": "QRIS",
  "payment_details": {
    "source": "DANA",
    "receipt_id": ""
  },
  "payment_method_id": "pm-0b35de6e-72ae-4d1a-b705-9b340ebd67f0",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752939258652_r9ie02lf3"
}