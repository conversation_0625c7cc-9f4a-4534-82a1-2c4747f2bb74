WEBHOOK TEST LOG - 2025-07-19T16:24:03.362Z
Payload: {
  "id": "687bc69ca59bfcd85c746d52",
  "external_id": "sub_1752942235812_e8wgh4qii",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T16:24:00.415Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752942235812_e8wgh4qii",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "created": "2025-07-19T16:23:56.552Z",
  "updated": "2025-07-19T16:24:02.169Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_010a5444-d576-46c6-913c-f0a5c0941553",
  "payment_method_id": "pm-c3daa101-d81d-4f25-af43-f74e39719fba",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}