WEBHOOK TEST LOG - 2025-07-19T18:55:30.299Z
Payload: {
  "id": "687bea18a59bfcd85c7494bf",
  "external_id": "sub_1752951320249_iyx7y1uod",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 1485000,
  "paid_amount": 1485000,
  "paid_at": "2025-07-19T18:55:27.039Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for enterprise tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:55:20.900Z",
  "updated": "2025-07-19T18:55:29.058Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_0a52803a-f9d4-44e0-aba1-36693dfa0ad4",
  "payment_method_id": "pm-b820659b-9c4d-46d5-9c54-cd73f5bfeeec",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}