WEBHOOK TEST LOG - 2025-07-19T17:03:53.072Z
Payload: {
  "id": "687bcff3a59bfcd85c747834",
  "external_id": "sub_1752944626917_64ypw4u4i",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T17:03:50.506Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752944626917_64ypw4u4i",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "created": "2025-07-19T17:03:47.607Z",
  "updated": "2025-07-19T17:03:52.328Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_657f13ab-a8dd-402d-9fd3-e4c95fee20de",
  "payment_method_id": "pm-25d86446-f58e-4632-9f67-4f0f08acaf99",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}