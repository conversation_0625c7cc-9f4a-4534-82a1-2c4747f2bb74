WEBHOOK TEST LOG - 2025-07-19T18:29:17.597Z
Payload: {
  "id": "687be395a59bfcd85c748dea",
  "external_id": "sub_1752949653455_0pqxr8c4v",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T18:29:14.546Z",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:27:34.173Z",
  "updated": "2025-07-19T18:29:16.356Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_65098a37-cf0c-48ec-bedf-1f2e76d829ae",
  "payment_method_id": "pm-fa7e65c7-c0ec-4ecc-b440-c9894e2ce03d",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}