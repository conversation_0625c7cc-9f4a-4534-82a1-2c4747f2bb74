WEBHOOK TEST LOG - 2025-07-19T16:52:59.298Z
Payload: {
  "id": "687bcd64a59bfcd85c74748a",
  "external_id": "sub_1752943971802_vgcu1voog",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T16:52:56.288Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752943971802_vgcu1voog",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "created": "2025-07-19T16:52:52.461Z",
  "updated": "2025-07-19T16:52:58.056Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_1c0aa7d6-44a4-4a48-b235-a2a723fd5a52",
  "payment_method_id": "pm-aa2653ba-b5eb-4b06-941c-8fb17fd19649",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}