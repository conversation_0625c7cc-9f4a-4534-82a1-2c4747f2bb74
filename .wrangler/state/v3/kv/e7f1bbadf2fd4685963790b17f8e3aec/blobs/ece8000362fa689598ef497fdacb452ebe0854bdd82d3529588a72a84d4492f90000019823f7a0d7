WEBHOOK TEST LOG - 2025-07-19T18:34:47.892Z
Payload: {
  "id": "687be53ea59bfcd85c748fe9",
  "external_id": "sub_1752950078502_16bxc19gv",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T18:34:44.697Z",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:34:39.281Z",
  "updated": "2025-07-19T18:34:46.636Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_9c359117-0898-4d07-9c12-082dbd3ae966",
  "payment_method_id": "pm-037e533b-f74e-42c7-ac0a-390f1057e384",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}