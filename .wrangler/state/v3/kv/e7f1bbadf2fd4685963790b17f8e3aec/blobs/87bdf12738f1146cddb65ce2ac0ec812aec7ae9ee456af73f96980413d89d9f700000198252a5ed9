WEBHOOK TEST LOG - 2025-07-20T00:09:50.553Z
Payload: {
  "id": "687c33c6a59bfcd85c74f7f1",
  "external_id": "sub_1752970181985_11jrzrbd2",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 1485000,
  "paid_amount": 1485000,
  "paid_at": "2025-07-20T00:09:47.459Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for enterprise tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-20T00:09:42.873Z",
  "updated": "2025-07-20T00:09:49.322Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_7369c626-c57f-4629-ad3e-43c635c5ec9b",
  "payment_method_id": "pm-7534a43e-96a9-493c-8b56-805f22a27bc7",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}