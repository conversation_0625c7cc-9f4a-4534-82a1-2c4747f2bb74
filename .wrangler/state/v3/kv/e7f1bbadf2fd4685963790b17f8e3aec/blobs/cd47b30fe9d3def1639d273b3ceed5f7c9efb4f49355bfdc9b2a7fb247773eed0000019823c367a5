WEBHOOK TEST LOG - 2025-07-19T17:37:45.378Z
Payload: {
  "id": "687bd7e1a59bfcd85c7481c5",
  "external_id": "sub_1752946657047_eoazqzjms",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 600000,
  "paid_amount": 600000,
  "paid_at": "2025-07-19T17:37:42.403Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for pro tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T17:37:37.581Z",
  "updated": "2025-07-19T17:37:44.099Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_53c869d3-67f8-4fb2-bfa1-b2be87fb92d4",
  "payment_method_id": "pm-59b28682-3784-490e-9484-16a5adf1ca2c",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}