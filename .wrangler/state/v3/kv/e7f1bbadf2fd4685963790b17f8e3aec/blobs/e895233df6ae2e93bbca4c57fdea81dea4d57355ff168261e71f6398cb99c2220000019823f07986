WEBHOOK TEST LOG - 2025-07-19T18:26:59.074Z
Payload: {
  "id": "687be36aa59bfcd85c748d99",
  "external_id": "sub_1752949610043_9oit5azdp",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T18:26:55.854Z",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3001/api/subscriptions/xendit/success",
  "failure_redirect_url": "http://localhost:3001/api/xendit/callback?status=cancel",
  "created": "2025-07-19T18:26:50.644Z",
  "updated": "2025-07-19T18:26:57.737Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_5fbc8dd5-c038-4031-a8ac-16526ef19a46",
  "payment_method_id": "pm-7501ab21-b92e-4581-904c-ade3ed3c0862",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}