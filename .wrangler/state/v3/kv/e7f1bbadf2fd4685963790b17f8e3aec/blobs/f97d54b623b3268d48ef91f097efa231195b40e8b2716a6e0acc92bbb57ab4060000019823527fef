WEBHOOK TEST LOG - 2025-07-19T15:34:26.027Z
Payload: {
  "id": "687bbafba59bfcd85c745e88",
  "external_id": "sub_1752939258652_r9ie02lf3",
  "user_id": "679e3e04efb310294787b54c",
  "payment_method": "QR_CODE",
  "status": "PAID",
  "merchant_name": "<PERSON><PERSON>",
  "amount": 225000,
  "paid_amount": 225000,
  "paid_at": "2025-07-19T15:34:23.098Z",
  "payer_email": "<EMAIL>",
  "description": "Subscription for basic tier",
  "is_high": false,
  "success_redirect_url": "http://localhost:3000/api/subscriptions/xendit/success?ref=sub_1752939258652_r9ie02lf3",
  "failure_redirect_url": "http://localhost:3000/dashboard?subscription_status=failed",
  "created": "2025-07-19T15:34:19.614Z",
  "updated": "2025-07-19T15:34:24.881Z",
  "currency": "IDR",
  "payment_channel": "QRIS",
  "payment_id": "qrpy_2386d723-e212-4d1c-8c73-7fb66dcf1cab",
  "payment_method_id": "pm-0b35de6e-72ae-4d1a-b705-9b340ebd67f0",
  "payment_details": {
    "receipt_id": "",
    "source": "DANA"
  }
}