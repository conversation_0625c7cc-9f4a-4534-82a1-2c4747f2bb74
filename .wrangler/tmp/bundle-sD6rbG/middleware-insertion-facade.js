				import worker, * as OTHER_EXPORTS from "/Users/<USER>/Docker/lemp/html/backend_awp/src/index.js";
				import * as __MIDDLEWARE_0__ from "/Users/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts";
import * as __MIDDLEWARE_1__ from "/Users/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/middleware-scheduled.ts";
import * as __MIDDLEWARE_2__ from "/Users/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts";

				export * from "/Users/<USER>/Docker/lemp/html/backend_awp/src/index.js";

				export const __INTERNAL_WRANGLER_MIDDLEWARE__ = [
					
					__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default,__MIDDLEWARE_2__.default
				]
				export default worker;