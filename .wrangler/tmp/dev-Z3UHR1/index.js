var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};

// .wrangler/tmp/bundle-GusSqL/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// node_modules/itty-router/dist/itty-router.mjs
var e = /* @__PURE__ */ __name(({ base: e2 = "", routes: r = [] } = {}) => ({ __proto__: new Proxy({}, { get: (a, o, t) => (a2, ...p) => r.push([o.toUpperCase(), RegExp(`^${(e2 + a2).replace(/(\/?)\*/g, "($1.*)?").replace(/(\/$)|((?<=\/)\/)/, "").replace(/(:(\w+)\+)/, "(?<$2>.*)").replace(/:(\w+)(\?)?(\.)?/g, "$2(?<$1>[^/]+)$2$3").replace(/\.(?=[\w(])/, "\\.").replace(/\)\.\?\(([^\[]+)\[\^/g, "?)\\.?($1(?<=\\.)[^\\.")}/*$`), p]) && t }), routes: r, async handle(e3, ...a) {
  let o, t, p = new URL(e3.url), l = e3.query = {};
  for (let [e4, r2] of p.searchParams)
    l[e4] = void 0 === l[e4] ? r2 : [l[e4], r2].flat();
  for (let [l2, s, c] of r)
    if ((l2 === e3.method || "ALL" === l2) && (t = p.pathname.match(s))) {
      e3.params = t.groups || {};
      for (let r2 of c)
        if (void 0 !== (o = await r2(e3.proxy || e3, ...a)))
          return o;
    }
} }), "e");

// src/services/responseService.js
var ResponseService = class {
  formatSuccess(data, message = null) {
    return {
      success: true,
      data,
      message,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  formatError(message, statusCode = 500) {
    return {
      success: false,
      message,
      statusCode,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
};
__name(ResponseService, "ResponseService");

// src/services/userService.js
var UserService = class {
  constructor(env) {
    this.env = env;
  }
  async getUserDetail(apiKey) {
    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return this._formatUserData(user);
  }
  async createUser(userData) {
    const existingUser = await this.env.USERS_KV.get(`email:${userData.email}`, "json");
    if (existingUser) {
      throw new Error("Email already exists");
    }
    const existingUsername = await this.env.USERS_KV.get(`username:${userData.username}`, "json");
    if (existingUsername) {
      throw new Error("Username already exists");
    }
    const apiKey = crypto.randomUUID();
    const user = {
      id: crypto.randomUUID(),
      username: userData.username,
      email: userData.email,
      password: await this._hashPassword(userData.password),
      api_key: apiKey,
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    await Promise.all([
      this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`username:${user.username}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`api_key:${apiKey}`, JSON.stringify(user))
    ]);
    return this._formatUserData(user);
  }
  async _hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hash = await crypto.subtle.digest("SHA-256", data);
    return Array.from(new Uint8Array(hash)).map((b) => b.toString(16).padStart(2, "0")).join("");
  }
  _formatUserData(user) {
    const { password, ...userData } = user;
    return userData;
  }
};
__name(UserService, "UserService");

// src/services/apiKeyService.js
var ApiKeyService = class {
  async validateApiKey(request, env) {
    const apiKey = request.headers.get("x-api-key");
    if (!apiKey) {
      throw new Error("API Key is required in X-API-KEY header");
    }
    const user = await env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return user;
  }
};
__name(ApiKeyService, "ApiKeyService");

// src/services/validationService.js
var ValidationService = class {
  validateUserData(userData) {
    const errors = [];
    if (!userData.username)
      errors.push("Username is required");
    if (!userData.email)
      errors.push("Email is required");
    if (!userData.password)
      errors.push("Password is required");
    if (userData.username && !this._isValidUsername(userData.username)) {
      errors.push("Username must be at least 3 characters long and contain only letters, numbers, and underscores");
    }
    if (userData.email && !this._isValidEmail(userData.email)) {
      errors.push("Invalid email format");
    }
    if (userData.password && !this._isValidPassword(userData.password)) {
      errors.push("Password must be at least 6 characters long");
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  _isValidUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]{3,}$/;
    return usernameRegex.test(username);
  }
  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  _isValidPassword(password) {
    return password.length >= 6;
  }
};
__name(ValidationService, "ValidationService");

// src/controllers/userController.js
var UserController = class {
  constructor(env) {
    this.env = env;
    this.userService = new UserService(env);
    this.apiKeyService = new ApiKeyService();
    this.responseService = new ResponseService();
    this.validationService = new ValidationService();
  }
  async validateApiKey(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(this.responseService.formatSuccess({
            isValid: false,
            message: "API Key is required in X-API-KEY header"
          })),
          { headers: { "Content-Type": "application/json" } }
        );
      }
      try {
        await this.apiKeyService.validateApiKey(request, this.env);
        return new Response(
          JSON.stringify(this.responseService.formatSuccess({
            isValid: true
          })),
          { headers: { "Content-Type": "application/json" } }
        );
      } catch (error) {
        return new Response(
          JSON.stringify(this.responseService.formatSuccess({
            isValid: false,
            message: error.message
          })),
          { headers: { "Content-Type": "application/json" } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError("Server error occurred")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async getUserDetail(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(this.responseService.formatError("API Key is required")),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.getUserDetail(apiKey);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async createUser(request) {
    try {
      const userData = await request.json();
      const { username, email, password } = userData;
      const validation = this.validationService.validateUserData(userData);
      if (!validation.isValid) {
        return new Response(
          JSON.stringify(this.responseService.formatError(validation.errors.join(", "))),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.createUser({ username, email, password });
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user, "User created successfully")),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }
};
__name(UserController, "UserController");

// src/controllers/debugController.js
var DebugController = class {
  constructor(env) {
    this.env = env;
  }
  async listAllKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value
          };
        })
      );
      const groupedData = data.reduce((acc, item) => {
        const prefix = item.key.split(":")[0];
        if (!acc[prefix]) {
          acc[prefix] = [];
        }
        acc[prefix].push(item);
        return acc;
      }, {});
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            summary: {
              totalKeys: list.keys.length,
              prefixes: Object.keys(groupedData),
              countByPrefix: Object.fromEntries(
                Object.entries(groupedData).map(([k, v]) => [k, v.length])
              )
            },
            grouped: groupedData,
            raw: data
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async deleteKVData(request) {
    try {
      const { key } = await request.json();
      if (!key) {
        throw new Error("Key is required");
      }
      await this.env.USERS_KV.delete(key);
      return new Response(
        JSON.stringify({
          success: true,
          message: `Key ${key} deleted successfully`
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(DebugController, "DebugController");

// src/controllers/apiUsageController.js
var ApiUsageController = class {
  constructor(env) {
    this.env = env;
  }
  async trackUsage(request) {
    try {
      const data = await request.json();
      const { apiKey, type, source, timestamp } = data;
      if (!apiKey || !type || !source || !timestamp) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Missing required fields: apiKey, type, source, timestamp"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const validTypes = ["images", "contents"];
      if (!validTypes.includes(type)) {
        return new Response(
          JSON.stringify({
            success: false,
            message: 'Invalid type. Must be either "images" or "contents"'
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const usageId = crypto.randomUUID();
      const usageData = {
        id: usageId,
        apiKey,
        type,
        source,
        timestamp,
        createdAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      await this.env.USERS_KV.put(
        `d_api_usage:${usageId}`,
        JSON.stringify(usageData)
      );
      await this.env.USERS_KV.put(
        `d_api_usage:${apiKey}:${usageId}`,
        JSON.stringify(usageData)
      );
      return new Response(
        JSON.stringify({
          success: true,
          message: "API usage tracked successfully",
          data: usageData
        }),
        {
          status: 201,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUserUsage(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API Key is required in X-API-KEY header"
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const { keys } = await this.env.USERS_KV.list({
        prefix: `d_api_usage:${apiKey}:`
      });
      const usageData = await Promise.all(
        keys.map(async (key) => {
          const data = await this.env.USERS_KV.get(key.name, "json");
          return data;
        })
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: usageData
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(ApiUsageController, "ApiUsageController");

// src/routes/userRoutes.js
function createUserRouter(env) {
  const router2 = e({ base: "/api/users" });
  const userController = new UserController(env);
  const debugController = new DebugController(env);
  const apiUsageController = new ApiUsageController(env);
  router2.post(
    "/validatekey",
    (request) => userController.validateApiKey(request)
  );
  router2.get("/userdetail", (request) => userController.getUserDetail(request));
  router2.post("/", (request) => userController.createUser(request));
  router2.post(
    "/usage/track",
    (request) => apiUsageController.trackUsage(request)
  );
  router2.get("/usage", (request) => apiUsageController.getUserUsage(request));
  router2.get("/debug/kv", (request) => debugController.listAllKVData(request));
  router2.post(
    "/debug/kv/delete",
    (request) => debugController.deleteKVData(request)
  );
  return router2;
}
__name(createUserRouter, "createUserRouter");

// src/services/tierService.js
var _TierService = class {
  constructor(env) {
    this.env = env;
  }
  async initializeTierSettings() {
    const existingConfig = await this.getTierSettings();
    if (!existingConfig) {
      await this.env.USERS_KV.put(
        `${_TierService.KEYS.SETTINGS}:tiers`,
        JSON.stringify({
          config: _TierService.DEFAULT_TIER_CONFIG,
          updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
          version: "1.0"
        })
      );
    }
    return await this.getTierSettings();
  }
  async getTierSettings() {
    return await this.env.USERS_KV.get(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      "json"
    );
  }
  async updateTierSettings(tierConfig) {
    const settings = {
      config: tierConfig,
      updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
      version: "1.0"
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify(settings)
    );
    return settings;
  }
  async getUserTier(userId) {
    try {
      const userTier = await this.env.USERS_KV.get(
        `${_TierService.KEYS.USER_TIER}:${userId}`,
        "json"
      );
      return userTier || {
        tier: _TierService.TIERS.FREE,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      console.error("Error getting user tier:", error);
      throw error;
    }
  }
  async setUserTier(userId, tier) {
    if (!Object.values(_TierService.TIERS).includes(tier)) {
      throw new Error("Invalid tier specified");
    }
    const tierData = {
      tier,
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.USER_TIER}:${userId}`,
      JSON.stringify(tierData)
    );
    await this.resetQuotaUsage(userId);
    return tierData;
  }
  async getUserQuotaUsage(userId) {
    const usage = await this.env.USERS_KV.get(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      "json"
    );
    return usage?.count || 0;
  }
  async incrementQuotaUsage(userId) {
    const currentUsage = await this.getUserQuotaUsage(userId);
    const userTier = await this.getUserTier(userId);
    const tierSettings = await this.getTierSettings();
    if (currentUsage >= tierSettings.config[userTier].maxQuota) {
      throw new Error("Quota exceeded for current tier");
    }
    const newUsage = {
      count: currentUsage + 1,
      lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      JSON.stringify(newUsage)
    );
    return newUsage.count;
  }
  async resetQuotaUsage(userId) {
    const resetData = {
      count: 0,
      lastReset: (/* @__PURE__ */ new Date()).toISOString()
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      JSON.stringify(resetData)
    );
    return resetData;
  }
  async getUserTierStatus(userId) {
    try {
      const [userTierData, tierSettings] = await Promise.all([
        this.getUserTier(userId),
        this.getTierSettings()
      ]);
      const currentTier = typeof userTierData === "object" ? userTierData.tier : userTierData;
      const usage = await this.getUserQuotaUsage(userId);
      const tierConfig = tierSettings.config[currentTier];
      if (!tierConfig) {
        throw new Error(`Invalid tier configuration for tier: ${currentTier}`);
      }
      return {
        currentTier,
        tierName: tierConfig.name,
        usage,
        maxQuota: tierConfig.maxQuota,
        remainingQuota: tierConfig.maxQuota - usage,
        price: tierConfig.price,
        features: tierConfig.features,
        quotaPercentage: (usage / tierConfig.maxQuota * 100).toFixed(2)
      };
    } catch (error) {
      console.error("Error getting user tier status:", error);
      throw error;
    }
  }
};
var TierService = _TierService;
__name(TierService, "TierService");
// KV key prefixes
__publicField(TierService, "KEYS", {
  SETTINGS: "t_setting",
  USER_TIER: "t_setting:user_tier",
  QUOTA_USAGE: "t_setting:quota_usage"
});
// Tier definitions
__publicField(TierService, "TIERS", {
  FREE: "free",
  MEDIUM: "medium",
  HIGH: "high"
});
// Default tier configuration
__publicField(TierService, "DEFAULT_TIER_CONFIG", {
  [_TierService.TIERS.FREE]: {
    name: "Free Tier",
    maxQuota: 1e3,
    price: 0,
    features: ["Basic API access", "Community support"]
  },
  [_TierService.TIERS.MEDIUM]: {
    name: "Medium Tier",
    maxQuota: 1e4,
    price: 9.99,
    features: ["Increased quota", "Email support"]
  },
  [_TierService.TIERS.HIGH]: {
    name: "High Tier",
    maxQuota: 1e6,
    price: 49.99,
    features: ["Maximum quota", "Priority support", "24/7 phone support"]
  }
});

// src/controllers/tierController.js
var TierController = class {
  constructor(env) {
    this.env = env;
    this.tierService = new TierService(env);
    this.responseService = new ResponseService();
    this.apiKeyService = new ApiKeyService();
  }
  async _getUserFromApiKey(apiKey) {
    if (!apiKey) {
      throw new Error("API Key is required");
    }
    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return user;
  }
  async getTierSettings(request) {
    try {
      const settings = await this.tierService.getTierSettings();
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(settings)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUserTierStatus(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      const user = await this._getUserFromApiKey(apiKey);
      const status = await this.tierService.getUserTierStatus(user.id);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(status)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async updateUserTier(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      const { tier } = await request.json();
      if (!tier) {
        throw new Error("Tier is required");
      }
      const user = await this._getUserFromApiKey(apiKey);
      const updatedTier = await this.tierService.setUserTier(user.id, tier);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            updatedTier,
            "User tier updated successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      const user = await this._getUserFromApiKey(apiKey);
      const usage = await this.tierService.getUserQuotaUsage(user.id);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess({ usage })),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async resetQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      const user = await this._getUserFromApiKey(apiKey);
      const resetData = await this.tierService.resetQuotaUsage(user.id);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            resetData,
            "Quota usage reset successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(TierController, "TierController");

// src/routes/tierRoutes.js
function createTierRouter(env) {
  const router2 = e({ base: "/api/tiers" });
  const tierController = new TierController(env);
  router2.get("/settings", (request) => tierController.getTierSettings(request));
  router2.get("/status", (request) => tierController.getUserTierStatus(request));
  router2.put("/upgrade", (request) => tierController.updateUserTier(request));
  router2.get("/quota", (request) => tierController.getQuotaUsage(request));
  router2.post(
    "/quota/reset",
    (request) => tierController.resetQuotaUsage(request)
  );
  return router2;
}
__name(createTierRouter, "createTierRouter");

// src/swagger/swagger.js
var swaggerDocument = {
  openapi: "3.0.0",
  info: {
    title: "User API Documentation",
    version: "1.0.0",
    description: "API documentation for User Management System"
  },
  servers: [
    {
      url: "http://localhost:3000",
      description: "Local Development"
    }
  ],
  paths: {
    "/api/users/": {
      post: {
        summary: "Create a new user",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["username", "email", "password"],
                properties: {
                  username: {
                    type: "string",
                    description: "User username"
                  },
                  email: {
                    type: "string",
                    format: "email",
                    description: "User email"
                  },
                  password: {
                    type: "string",
                    description: "User password"
                  }
                }
              }
            }
          }
        },
        responses: {
          201: {
            description: "User created successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UserResponse"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/userdetail": {
      get: {
        summary: "Get user details",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: []
          }
        ],
        responses: {
          200: {
            description: "User details retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UserResponse"
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/settings": {
      get: {
        summary: "Get tier settings",
        tags: ["Tiers"],
        description: "Retrieve all tier configurations including quotas and features",
        responses: {
          200: {
            description: "Tier settings retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        config: {
                          type: "object",
                          properties: {
                            free: { $ref: "#/components/schemas/TierConfig" },
                            medium: { $ref: "#/components/schemas/TierConfig" },
                            high: { $ref: "#/components/schemas/TierConfig" }
                          }
                        },
                        updatedAt: { type: "string", format: "date-time" },
                        version: { type: "string" }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/status": {
      get: {
        summary: "Get user tier status",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Get current user tier status including usage and quota information",
        responses: {
          200: {
            description: "User tier status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/TierStatusResponse"
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/upgrade": {
      put: {
        summary: "Upgrade user tier",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Upgrade user to a different tier",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["free", "medium", "high"],
                    description: "The tier to upgrade to"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Tier upgraded successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        tier: { type: "string" },
                        updatedAt: { type: "string", format: "date-time" }
                      }
                    },
                    message: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/quota": {
      get: {
        summary: "Get quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Get current quota usage information",
        responses: {
          200: {
            description: "Quota usage retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        usage: { type: "number" }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/quota/reset": {
      post: {
        summary: "Reset quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Reset the quota usage counter to zero",
        responses: {
          200: {
            description: "Quota reset successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        count: { type: "number" },
                        lastReset: { type: "string", format: "date-time" }
                      }
                    },
                    message: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  components: {
    schemas: {
      UserResponse: {
        type: "object",
        properties: {
          success: { type: "boolean" },
          data: {
            type: "object",
            properties: {
              id: { type: "string" },
              username: { type: "string" },
              email: { type: "string" },
              api_key: { type: "string" }
            }
          }
        }
      },
      TierConfig: {
        type: "object",
        properties: {
          name: { type: "string" },
          maxQuota: { type: "number" },
          price: { type: "number" },
          features: {
            type: "array",
            items: { type: "string" }
          }
        }
      },
      TierStatusResponse: {
        type: "object",
        properties: {
          success: { type: "boolean" },
          data: {
            type: "object",
            properties: {
              currentTier: { type: "string" },
              tierName: { type: "string" },
              usage: { type: "number" },
              maxQuota: { type: "number" },
              remainingQuota: { type: "number" },
              price: { type: "number" },
              features: {
                type: "array",
                items: { type: "string" }
              },
              quotaPercentage: { type: "string" }
            }
          }
        }
      }
    },
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "x-api-key"
      }
    }
  }
};
function getSwaggerHTML(swaggerSpec) {
  return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>API Documentation</title>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui.min.css" />
      </head>
      <body>
          <div id="swagger-ui"></div>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui-bundle.min.js"><\/script>
          <script>
              window.onload = () => {
                  window.ui = SwaggerUIBundle({
                      spec: ${JSON.stringify(swaggerSpec)},
                      dom_id: '#swagger-ui',
                      deepLinking: true,
                      presets: [
                          SwaggerUIBundle.presets.apis,
                          SwaggerUIBundle.SwaggerUIStandalonePreset
                      ],
                  });
              };
          <\/script>
      </body>
      </html>
    `;
}
__name(getSwaggerHTML, "getSwaggerHTML");

// src/index.js
var router = e();
async function initializeTierSettings(env) {
  const tierService = new TierService(env);
  await tierService.initializeTierSettings();
  console.log("Tier settings initialized");
}
__name(initializeTierSettings, "initializeTierSettings");
router.get("/api/docs", () => {
  return new Response(getSwaggerHTML(swaggerDocument), {
    headers: {
      "content-type": "text/html;charset=UTF-8"
    }
  });
});
router.all("/api/tiers/*", (request, env) => {
  const tierRouter = createTierRouter(env);
  return tierRouter.handle(request);
});
router.all("/api/users/*", (request, env) => {
  const userRouter = createUserRouter(env);
  return userRouter.handle(request);
});
router.all("*", () => new Response("Not Found", { status: 404 }));
var src_default = {
  async fetch(request, env, ctx) {
    await initializeTierSettings(env);
    return router.handle(request, env, ctx);
  }
};

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e2) {
      console.error("Failed to drain the unused request body.", e2);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e2) {
  return {
    name: e2?.name,
    message: e2?.message ?? String(e2),
    stack: e2?.stack,
    cause: e2?.cause === void 0 ? void 0 : reduceError(e2.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e2) {
    const error = reduceError(e2);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-GusSqL/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-GusSqL/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
