{"version": 3, "sources": ["../bundle-mB1F46/checked-fetch.js", "../../../node_modules/itty-router/dist/itty-router.mjs", "../../../src/services/responseService.js", "../../../src/services/emailService.js", "../../../src/services/emailQueueService.js", "../../../src/services/userService.js", "../../../src/services/apiKeyService.js", "../../../src/services/validationService.js", "../../../src/controllers/userController.js", "../../../src/controllers/debugController.js", "../../../src/controllers/apiUsageController.js", "../../../src/routes/userRoutes.js", "../../../src/services/tierService.js", "../../../src/controllers/tierController.js", "../../../src/routes/tierRoutes.js", "../../../src/routes/debugRoutes.js", "../../../src/services/paypalService.js", "../../../src/controllers/subscriptionController.js", "../../../src/routes/subscriptionRoutes.js", "../../../src/swagger/swagger.js", "../../../src/services/usageHistoryService.js", "../../../src/controllers/usageController.js", "../../../src/routes/usageRoutes.js", "../../../src/services/webhookService.js", "../../../src/controllers/webhookController.js", "../../../src/routes/webhookRoutes.js", "../../../src/controllers/webhookTestController.js", "../../../src/routes/webhookTestRoutes.js", "../../../src/index.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-scheduled.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-mB1F46/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-mB1F46/middleware-loader.entry.ts"], "sourceRoot": "/home/<USER>/Docker/lemp/html/backend_awp/.wrangler/tmp/dev-rBMbyz", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "const e=({base:e=\"\",routes:r=[]}={})=>({__proto__:new Proxy({},{get:(a,o,t)=>(a,...p)=>r.push([o.toUpperCase(),RegExp(`^${(e+a).replace(/(\\/?)\\*/g,\"($1.*)?\").replace(/(\\/$)|((?<=\\/)\\/)/,\"\").replace(/(:(\\w+)\\+)/,\"(?<$2>.*)\").replace(/:(\\w+)(\\?)?(\\.)?/g,\"$2(?<$1>[^/]+)$2$3\").replace(/\\.(?=[\\w(])/,\"\\\\.\").replace(/\\)\\.\\?\\(([^\\[]+)\\[\\^/g,\"?)\\\\.?($1(?<=\\\\.)[^\\\\.\")}/*$`),p])&&t}),routes:r,async handle(e,...a){let o,t,p=new URL(e.url),l=e.query={};for(let[e,r]of p.searchParams)l[e]=void 0===l[e]?r:[l[e],r].flat();for(let[l,s,c]of r)if((l===e.method||\"ALL\"===l)&&(t=p.pathname.match(s))){e.params=t.groups||{};for(let r of c)if(void 0!==(o=await r(e.proxy||e,...a)))return o}}});export{e as Router};\n", "export class ResponseService {\n    formatSuccess(data, message = null) {\n      return {\n        success: true,\n        data,\n        message,\n        timestamp: new Date().toISOString()\n      };\n    }\n  \n    formatError(message, statusCode = 500) {\n      return {\n        success: false,\n        message,\n        statusCode,\n        timestamp: new Date().toISOString()\n      };\n    }\n  }", "export class EmailService {\n  constructor(env) {\n    if (!env.ZEPTO_TOKEN) {\n      console.error(\n        \"❌ ZEPTO_TOKEN is not configured in environment variables\"\n      );\n      throw new Error(\"Email service configuration missing\");\n    }\n\n    if (!env.ZEPTO_FROM_ADDRESS) {\n      console.error(\n        \"❌ ZEPTO_FROM_ADDRESS is not configured in environment variables\"\n      );\n      throw new Error(\"Email service configuration missing\");\n    }\n\n    this.API_URL = \"https://api.zeptomail.com/v1.1/email/template\";\n    this.token = env.ZEPTO_TOKEN;\n    this.fromAddress = env.ZEPTO_FROM_ADDRESS.includes(\"@\")\n      ? env.ZEPTO_FROM_ADDRESS\n      : `noreply@${env.ZEPTO_FROM_ADDRESS}`;\n    this.templateKey =\n      \"2d6f.15d6311547e5377.k1.27e302f0-a62e-11ef-914f-525400fa05f6.19342aa189f\";\n  }\n\n  async sendApiKeyEmail(email, username, apiKey, password, domain) {\n    console.log(\"📧 Starting to send email to:\", email);\n\n    try {\n      const requestBody = {\n        template_key: this.templateKey,\n        from: {\n          address: this.fromAddress,\n          name: \"Superuser.ID\",\n        },\n        to: [\n          {\n            email_address: {\n              address: email,\n              name: email.split(\"@\")[0],\n            },\n          },\n        ],\n        merge_info: {\n          API_KEY: apiKey, // Changed to match template variable\n          DOMAIN: domain,\n          USER_EMAIL: email,\n          TEMP_PASSWORD: password,\n          YEAR: new Date().getFullYear(),\n          AWP_APP: \"Superuser.ID\",\n          DOCS_URL: \"https://docs.superuser.id\",\n          API_DOCS: \"https://superuser.id/portal\",\n          EMAIL_SUPPORT: \"<EMAIL>\",\n        },\n        track_clicks: false,\n        track_opens: false,\n      };\n\n      console.log(\"📤 Sending email with API key:\", apiKey);\n\n      const response = await fetch(this.API_URL, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: this.token,\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      const responseData = await response.json();\n      console.log(\n        \"📨 Email API Response:\",\n        JSON.stringify(responseData, null, 2)\n      );\n\n      if (!response.ok) {\n        throw new Error(\n          `Email API error: ${response.status} - ${JSON.stringify(\n            responseData\n          )}`\n        );\n      }\n\n      return {\n        success: true,\n        messageId: responseData.message_id,\n      };\n    } catch (error) {\n      console.error(\"❌ Error sending email:\", error);\n      return {\n        success: false,\n        error: error.message,\n      };\n    }\n  }\n}\n", "// emailQueueService.js\nimport { EmailService } from \"./emailService.js\"; // Add this import\nexport class EmailQueueService {\n  constructor(env) {\n    this.env = env;\n    this.emailService = new EmailService(env);\n  }\n\n  async addToQueue(emailData) {\n    try {\n      console.log(\"🚀 Adding email to queue:\", {\n        email: emailData.email,\n        type: emailData.type,\n        domain: emailData.domain,\n      });\n\n      const id = crypto.randomUUID();\n      const queueItem = {\n        id,\n        ...emailData,\n        status: \"pending\",\n        attempts: 0,\n        queuedAt: new Date().toISOString(),\n        processAfter: new Date().toISOString(),\n      };\n\n      await this.env.USERS_KV.put(\n        `email_queue:${id}`,\n        JSON.stringify(queueItem)\n      );\n\n      // Process immediately\n      await this.processQueueItem(queueItem);\n\n      return id;\n    } catch (error) {\n      console.error(\"❌ Error adding to queue:\", error);\n      throw error;\n    }\n  }\n\n  async processQueueItem(queueItem) {\n    console.log(`📧 Processing email ${queueItem.id} for ${queueItem.email}`);\n\n    try {\n      const result = await this.emailService.sendApiKeyEmail(\n        queueItem.email,\n        queueItem.username,\n        queueItem.apiKey,\n        queueItem.password,\n        queueItem.domain\n      );\n\n      if (result.success) {\n        console.log(`✅ Email sent successfully to ${queueItem.email}`);\n        // Move to sent items\n        await Promise.all([\n          this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),\n          this.env.USERS_KV.put(\n            `email_sent:${queueItem.id}`,\n            JSON.stringify({\n              ...queueItem,\n              status: \"sent\",\n              sentAt: new Date().toISOString(),\n              messageId: result.messageId,\n            })\n          ),\n        ]);\n        return result;\n      } else {\n        throw new Error(result.error || \"Failed to send email\");\n      }\n    } catch (error) {\n      console.error(`❌ Error processing email for ${queueItem.email}:`, error);\n\n      // Handle retry logic\n      const attempts = (queueItem.attempts || 0) + 1;\n      if (attempts >= 3) {\n        await this.moveToFailed(queueItem, error);\n      } else {\n        await this.scheduleRetry(queueItem, attempts, error); // Pass the error\n      }\n\n      throw error;\n    }\n  }\n\n  async moveToFailed(queueItem, error) {\n    await Promise.all([\n      this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),\n      this.env.USERS_KV.put(\n        `email_failed:${queueItem.id}`,\n        JSON.stringify({\n          ...queueItem,\n          status: \"failed\",\n          error: error.message,\n          failedAt: new Date().toISOString(),\n        })\n      ),\n    ]);\n  }\n\n  async scheduleRetry(queueItem, attempts, error) {\n    // Added error parameter\n    const delayMinutes = Math.pow(2, attempts - 1);\n    const processAfter = new Date(\n      Date.now() + delayMinutes * 60 * 1000\n    ).toISOString();\n\n    await this.env.USERS_KV.put(\n      `email_queue:${queueItem.id}`,\n      JSON.stringify({\n        ...queueItem,\n        attempts,\n        processAfter,\n        lastError: error.message, // Now error is defined\n        lastAttempt: new Date().toISOString(),\n      })\n    );\n\n    console.log(\n      `⏳ Scheduled retry #${attempts} for ${queueItem.email} after ${delayMinutes} minutes`\n    );\n  }\n\n  async getQueueStatus() {\n    try {\n      const [pending, sent, failed] = await Promise.all([\n        this.env.USERS_KV.list({ prefix: \"email_queue:\" }),\n        this.env.USERS_KV.list({ prefix: \"email_sent:\" }),\n        this.env.USERS_KV.list({ prefix: \"email_failed:\" }),\n      ]);\n\n      const status = {\n        pending: pending.keys.length,\n        sent: sent.keys.length,\n        failed: failed.length,\n        details: {\n          pending: await this._getQueueItems(\"email_queue:\"),\n          sent: await this._getQueueItems(\"email_sent:\"),\n          failed: await this._getQueueItems(\"email_failed:\"),\n        },\n      };\n\n      console.log(\"📊 Queue Status:\", status);\n      return status;\n    } catch (error) {\n      console.error(\"❌ Error getting queue status:\", error);\n      throw error;\n    }\n  }\n\n  async _getQueueItems(prefix) {\n    const { keys } = await this.env.USERS_KV.list({ prefix });\n    const items = await Promise.all(\n      keys.map(async (key) => {\n        const value = await this.env.USERS_KV.get(key.name, \"json\");\n        return { key: key.name, ...value };\n      })\n    );\n    return items;\n  }\n\n  async getDebugInfo() {\n    const status = await this.getQueueStatus();\n    return {\n      queueStatus: status,\n      timestamp: new Date().toISOString(),\n      environment: {\n        hasZeptoToken: !!this.env.ZEPTO_TOKEN,\n        hasFromAddress: !!this.env.ZEPTO_FROM_ADDRESS,\n        zeptoToken: {\n          exists: !!this.env.ZEPTO_TOKEN,\n          length: this.env.ZEPTO_TOKEN?.length || 0,\n          preview: this.env.ZEPTO_TOKEN\n            ? `${this.env.ZEPTO_TOKEN.substring(0, 10)}...`\n            : null,\n        },\n        fromAddress: this.env.ZEPTO_FROM_ADDRESS || \"not configured\",\n      },\n    };\n  }\n\n  async getDebugInfo() {\n    const status = await this.getQueueStatus();\n    return {\n      queueStatus: status,\n      timestamp: new Date().toISOString(),\n      environment: {\n        hasZeptoKey: !!this.env.ZEPTOMAIL_API_KEY,\n        keyLength: this.env.ZEPTOMAIL_API_KEY?.length || 0,\n      },\n    };\n  }\n}\n", "import { EmailService } from \"./emailService.js\";\nimport { EmailQueueService } from \"./emailQueueService.js\";\n\nexport class UserService {\n  constructor(env) {\n    this.env = env;\n    this.emailService = new EmailService(env);\n    this.emailQueueService = new EmailQueueService(env);\n  }\n\n  _generatePassword() {\n    try {\n      // Generate a strong random password\n      const length = 12;\n      const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n      const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n      const numbers = \"0123456789\";\n      const special = \"!@#$%^&*\";\n\n      let password = \"\";\n\n      // Add at least one of each type\n      password += lowercase[Math.floor(Math.random() * lowercase.length)];\n      password += uppercase[Math.floor(Math.random() * uppercase.length)];\n      password += numbers[Math.floor(Math.random() * numbers.length)];\n      password += special[Math.floor(Math.random() * special.length)];\n\n      // Complete the rest of the password\n      const allChars = lowercase + uppercase + numbers + special;\n      for (let i = password.length; i < length; i++) {\n        password += allChars[Math.floor(Math.random() * allChars.length)];\n      }\n\n      // Shuffle the password\n      return password\n        .split(\"\")\n        .sort(() => 0.5 - Math.random())\n        .join(\"\");\n    } catch (error) {\n      console.error(\"Error generating password:\", error);\n      // Fallback password generation\n      return `Pass${Math.random().toString(36).substring(2, 10)}!`;\n    }\n  }\n\n  async createUser(userData) {\n    try {\n      // Input validation\n      if (!userData.domain || !userData.email) {\n        throw new Error(\"Domain and email are required\");\n      }\n\n      // Normalize email and domain\n      const normalizedEmail = userData.email.toLowerCase().trim();\n      const normalizedDomain = userData.domain.toLowerCase().trim();\n\n      // Check if the exact domain and email combination exists\n      const existingUser = await this.env.USERS_KV.get(\n        `email:${normalizedEmail}`,\n        \"json\"\n      );\n\n      if (existingUser && existingUser.domain === normalizedDomain) {\n        throw new Error(\"This email and domain is already registered.\");\n      }\n\n      // Check if domain exists\n      const existingDomain = await this.env.USERS_KV.get(\n        `domain:${normalizedDomain}`,\n        \"json\"\n      );\n\n      if (existingDomain) {\n        throw new Error(\n          \"Domain is already registered. Each domain can only have one API key.\"\n        );\n      }\n\n      // Check if email exists to determine if we should reuse password\n      let existingEmail = await this.env.USERS_KV.get(\n        `email:${normalizedEmail}`,\n        \"json\"\n      );\n\n      // Generate password and API key\n      const apiKey = crypto.randomUUID();\n      let plainPassword;\n      let hashedPassword;\n\n      if (existingEmail) {\n        // Reuse password from existing email registration\n        const existingCredentials = await this.env.USERS_KV.get(\n          `credentials:${existingEmail.id}`,\n          \"json\"\n        );\n\n        // Verify we have existing credentials\n        if (!existingCredentials) {\n          console.error(\n            \"No credentials found for existing email, generating new password\"\n          );\n          plainPassword = this._generatePassword();\n          hashedPassword = await this._hashPassword(plainPassword);\n        } else {\n          // Reuse existing credentials\n          plainPassword = await this._getPlainPasswordFromCredentials(\n            existingCredentials\n          );\n          hashedPassword = existingCredentials.hashedPassword;\n        }\n      } else {\n        // Generate new password for new email\n        plainPassword = this._generatePassword();\n        hashedPassword = await this._hashPassword(plainPassword);\n      }\n\n      // Create user object\n      const user = {\n        id: crypto.randomUUID(),\n        email: userData.email,\n        domain: userData.domain,\n        password: hashedPassword, // Store hashed password\n        api_key: apiKey,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n\n      // Store credentials separately for verification\n      const credentials = {\n        hashedPassword,\n        apiKey,\n        userId: user.id,\n        email: user.email,\n        createdAt: user.createdAt,\n      };\n\n      // Store in KV\n      await Promise.all([\n        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),\n        this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),\n        this.env.USERS_KV.put(`domain:${user.domain}`, JSON.stringify(user)),\n        this.env.USERS_KV.put(`api_key:${apiKey}`, JSON.stringify(user)),\n        // Store credentials separately\n        this.env.USERS_KV.put(\n          `credentials:${user.id}`,\n          JSON.stringify(credentials)\n        ),\n      ]);\n\n      // Queue welcome email with plain password\n      try {\n        const emailQueueId = await this.emailQueueService.addToQueue({\n          email: user.email,\n          domain: user.domain,\n          password: plainPassword, // Send plain password in email\n          apiKey: apiKey,\n          type: \"welcome\",\n        });\n\n        console.log(\"📬 Welcome email queued:\", emailQueueId);\n      } catch (error) {\n        console.error(\"❌ Error queueing welcome email:\", error);\n      }\n\n      // Return response with generated credentials\n      const response = {\n        ...this._formatUserData(user),\n        credentials: {\n          password: plainPassword, // Include plain password in initial response\n          apiKey: apiKey,\n        },\n      };\n\n      return response;\n    } catch (error) {\n      console.error(\"Error in createUser:\", error);\n      throw error;\n    }\n  }\n\n  async _hashPassword(password) {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(password);\n    const hash = await crypto.subtle.digest(\"SHA-256\", data);\n    return Array.from(new Uint8Array(hash))\n      .map((b) => b.toString(16).padStart(2, \"0\"))\n      .join(\"\");\n  }\n\n    // Helper method to get plain password from credentials\n  async _getPlainPasswordFromCredentials(credentials) {\n    // For existing users, return the stored plain password\n    return credentials.plainPassword;\n  }    \n\n  async getUserDetail(apiKey) {\n    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, \"json\");\n    if (!user) {\n      throw new Error(\"User not found\");\n    }\n    return this._formatUserData(user);\n  }\n\n  async verifyPassword(userId, password) {\n    const credentials = await this.env.USERS_KV.get(\n      `credentials:${userId}`,\n      \"json\"\n    );\n    if (!credentials) {\n      throw new Error(\"Credentials not found\");\n    }\n    const hashedInput = await this._hashPassword(password);\n    return hashedInput === credentials.hashedPassword;\n  }\n\n  async activateUser(license) {\n    try {\n      // Get user by API key instead of license\n      const user = await this.env.USERS_KV.get(`api_key:${license}`, \"json\");\n\n      if (!user) {\n        return false;\n      }\n\n      // Update user status to active\n      const updatedUser = {\n        ...user,\n        status: \"active\",\n        activatedAt: new Date().toISOString(),\n      };\n\n      // Update all references\n      await Promise.all([\n        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(updatedUser)),\n        this.env.USERS_KV.put(\n          `email:${user.email}`,\n          JSON.stringify(updatedUser)\n        ),\n        this.env.USERS_KV.put(\n          `api_key:${license}`,\n          JSON.stringify(updatedUser)\n        ),\n        user.domain &&\n          this.env.USERS_KV.put(\n            `domain:${user.domain}`,\n            JSON.stringify(updatedUser)\n          ),\n      ]);\n\n      return true;\n    } catch (error) {\n      console.error(\"Error activating user:\", error);\n      return false;\n    }\n  }\n\n  async getLicenseStatus(license) {\n    try {\n      const user = await this.env.USERS_KV.get(`api_key:${license}`, \"json\");\n\n      if (!user) {\n        return {\n          isValid: false,\n          message: \"License not found\",\n        };\n      }\n\n      return {\n        isValid: true,\n        status: user.status || \"pending\",\n        activatedAt: user.activatedAt || null,\n        domain: user.domain,\n        message:\n          user.status === \"active\"\n            ? \"License is activated\"\n            : \"License is pending activation\",\n      };\n    } catch (error) {\n      console.error(\"Error checking license status:\", error);\n      return {\n        isValid: false,\n        message: \"Error checking license status\",\n      };\n    }\n  }\n\n  _formatUserData(user) {\n    const { password, ...userData } = user;\n    return userData;\n  }\n}\n", "export class ApiKeyService {\n    async validateApi<PERSON>ey(request, env) {\n      const apiKey = request.headers.get('x-sps-key');\n  \n      if (!apiKey) {\n        throw new Error('API Key is required in x-sps-key header');\n      }\n  \n      const user = await env.USERS_KV.get(`api_key:${apiKey}`, 'json');\n      if (!user) {\n        throw new Error('Invalid API Key');\n      }\n  \n      return user;\n    }\n  }", "export class ValidationService {\n  validateUserData(userData) {\n    const errors = [];\n\n    if (!userData.email) errors.push(\"Email is required\");\n    if (!userData.domain) errors.push(\"Domain is required\");\n\n    if (userData.email && !this._isValidEmail(userData.email)) {\n      errors.push(\"Invalid email format\");\n    }\n\n    if (userData.domain && !this._isValidDomain(userData.domain)) {\n      errors.push(\"Invalid domain format\");\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  _isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n\n  _isValidDomain(domain) {\n    // Basic domain validation regex\n    const domainRegex =\n      /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}$/;\n    return domainRegex.test(domain);\n  }\n}\n", "// src/controllers/userController.js\nimport { ResponseService } from \"../services/responseService\";\nimport { UserService } from \"../services/userService\";\nimport { ApiKeyService } from \"../services/apiKeyService\";\nimport { ValidationService } from \"../services/validationService\";\n\nexport class UserController {\n  constructor(env) {\n    this.env = env;\n    this.userService = new UserService(env);\n    this.apiKeyService = new ApiKeyService();\n    this.responseService = new ResponseService();\n    this.validationService = new ValidationService();\n  }\n\n  async createUser(request) {\n    try {\n      // Parse the request body\n      const userData = await request.json();\n      console.log(\"Received user data:\", userData); // Debug log\n\n      // Validate input\n      const validation = this.validationService.validateUserData(userData);\n      if (!validation.isValid) {\n        return new Response(\n          JSON.stringify(\n            this.responseService.formatError(validation.errors.join(\", \"))\n          ),\n          { status: 200, headers: { \"Content-Type\": \"application/json\" } }\n        );\n      }\n\n      // Ensure domain and email are present\n      if (!userData.domain || !userData.email) {\n        return new Response(\n          JSON.stringify(\n            this.responseService.formatError(\"Domain and email are required\")\n          ),\n          { status: 200, headers: { \"Content-Type\": \"application/json\" } }\n        );\n      }\n\n      const user = await this.userService.createUser(userData);\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess(\n            user,\n            \"User created successfully. Please check your email for credentials.\"\n          )\n        ),\n        { status: 201, headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      console.error(\"Error creating user:\", error); // Debug log\n      // Check if it's a domain registration error\n      if (error.message.includes(\"Domain is already registered\")) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: error.message,\n            timestamp: new Date().toISOString(),\n          }),\n          {\n            status: 200, // Keep 200 for domain registration error\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // For other errors, maintain the same structure but with 200 status\n      return new Response(\n        JSON.stringify({\n          success: false,\n          message: error.message,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          status: 200, // Changed from 400/500 to 200\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async validateApiKey(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n\n      if (!apiKey) {\n        return new Response(\n          JSON.stringify(\n            this.responseService.formatSuccess({\n              isValid: false,\n              message: \"API Key is required in x-sps-key header\",\n            })\n          ),\n          { headers: { \"Content-Type\": \"application/json\" } }\n        );\n      }\n\n      try {\n        // Try to validate the API key\n        await this.apiKeyService.validateApiKey(request, this.env);\n\n        return new Response(\n          JSON.stringify(\n            this.responseService.formatSuccess({\n              isValid: true,\n            })\n          ),\n          { headers: { \"Content-Type\": \"application/json\" } }\n        );\n      } catch (error) {\n        // If validation fails, return isValid: false\n        return new Response(\n          JSON.stringify(\n            this.responseService.formatSuccess({\n              isValid: false,\n              message: error.message,\n            })\n          ),\n          { headers: { \"Content-Type\": \"application/json\" } }\n        );\n      }\n    } catch (error) {\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatError(\"Server error occurred\")\n        ),\n        { status: 500, headers: { \"Content-Type\": \"application/json\" } }\n      );\n    }\n  }\n\n  async getUserDetail(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      if (!apiKey) {\n        return new Response(\n          JSON.stringify(\n            this.responseService.formatError(\"API Key is required\")\n          ),\n          { status: 401, headers: { \"Content-Type\": \"application/json\" } }\n        );\n      }\n      const user = await this.userService.getUserDetail(apiKey);\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(user)),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        { status: 401, headers: { \"Content-Type\": \"application/json\" } }\n      );\n    }\n  }\n\n  async activate(request) {\n    try {\n      const data = await request.json();\n      const { license } = data;\n\n      if (!license) {\n        return new Response(\n          JSON.stringify({\n            status: false,\n            message: \"License key is required\",\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      const activated = await this.userService.activateUser(license);\n\n      return new Response(\n        JSON.stringify({\n          status: activated,\n          message: activated\n            ? \"Success activate api key\"\n            : \"Failed activate api key\",\n        }),\n        {\n          status: activated ? 200 : 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      console.error(\"Error activating user:\", error);\n      return new Response(\n        JSON.stringify({\n          status: false,\n          message: \"Failed activate api key\",\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async checkLicenseStatus(request) {\n    try {\n      const data = await request.json();\n      const { license } = data;\n\n      if (!license) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: \"License key is required\",\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      const status = await this.userService.getLicenseStatus(license);\n      return new Response(\n        JSON.stringify({\n          success: status.isValid,\n          data: status,\n        }),\n        {\n          status: 200,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      console.error(\"Error checking license status:\", error);\n      return new Response(\n        JSON.stringify({\n          success: false,\n          message: \"Failed to check license status\",\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n}\n", "// src/controllers/debugController.js\nexport class DebugController {\n  constructor(env) {\n    this.env = env;\n  }\n\n  async listAllKVData(request) {\n    try {\n      // List all keys with their prefixes\n      const list = await this.env.USERS_KV.list();\n\n      // Fetch all values\n      const data = await Promise.all(\n        list.keys.map(async (key) => {\n          const value = await this.env.USERS_KV.get(key.name, \"json\");\n          return {\n            key: key.name,\n            value,\n          };\n        })\n      );\n\n      // Group data by prefix\n      const groupedData = data.reduce((acc, item) => {\n        const prefix = item.key.split(\":\")[0];\n        if (!acc[prefix]) {\n          acc[prefix] = [];\n        }\n        acc[prefix].push(item);\n        return acc;\n      }, {});\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: {\n            summary: {\n              totalKeys: list.keys.length,\n              prefixes: Object.keys(groupedData),\n              countByPrefix: Object.fromEntries(\n                Object.entries(groupedData).map(([k, v]) => [k, v.length])\n              ),\n            },\n            grouped: groupedData,\n            raw: data,\n          },\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async deleteKVData(request) {\n    try {\n      const { key } = await request.json();\n      if (!key) {\n        throw new Error(\"Key is required\");\n      }\n      await this.env.USERS_KV.delete(key);\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: `Key ${key} deleted successfully`,\n        }),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getUserTierData(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, \"json\");\n      const tierData = await this.env.USERS_KV.get(\n        `${TierService.KEYS.USER_TIER}:${user.id}`,\n        \"json\"\n      );\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: {\n            user,\n            tierData,\n          },\n        }),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n}\n", "export class ApiUsageController {\n  constructor(env) {\n    this.env = env;\n  }\n\n  async trackUsage(request) {\n    try {\n      const data = await request.json();\n      const { apiKey, type, source, timestamp } = data;\n\n      // Validate required fields\n      if (!apiKey || !type || !source || !timestamp) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: \"Missing required fields: apiKey, type, source, timestamp\",\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // Validate type values\n      const validTypes = [\"images\", \"contents\"];\n      if (!validTypes.includes(type)) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: 'Invalid type. Must be either \"images\" or \"contents\"',\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // Create a unique ID for this usage record\n      const usageId = crypto.randomUUID();\n\n      const usageData = {\n        id: usageId,\n        apiKey,\n        type,\n        source,\n        timestamp,\n        createdAt: new Date().toISOString(),\n      };\n\n      // Store in KV with prefix for easy querying\n      await this.env.USERS_KV.put(\n        `d_api_usage:${usageId}`,\n        JSON.stringify(usageData)\n      );\n\n      // Also store by API key for quick user lookups\n      await this.env.USERS_KV.put(\n        `d_api_usage:${apiKey}:${usageId}`,\n        JSON.stringify(usageData)\n      );\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: \"API usage tracked successfully\",\n          data: usageData,\n        }),\n        {\n          status: 201,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          message: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getUserUsage(request) {\n    try {\n      const apiKey = request.headers.get(\"x-api-key\");\n\n      if (!apiKey) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: \"API Key is required in X-API-KEY header\",\n          }),\n          {\n            status: 401,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // List all usage records for this API key\n      const { keys } = await this.env.USERS_KV.list({\n        prefix: `d_api_usage:${apiKey}:`,\n      });\n\n      const usageData = await Promise.all(\n        keys.map(async (key) => {\n          const data = await this.env.USERS_KV.get(key.name, \"json\");\n          return data;\n        })\n      );\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: usageData,\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          message: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n}\n", "import { Router } from \"itty-router\";\nimport { UserController } from \"../controllers/userController\";\nimport { DebugController } from \"../controllers/debugController\";\nimport { ApiUsageController } from \"../controllers/apiUsageController\";\n\nexport function createUserRouter(env) {\n  const router = Router({ base: \"/api/users\" });\n  const userController = new UserController(env);\n  const debugController = new DebugController(env);\n  const apiUsageController = new ApiUsageController(env);\n\n  router.post(\"/validatekey\", (request) =>\n    userController.validateApiKey(request)\n  );\n\n  // activate api key\n  router.post(\"/activate\", (request) => userController.activate(request));\n  router.post(\"/license/status\", (request) =>\n    userController.checkLicenseStatus(request)\n  );\n\n  // Existing routes\n  router.get(\"/userdetail\", (request) => userController.getUserDetail(request));\n  router.post(\"/\", (request) => userController.createUser(request));\n\n  router.post(\"/usage/track\", (request) =>\n    apiUsageController.trackUsage(request)\n  );\n  router.get(\"/usage\", (request) => apiUsageController.getUserUsage(request));\n\n  // Debug routes\n  router.get(\"/debug/kv\", (request) => debugController.listAllKVData(request));\n  router.post(\"/debug/kv/delete\", (request) =>\n    debugController.deleteKVData(request)\n  );\n\n  return router;\n}\n", "// src/services/tierService.js\nexport class TierService {\n  constructor(env) {\n    this.env = env;\n  }\n\n  // KV key prefixes\n  static KEYS = {\n    SETTINGS: \"t_setting\",\n    USER_TIER: \"t_setting:user_tier\",\n    QUOTA_USAGE: \"t_setting:quota_usage\",\n  };\n\n  // Tier definitions\n  static TIERS = {\n    FREE: \"free\",\n    MEDIUM: \"medium\",\n    HIGH: \"high\",\n  };\n\n  // Default tier configuration\n  static DEFAULT_TIER_CONFIG = {\n    [TierService.TIERS.FREE]: {\n      name: \"Free Tier\",\n      maxQuota: 1000,\n      imagesQuota: 1000,\n      contentQuota: 1000,\n      titleQuota: 1000,\n      price: 0,\n      expirationDays: 30,\n      addon1: false,\n      addon2: false,\n      addon1_price: 4.99,\n      addon2_price: 9.99,\n      addon1_detail: [\n        \"Enhanced resolution\",\n        \"Advanced filters\",\n        \"Batch processing\",\n      ],\n      addon2_detail: [\n        \"Premium support\",\n        \"1-on-1 training\",\n        \"Custom integration\",\n      ],\n      features: [\"Basic API access\", \"Community support\"],\n    },\n    [TierService.TIERS.MEDIUM]: {\n      name: \"Medium Tier\",\n      maxQuota: 10000,\n      imagesQuota: 10000,\n      contentQuota: 10000,\n      titleQuota: 10000,\n      price: 9.99,\n      expirationDays: 30,\n      addon1: false,\n      addon2: false,\n      addon1_price: 9.99,\n      addon2_price: 19.99,\n      addon1_detail: [\n        \"Enhanced resolution\",\n        \"Advanced filters\",\n        \"Batch processing\",\n        \"Priority processing\",\n      ],\n      addon2_detail: [\n        \"Premium support\",\n        \"Weekly training\",\n        \"Custom integration\",\n        \"API consultation\",\n      ],\n      features: [\"Increased quota\", \"Email support\"],\n    },\n    [TierService.TIERS.HIGH]: {\n      name: \"High Tier\",\n      maxQuota: 1000000,\n      imagesQuota: 1000000,\n      contentQuota: 1000000,\n      titleQuota: 1000000,\n      price: 49.99,\n      expirationDays: 30,\n      addon1: false,\n      addon2: false,\n      addon1_price: 19.99,\n      addon2_price: 39.99,\n      addon1_detail: [\n        \"Enhanced resolution\",\n        \"Advanced filters\",\n        \"Batch processing\",\n        \"Priority processing\",\n        \"Custom models\",\n      ],\n      addon2_detail: [\n        \"Premium support\",\n        \"Daily training\",\n        \"Custom integration\",\n        \"Dedicated manager\",\n        \"24/7 phone support\",\n      ],\n      features: [\"Maximum quota\", \"Priority support\", \"24/7 phone support\"],\n    },\n  };\n\n  async getTypeQuotaUsage(userId, type) {\n    const usage = await this.env.USERS_KV.get(\n      `${TierService.KEYS.QUOTA_USAGE}:${userId}:${type}`,\n      \"json\"\n    );\n    return usage?.count || 0;\n  }\n\n  async incrementTypeQuotaUsage(userId, type) {\n    const currentUsage = await this.getTypeQuotaUsage(userId, type);\n    const userTier = await this.getUserTier(userId);\n    const tierSettings = await this.getTierSettings();\n\n    const quotaKey =\n      type === \"images\"\n        ? \"imagesQuota\"\n        : type === \"content\"\n        ? \"contentQuota\"\n        : \"titleQuota\";\n\n    const maxQuota = tierSettings.config[userTier.tier][quotaKey];\n\n    if (currentUsage >= maxQuota) {\n      throw new Error(`${type} quota exceeded for current tier`);\n    }\n\n    const newUsage = {\n      count: currentUsage + 1,\n      lastUpdated: new Date().toISOString(),\n    };\n\n    await this.env.USERS_KV.put(\n      `${TierService.KEYS.QUOTA_USAGE}:${userId}:${type}`,\n      JSON.stringify(newUsage)\n    );\n\n    return newUsage.count;\n  }\n\n  async initializeTierSettings() {\n    const defaultConfig = {\n      free: {\n        name: \"Free Tier\",\n        maxQuota: 1000,\n        imagesQuota: 1000,\n        contentQuota: 1000,\n        titleQuota: 1000,\n        price: 0,\n        expirationDays: 30,\n        addon1: false,\n        addon2: false,\n        addon1_price: 4.99,\n        addon2_price: 9.99,\n        addon1_detail: [\n          \"Enhanced resolution\",\n          \"Advanced filters\",\n          \"Batch processing\",\n        ],\n        addon2_detail: [\n          \"Premium support\",\n          \"1-on-1 training\",\n          \"Custom integration\",\n        ],\n        features: [\"Basic API access\", \"Community support\"],\n      },\n      medium: {\n        name: \"Medium Tier\",\n        maxQuota: 10000,\n        imagesQuota: 10000,\n        contentQuota: 10000,\n        titleQuota: 10000,\n        price: 9.99,\n        expirationDays: 30,\n        addon1: false,\n        addon2: false,\n        addon1_price: 9.99,\n        addon2_price: 19.99,\n        addon1_detail: [\n          \"Enhanced resolution\",\n          \"Advanced filters\",\n          \"Batch processing\",\n          \"Priority processing\",\n        ],\n        addon2_detail: [\n          \"Premium support\",\n          \"Weekly training\",\n          \"Custom integration\",\n          \"API consultation\",\n        ],\n        features: [\"Increased quota\", \"Email support\"],\n      },\n      high: {\n        name: \"High Tier\",\n        maxQuota: 1000000,\n        imagesQuota: 1000000,\n        contentQuota: 1000000,\n        titleQuota: 1000000,\n        price: 49.99,\n        expirationDays: 30,\n        addon1: false,\n        addon2: false,\n        addon1_price: 19.99,\n        addon2_price: 39.99,\n        addon1_detail: [\n          \"Enhanced resolution\",\n          \"Advanced filters\",\n          \"Batch processing\",\n          \"Priority processing\",\n          \"Custom models\",\n        ],\n        addon2_detail: [\n          \"Premium support\",\n          \"Daily training\",\n          \"Custom integration\",\n          \"Dedicated manager\",\n          \"24/7 phone support\",\n        ],\n        features: [\"Maximum quota\", \"Priority support\", \"24/7 phone support\"],\n      },\n    };\n\n    // Store the settings\n    await this.env.USERS_KV.put(\n      `${TierService.KEYS.SETTINGS}:tiers`,\n      JSON.stringify({\n        config: defaultConfig,\n        updatedAt: new Date().toISOString(),\n        version: \"1.0\",\n      })\n    );\n\n    return await this.getTierSettings();\n  }\n\n  async getTierSettings() {\n    return await this.env.USERS_KV.get(\n      `${TierService.KEYS.SETTINGS}:tiers`,\n      \"json\"\n    );\n  }\n\n  async updateTierSettings(tierConfig) {\n    const settings = {\n      config: tierConfig,\n      updatedAt: new Date().toISOString(),\n      version: \"1.0\",\n    };\n    await this.env.USERS_KV.put(\n      `${TierService.KEYS.SETTINGS}:tiers`,\n      JSON.stringify(settings)\n    );\n    return settings;\n  }\n\n  async getUserTier(userId) {\n    try {\n      const userTier = await this.env.USERS_KV.get(\n        `${TierService.KEYS.USER_TIER}:${userId}`,\n        \"json\"\n      );\n      // If no specific tier is set, return the default free tier\n      return (\n        userTier || {\n          tier: TierService.TIERS.FREE,\n          updatedAt: new Date().toISOString(),\n        }\n      );\n    } catch (error) {\n      console.error(\"Error getting user tier:\", error);\n      throw error;\n    }\n  }\n\n  async setUserTier(userId, newTier, options = {}) {\n    if (!Object.values(TierService.TIERS).includes(newTier)) {\n      throw new Error(\"Invalid tier specified\");\n    }\n\n    const tierSettings = await this.getTierSettings();\n    const newTierConfig = tierSettings.config[newTier];\n\n    if (!newTierConfig) {\n      throw new Error(`Configuration not found for tier: ${newTier}`);\n    }\n\n    // Validate current usage\n    const [imagesUsage, contentUsage] = await Promise.all([\n      this.getTypeQuotaUsage(userId, \"images\"),\n      this.getTypeQuotaUsage(userId, \"content\"),\n    ]);\n\n    const startDate = new Date();\n    const expirationDate = new Date(startDate);\n    expirationDate.setDate(startDate.getDate() + newTierConfig.expirationDays);\n\n    const tierData = {\n      tier: newTier,\n      startDate: startDate.toISOString(),\n      expirationDate: expirationDate.toISOString(),\n      updatedAt: new Date().toISOString(),\n      addon1: options.addon1 || false,\n      addon2: options.addon2 || false,\n      previousUsage: {\n        images: imagesUsage || 0,\n        content: contentUsage || 0,\n      },\n    };\n\n    await this.env.USERS_KV.put(\n      `${TierService.KEYS.USER_TIER}:${userId}`,\n      JSON.stringify(tierData)\n    );\n\n    return {\n      ...tierData,\n      currentStatus: await this.getUserTierStatus(userId),\n    };\n  }\n\n  async getUserQuotaUsage(userId) {\n    const usage = await this.env.USERS_KV.get(\n      `${TierService.KEYS.QUOTA_USAGE}:${userId}`,\n      \"json\"\n    );\n    return usage?.count || 0;\n  }\n\n  async incrementQuotaUsage(userId) {\n    const currentUsage = await this.getUserQuotaUsage(userId);\n    const userTier = await this.getUserTier(userId);\n    const tierSettings = await this.getTierSettings();\n\n    if (currentUsage >= tierSettings.config[userTier].maxQuota) {\n      throw new Error(\"Quota exceeded for current tier\");\n    }\n\n    const newUsage = {\n      count: currentUsage + 1,\n      lastUpdated: new Date().toISOString(),\n    };\n\n    await this.env.USERS_KV.put(\n      `${TierService.KEYS.QUOTA_USAGE}:${userId}`,\n      JSON.stringify(newUsage)\n    );\n    return newUsage.count;\n  }\n\n  async resetQuotaUsage(userId) {\n    const resetData = {\n      count: 0,\n      lastReset: new Date().toISOString(),\n    };\n\n    await Promise.all([\n      this.env.USERS_KV.put(\n        `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,\n        JSON.stringify(resetData)\n      ),\n      this.env.USERS_KV.put(\n        `${TierService.KEYS.QUOTA_USAGE}:${userId}:content`,\n        JSON.stringify(resetData)\n      ),\n    ]);\n\n    return resetData;\n  }\n\n  async getUserTierStatus(userId) {\n    try {\n      const [\n        userTierData,\n        tierSettings,\n        imagesUsage,\n        contentUsage,\n        titleUsage,\n      ] = await Promise.all([\n        this.getUserTier(userId),\n        this.getTierSettings(),\n        this.getTypeQuotaUsage(userId, \"images\"),\n        this.getTypeQuotaUsage(userId, \"content\"),\n        this.getTypeQuotaUsage(userId, \"title\"),\n      ]);\n\n      const currentTier =\n        typeof userTierData === \"object\" ? userTierData.tier : userTierData;\n      const tierConfig = tierSettings.config[currentTier];\n\n      // Calculate usage and remaining quotas\n      const totalUsage =\n        (imagesUsage || 0) + (contentUsage || 0) + (titleUsage || 0);\n      const remainingImagesQuota = Math.max(\n        0,\n        tierConfig.imagesQuota - (imagesUsage || 0)\n      );\n      const remainingContentQuota = Math.max(\n        0,\n        tierConfig.contentQuota - (contentUsage || 0)\n      );\n      const remainingTitleQuota = Math.max(\n        0,\n        tierConfig.titleQuota - (titleUsage || 0)\n      );\n\n      // Check if subscription is expired\n      const isExpired =\n        userTierData.expirationDate &&\n        new Date(userTierData.expirationDate) < new Date();\n\n      return {\n        currentTier,\n        tierName: tierConfig.name,\n        usage: totalUsage,\n        imagesUsage: imagesUsage || 0,\n        contentUsage: contentUsage || 0,\n        titleUsage: titleUsage || 0,\n        maxQuota: tierConfig.maxQuota,\n        imagesQuota: tierConfig.imagesQuota,\n        contentQuota: tierConfig.contentQuota,\n        titleQuota: tierConfig.titleQuota,\n        remainingImagesQuota,\n        remainingContentQuota,\n        remainingTitleQuota,\n        price: tierConfig.price,\n        features: tierConfig.features,\n        quotaPercentage: ((totalUsage / tierConfig.maxQuota) * 100).toFixed(2),\n        // New fields\n        expirationDays: tierConfig.expirationDays,\n        startDate: userTierData.startDate,\n        expirationDate: userTierData.expirationDate,\n        isExpired,\n        addon1: userTierData.addon1 || false,\n        addon2: userTierData.addon2 || false,\n        addon1_price: tierConfig.addon1_price,\n        addon2_price: tierConfig.addon2_price,\n        addon1_detail: tierConfig.addon1_detail,\n        addon2_detail: tierConfig.addon2_detail,\n      };\n    } catch (error) {\n      console.error(\"Error getting user tier status:\", error);\n      throw error;\n    }\n  }\n\n  async setQuotaToZero(userId) {\n    try {\n      // Set both image and content quotas to their maximum values\n      const usage = {\n        count: Number.MAX_SAFE_INTEGER, // This will effectively make remaining = 0\n        lastUpdated: new Date().toISOString(),\n      };\n\n      // Update both image and content quotas\n      await Promise.all([\n        this.env.USERS_KV.put(\n          `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,\n          JSON.stringify(usage)\n        ),\n        this.env.USERS_KV.put(\n          `${TierService.KEYS.QUOTA_USAGE}:${userId}:content`,\n          JSON.stringify(usage)\n        ),\n      ]);\n\n      // Get updated tier status to confirm\n      const tierStatus = await this.getUserTierStatus(userId);\n\n      // Verify remaining quotas are 0\n      const isQuotaZero =\n        tierStatus.remainingImagesQuota === 0 &&\n        tierStatus.remainingContentQuota === 0;\n\n      return {\n        success: true,\n        isQuotaZero,\n        tierStatus: {\n          remainingImages: tierStatus.remainingImagesQuota,\n          remainingContent: tierStatus.remainingContentQuota,\n          totalImages: tierStatus.imagesQuota,\n          totalContent: tierStatus.contentQuota,\n          currentTier: tierStatus.currentTier,\n        },\n      };\n    } catch (error) {\n      console.error(\"Error setting quota to zero:\", error);\n      throw error;\n    }\n  }\n}\n", "// src/controllers/tierController.js\nimport { TierService } from \"../services/tierService\";\nimport { ResponseService } from \"../services/responseService\";\nimport { ApiKeyService } from \"../services/apiKeyService\";\n\nexport class TierController {\n  constructor(env) {\n    this.env = env;\n    this.tierService = new TierService(env);\n    this.responseService = new ResponseService();\n    this.apiKeyService = new ApiKeyService();\n  }\n\n  async _getUserFromApiKey(apiKey) {\n    if (!apiKey) {\n      throw new Error(\"API Key is required\");\n    }\n    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, \"json\");\n    if (!user) {\n      throw new Error(\"Invalid API Key\");\n    }\n    return user;\n  }\n\n  async getTierSettings(request) {\n    try {\n      const settings = await this.tierService.getTierSettings();\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(settings)),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getUserTierStatus(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const user = await this._getUserFromApiKey(apiKey);\n      const status = await this.tierService.getUserTierStatus(user.id);\n\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(status)),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async updateUserTier(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const { tier } = await request.json();\n\n      if (!tier) {\n        throw new Error(\"Tier is required\");\n      }\n\n      const user = await this._getUserFromApiKey(apiKey);\n      const updatedTierData = await this.tierService.setUserTier(user.id, tier);\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess(\n            {\n              tier: updatedTierData.tier,\n              updatedAt: updatedTierData.updatedAt,\n              currentStatus: updatedTierData.status,\n            },\n            \"User tier updated successfully\"\n          )\n        ),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getQuotaUsage(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const user = await this._getUserFromApiKey(apiKey);\n\n      // Get both quota usage and tier status\n      const [usage, tierStatus] = await Promise.all([\n        this.tierService.getUserQuotaUsage(user.id),\n        this.tierService.getUserTierStatus(user.id),\n      ]);\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess({\n            usage: usage,\n            currentTier: tierStatus.currentTier,\n            tierName: tierStatus.tierName,\n            limit: tierStatus.maxQuota,\n            remaining: tierStatus.remainingQuota,\n            usagePercentage: tierStatus.quotaPercentage + \"%\",\n            price: tierStatus.price,\n            features: tierStatus.features,\n          })\n        ),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async resetQuotaUsage(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const user = await this._getUserFromApiKey(apiKey);\n      const resetData = await this.tierService.resetQuotaUsage(user.id);\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess(\n            resetData,\n            \"Quota usage reset successfully\"\n          )\n        ),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async setQuotaToZero(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const user = await this._getUserFromApiKey(apiKey);\n\n      const result = await this.tierService.setQuotaToZero(user.id);\n\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(result)),\n        { headers: { \"Content-Type\": \"application/json\" } }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n}\n", "// src/routes/tierRoutes.js\nimport { Router } from \"itty-router\";\nimport { TierController } from \"../controllers/tierController\";\nimport { TierService } from \"../services/tierService\";\n\nconst defaultTierConfig = {\n  free: {\n    name: \"Free Tier\",\n    maxQuota: 1000,\n    imagesQuota: 1000,\n    contentQuota: 1000,\n    titleQuota: 1000,\n    price: 0,\n    expirationDays: 30,\n    addon1: false,\n    addon2: false,\n    addon1_price: 4.99,\n    addon2_price: 9.99,\n    addon1_detail: [\n      \"Enhanced resolution\",\n      \"Advanced filters\",\n      \"Batch processing\",\n    ],\n    addon2_detail: [\"Premium support\", \"1-on-1 training\", \"Custom integration\"],\n    features: [\"Basic API access\", \"Community support\"],\n  },\n  medium: {\n    name: \"Medium Tier\",\n    maxQuota: 10000,\n    imagesQuota: 10000,\n    contentQuota: 10000,\n    titleQuota: 10000,\n    price: 9.99,\n    expirationDays: 30,\n    addon1: false,\n    addon2: false,\n    addon1_price: 9.99,\n    addon2_price: 19.99,\n    addon1_detail: [\n      \"Enhanced resolution\",\n      \"Advanced filters\",\n      \"Batch processing\",\n      \"Priority processing\",\n    ],\n    addon2_detail: [\n      \"Premium support\",\n      \"Weekly training\",\n      \"Custom integration\",\n      \"API consultation\",\n    ],\n    features: [\"Increased quota\", \"Email support\"],\n  },\n  high: {\n    name: \"High Tier\",\n    maxQuota: 1000000,\n    imagesQuota: 1000000,\n    contentQuota: 1000000,\n    titleQuota: 1000000,\n    price: 49.99,\n    expirationDays: 30,\n    addon1: false,\n    addon2: false,\n    addon1_price: 19.99,\n    addon2_price: 39.99,\n    addon1_detail: [\n      \"Enhanced resolution\",\n      \"Advanced filters\",\n      \"Batch processing\",\n      \"Priority processing\",\n      \"Custom models\",\n    ],\n    addon2_detail: [\n      \"Premium support\",\n      \"Daily training\",\n      \"Custom integration\",\n      \"Dedicated manager\",\n      \"24/7 phone support\",\n    ],\n    features: [\"Maximum quota\", \"Priority support\", \"24/7 phone support\"],\n  },\n};\n\nexport function createTierRouter(env) {\n  const router = Router({ base: \"/api/tiers\" });\n  const tierController = new TierController(env);\n\n  // Public endpoint to view tier settings\n  router.get(\"/settings\", (request) => tierController.getTierSettings(request));\n\n  // Protected endpoints requiring API key\n  router.get(\"/status\", (request) => tierController.getUserTierStatus(request));\n\n  router.put(\"/upgrade\", (request) => tierController.updateUserTier(request));\n\n  router.get(\"/quota\", (request) => tierController.getQuotaUsage(request));\n\n  router.post(\"/quota/reset\", (request) =>\n    tierController.resetQuotaUsage(request)\n  );\n\n  router.post(\"/quota/zero\", (request) =>\n    tierController.setQuotaToZero(request)\n  );\n\n  router.post(\"/settings/force-update\", async (request) => {\n    const tierService = new TierService(env);\n    await env.USERS_KV.delete(`${TierService.KEYS.SETTINGS}:tiers`);\n    const settings = await tierService.initializeTierSettings();\n    return new Response(JSON.stringify({ success: true, data: settings }), {\n      headers: { \"Content-Type\": \"application/json\" },\n    });\n  });\n\n  return router;\n}\n", "// src/routes/debugRoutes.js\nimport { Router } from \"itty-router\";\nimport { EmailQueueService } from \"../services/emailQueueService\";\n\nexport function createDebugRouter(env) {\n  const router = Router({ base: \"/api/debug\" });\n  const emailQueueService = new EmailQueueService(env);\n\n  router.post(\"/email-queue/cleanup\", async () => {\n    try {\n      const results = await emailQueueService.cleanupQueue();\n      const newStatus = await emailQueueService.getQueueStatus();\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: \"Queue cleanup completed\",\n          results,\n          currentStatus: newStatus,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  // Get queue status with detailed information\n  router.get(\"/email-queue\", async () => {\n    try {\n      const status = await emailQueueService.getQueueStatus();\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: status,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  router.get(\"/cron-debug\", async (request) => {\n    const emailQueueService = new EmailQueueService(env);\n    const debugInfo = await emailQueueService.getDebugInfo();\n\n    return new Response(\n      JSON.stringify({\n        success: true,\n        data: debugInfo,\n      }),\n      {\n        headers: { \"Content-Type\": \"application/json\" },\n      }\n    );\n  });\n\n  // Process queue with results\n  router.post(\"/process-queue\", async () => {\n    try {\n      const results = await emailQueueService.processQueue();\n      const newStatus = await emailQueueService.getQueueStatus();\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: \"Queue processing completed\",\n          results,\n          currentStatus: newStatus,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  // Clear queue\n  router.delete(\"/email-queue\", async () => {\n    try {\n      const clearedCount = await emailQueueService.clearQueue();\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: `Cleared ${clearedCount} items from queue`,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  router.get(\"/email-queue/status\", async (request, env) => {\n    try {\n      const emailQueueService = new EmailQueueService(env);\n      const status = await emailQueueService.getQueueStatus();\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: status,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      console.error(\"Error getting queue status:\", error);\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  // Add debug endpoint for environment variables\n  router.get(\"/email-queue/debug\", async (request, env) => {\n    try {\n      const emailQueueService = new EmailQueueService(env);\n      const debugInfo = await emailQueueService.getDebugInfo();\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: debugInfo,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n          timestamp: new Date().toISOString(),\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  return router;\n}\n", "// src/services/paypalService.js\nimport { TierService } from \"./tierService.js\";\n\nexport class PayPalService {\n  constructor(env) {\n    this.env = env;\n    this.clientId = env.PAYPAL_CLIENT_ID;\n    this.clientSecret = env.PAYPAL_CLIENT_SECRET;\n    this.baseURL =\n      env.PAYPAL_SANDBOX === \"true\"\n        ? \"https://api-m.sandbox.paypal.com\"\n        : \"https://api-m.paypal.com\";\n    this.tierService = new TierService(env);\n  }\n\n  async createProduct(tierName) {\n    try {\n      const accessToken = await this.getAccessToken();\n      const productData = {\n        name: `${tierName} Subscription`,\n        description: `Access to ${tierName} tier features`,\n        type: \"SERVICE\",\n        category: \"SOFTWARE\",\n      };\n\n      const response = await fetch(`${this.baseURL}/v1/catalogs/products`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${accessToken}`,\n          \"PayPal-Request-Id\": `prod_${Date.now()}`,\n        },\n        body: JSON.stringify(productData),\n      });\n\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.message || \"Failed to create product\");\n      }\n\n      return data.id;\n    } catch (error) {\n      console.error(\"Error creating PayPal product:\", error);\n      throw error;\n    }\n  }\n\n  async createSubscription(\n    userId,\n    tier,\n    addons = { addon1: false, addon2: false }\n  ) {\n    try {\n      console.log(\n        \"Creating subscription for tier:\",\n        tier,\n        \"with addons:\",\n        addons\n      );\n\n      // Get tier settings to get the price\n      const tierSettings = await this.tierService.getTierSettings();\n      console.log(\"Tier settings:\", tierSettings);\n\n      if (!tierSettings?.config?.[tier]) {\n        throw new Error(`Invalid tier: ${tier}`);\n      }\n\n      const tierConfig = tierSettings.config[tier];\n      let basePrice = tierConfig.price;\n\n      // Calculate total price including selected add-ons\n      let totalPrice = basePrice;\n      if (addons.addon1) {\n        totalPrice += tierConfig.addon1_price;\n      }\n      if (addons.addon2) {\n        totalPrice += tierConfig.addon2_price;\n      }\n\n      // Create PayPal product with add-ons information\n      const productName = `${tierConfig.name}${\n        addons.addon1 ? \" + Addon 1\" : \"\"\n      }${addons.addon2 ? \" + Addon 2\" : \"\"}`;\n      const productDescription = this._generateProductDescription(\n        tierConfig,\n        addons\n      );\n\n      const productId = await this.createProduct(\n        productName,\n        productDescription\n      );\n\n      // Create billing plan with the new product\n      const planData = {\n        name: `${productName} Monthly Subscription`,\n        product_id: productId,\n        billing_cycles: [\n          {\n            frequency: {\n              interval_unit: \"MONTH\",\n              interval_count: 1,\n            },\n            tenure_type: \"REGULAR\",\n            sequence: 1,\n            total_cycles: 0,\n            pricing_scheme: {\n              fixed_price: {\n                value: totalPrice.toFixed(2),\n                currency_code: \"USD\",\n              },\n            },\n          },\n        ],\n        payment_preferences: {\n          auto_bill_outstanding: true,\n          setup_fee_failure_action: \"CONTINUE\",\n          payment_failure_threshold: 3,\n        },\n      };\n\n      // Create plan and subscription as before...\n      const plan = await this._createPlan(planData);\n      const subscription = await this._createSubscriptionWithPlan(\n        plan.id,\n        userId\n      );\n\n      // Store subscription details in KV with add-ons information\n      const subscriptionRecord = {\n        subscriptionId: subscription.id,\n        planId: plan.id,\n        productId: productId,\n        tier,\n        basePrice: basePrice,\n        addons: {\n          addon1: addons.addon1\n            ? {\n                enabled: true,\n                price: tierConfig.addon1_price,\n                features: tierConfig.addon1_detail,\n              }\n            : false,\n          addon2: addons.addon2\n            ? {\n                enabled: true,\n                price: tierConfig.addon2_price,\n                features: tierConfig.addon2_detail,\n              }\n            : false,\n        },\n        totalPrice: totalPrice,\n        status: subscription.status,\n        createdAt: new Date().toISOString(),\n      };\n\n      await this.env.USERS_KV.put(\n        `subscription:${userId}`,\n        JSON.stringify(subscriptionRecord)\n      );\n\n      return {\n        ...subscriptionRecord,\n        approvalUrl: subscription.links.find((link) => link.rel === \"approve\")\n          ?.href,\n      };\n    } catch (error) {\n      console.error(\"Error creating PayPal subscription:\", error);\n      throw error;\n    }\n  }\n\n  async updateSubscriptionAddons(subscriptionId, addons) {\n    try {\n      const accessToken = await this.getAccessToken();\n      const subscription = await this.getSubscriptionDetails(subscriptionId);\n      const subscriptionData = await this.getSubscriptionData(subscriptionId);\n\n      const tierConfig = await this.tierService.getTierSettings();\n      const tierSettings = tierConfig.config[subscriptionData.tier];\n\n      // Calculate new price with selected add-ons\n      let newPrice = tierSettings.price;\n      if (addons.addon1) newPrice += tierSettings.addon1_price;\n      if (addons.addon2) newPrice += tierSettings.addon2_price;\n\n      // Update subscription price\n      const response = await fetch(\n        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}/revise`,\n        {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`,\n          },\n          body: JSON.stringify({\n            plan_id: subscription.plan_id,\n            shipping_amount: {\n              currency_code: \"USD\",\n              value: newPrice.toFixed(2),\n            },\n          }),\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update subscription\");\n      }\n\n      // Update local subscription record\n      await this.updateSubscriptionRecord(subscriptionId, {\n        addons,\n        totalPrice: newPrice,\n      });\n\n      return {\n        success: true,\n        subscriptionId,\n        newPrice,\n        addons,\n      };\n    } catch (error) {\n      console.error(\"Error updating subscription addons:\", error);\n      throw error;\n    }\n  }\n\n  async getAccessToken() {\n    try {\n      const response = await fetch(`${this.baseURL}/v1/oauth2/token`, {\n        method: \"POST\",\n        headers: {\n          Accept: \"application/json\",\n          Authorization: `Basic ${btoa(\n            `${this.clientId}:${this.clientSecret}`\n          )}`,\n          \"Content-Type\": \"application/x-www-form-urlencoded\",\n        },\n        body: \"grant_type=client_credentials\",\n      });\n\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error_description || \"Failed to get access token\");\n      }\n\n      return data.access_token;\n    } catch (error) {\n      console.error(\"PayPal access token error:\", error);\n      throw error;\n    }\n  }\n\n  async getSubscriptionStatus(subscriptionId) {\n    try {\n      // Get PayPal access token\n      const accessToken = await this.getAccessToken();\n\n      // Fetch subscription details from PayPal\n      const response = await fetch(\n        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,\n        {\n          method: \"GET\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${accessToken}`,\n          },\n        }\n      );\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.message || \"Failed to get subscription status\");\n      }\n\n      const subscription = await response.json();\n\n      // Find the subscription in our KV store\n      const { keys } = await this.env.USERS_KV.list({\n        prefix: \"subscription:\",\n      });\n\n      let localSubscription = null;\n      let userId = null;\n\n      // Search for the subscription in our KV store\n      for (const key of keys) {\n        const record = await this.env.USERS_KV.get(key.name, \"json\");\n        if (record && record.subscriptionId === subscriptionId) {\n          localSubscription = record;\n          userId = key.name.split(\":\")[1];\n          break;\n        }\n      }\n\n      // Combine PayPal data with our local data\n      return {\n        subscriptionId: subscription.id,\n        status: subscription.status,\n        planId: subscription.plan_id,\n        startTime: subscription.start_time,\n        nextBillingTime: subscription.billing_info?.next_billing_time,\n        lastPaymentTime: subscription.billing_info?.last_payment?.time,\n        failedPayments: subscription.billing_info?.failed_payments_count || 0,\n        tier: localSubscription?.tier,\n        price: localSubscription?.price,\n        localStatus: localSubscription?.status,\n        createdAt: localSubscription?.createdAt,\n        activatedAt: localSubscription?.activatedAt,\n        lastUpdated: new Date().toISOString(),\n      };\n    } catch (error) {\n      console.error(\"Error getting subscription status:\", error);\n      throw error;\n    }\n  }\n\n  async activateSubscription(subscriptionId) {\n    try {\n      // Get PayPal subscription details\n      const accessToken = await this.getAccessToken();\n      const response = await fetch(\n        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,\n        {\n          headers: {\n            Authorization: `Bearer ${accessToken}`,\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      const subscription = await response.json();\n\n      if (!response.ok) {\n        throw new Error(\n          subscription.message || \"Failed to get subscription details\"\n        );\n      }\n\n      // Find the subscription in our KV store\n      const { keys } = await this.env.USERS_KV.list({\n        prefix: \"subscription:\",\n      });\n      let subscriptionRecord = null;\n      let userId = null;\n\n      for (const key of keys) {\n        const record = await this.env.USERS_KV.get(key.name, \"json\");\n        if (record && record.subscriptionId === subscriptionId) {\n          subscriptionRecord = record;\n          userId = key.name.split(\":\")[1];\n          break;\n        }\n      }\n\n      if (!subscriptionRecord || !userId) {\n        throw new Error(\"Subscription record not found\");\n      }\n\n      // Update subscription status\n      const updatedRecord = {\n        ...subscriptionRecord,\n        status: subscription.status,\n        activatedAt: new Date().toISOString(),\n        paypalStatus: subscription.status,\n        lastUpdated: new Date().toISOString(),\n      };\n\n      // Update the record in KV\n      await this.env.USERS_KV.put(\n        `subscription:${userId}`,\n        JSON.stringify(updatedRecord)\n      );\n\n      return {\n        success: true,\n        subscription: updatedRecord,\n      };\n    } catch (error) {\n      console.error(\"Error activating subscription:\", error);\n      throw error;\n    }\n  }\n\n  _generateProductDescription(tierConfig, addons) {\n    let description = `${tierConfig.name} features:\\n`;\n    description += tierConfig.features.join(\", \") + \"\\n\";\n\n    if (addons.addon1) {\n      description += \"\\nAddon 1 features:\\n\";\n      description += tierConfig.addon1_detail.join(\", \");\n    }\n\n    if (addons.addon2) {\n      description += \"\\nAddon 2 features:\\n\";\n      description += tierConfig.addon2_detail.join(\", \");\n    }\n\n    return description;\n  }\n}\n", "// src/controllers/subscriptionController.js\nimport { PayPalService } from \"../services/paypalService.js\"; // Note the .js extension\nimport { TierService } from \"../services/tierService.js\";\nimport { ResponseService } from \"../services/responseService.js\";\n\nexport class SubscriptionController {\n  constructor(env) {\n    this.env = env;\n    this.paypalService = new PayPalService(env);\n    this.tierService = new TierService(env);\n    this.responseService = new ResponseService();\n  }\n\n  async createSubscription(request) {\n    try {\n      console.log(\"Starting subscription creation...\");\n\n      // Log headers for debugging\n      const headers = {};\n      request.headers.forEach((value, key) => {\n        headers[key] = value;\n      });\n      console.log(\"Request headers:\", headers);\n\n      const apiKey = request.headers.get(\"x-sps-key\");\n      console.log(\"API Key present:\", !!apiKey);\n\n      if (!apiKey) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: \"API key is required in x-sps-key header\",\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // Get user from API key\n      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, \"json\");\n      console.log(\"User found:\", !!user);\n\n      if (!user) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: \"Invalid API key\",\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // Get tier settings first to validate\n      const tierSettings = await this.tierService.getTierSettings();\n      console.log(\"Tier settings:\", tierSettings);\n\n      // Get request body\n      const body = await request.json();\n      console.log(\"Request body:\", body);\n\n      const { tier, addons = { addon1: false, addon2: false } } = body;\n      // Validate addons\n      if (addons.addon1 !== undefined && typeof addons.addon1 !== \"boolean\") {\n        throw new Error(\"addon1 must be a boolean value\");\n      }\n      if (addons.addon2 !== undefined && typeof addons.addon2 !== \"boolean\") {\n        throw new Error(\"addon2 must be a boolean value\");\n      }\n\n      if (!tier) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: \"Tier is required\",\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // Validate tier exists in settings\n      if (!tierSettings?.config?.[tier]) {\n        return new Response(\n          JSON.stringify({\n            success: false,\n            message: `Invalid tier: ${tier}. Available tiers: ${Object.keys(\n              tierSettings?.config || {}\n            ).join(\", \")}`,\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        );\n      }\n\n      // Log PayPal environment variables\n      console.log(\"PayPal config:\", {\n        sandboxMode: this.env.PAYPAL_SANDBOX === \"true\",\n        hasClientId: !!this.env.PAYPAL_CLIENT_ID,\n        hasClientSecret: !!this.env.PAYPAL_CLIENT_SECRET,\n        appUrl: this.env.APP_URL,\n      });\n\n      //   const { tier, addons = { addon1: false, addon2: false } } = body;\n\n      // Create PayPal subscription\n      console.log(\"Creating PayPal subscription for tier:\", tier);\n      const subscription = await this.paypalService.createSubscription(\n        user.id,\n        tier,\n        addons\n      );\n\n      console.log(\"Subscription created:\", subscription);\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: {\n            subscriptionId: subscription.subscriptionId,\n            approvalUrl: subscription.approvalUrl,\n            tier: subscription.tier,\n            basePrice: subscription.basePrice,\n            addons: subscription.addons,\n            totalPrice: subscription.totalPrice,\n            status: subscription.status,\n          },\n        }),\n        {\n          status: 200,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      console.error(\"Subscription creation error:\", error);\n      return new Response(\n        JSON.stringify({\n          success: false,\n          message: error.message || \"Failed to create subscription\",\n          details: error.stack,\n        }),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  // Add new endpoint to update addons\n  async updateSubscriptionAddons(request) {\n    try {\n      const apiKey = request.headers.get(\"x-sps-key\");\n      const { subscriptionId, addons } = await request.json();\n\n      if (!apiKey) {\n        throw new Error(\"API key is required\");\n      }\n\n      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, \"json\");\n      if (!user) {\n        throw new Error(\"Invalid API key\");\n      }\n\n      const result = await this.paypalService.updateSubscriptionAddons(\n        subscriptionId,\n        addons\n      );\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: result,\n        }),\n        {\n          status: 200,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      // ... error handling ...\n    }\n  }\n\n  async getTestEndpoint() {\n    try {\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: \"Subscription router is working\",\n          env: {\n            hasPayPalConfig: !!(\n              this.env.PAYPAL_CLIENT_ID && this.env.PAYPAL_CLIENT_SECRET\n            ),\n            sandbox: this.env.PAYPAL_SANDBOX === \"true\",\n            appUrl: this.env.APP_URL,\n          },\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async handleSubscriptionSuccess(request) {\n    try {\n      const url = new URL(request.url);\n      const subscriptionId = url.searchParams.get(\"subscription_id\");\n\n      if (!subscriptionId) {\n        throw new Error(\"Subscription ID is required\");\n      }\n\n      console.log(\"Processing successful subscription:\", subscriptionId);\n\n      // Activate the subscription\n      const result = await this.paypalService.activateSubscription(\n        subscriptionId\n      );\n\n      // Redirect to frontend with success status\n      const redirectUrl = new URL(\"/subscription/success\", this.env.APP_URL);\n      redirectUrl.searchParams.set(\"status\", \"success\");\n      redirectUrl.searchParams.set(\"subscription_id\", subscriptionId);\n\n      return Response.redirect(redirectUrl.toString(), 302);\n    } catch (error) {\n      console.error(\"Error handling subscription success:\", error);\n\n      // Redirect to frontend with error status\n      const redirectUrl = new URL(\"/subscription/error\", this.env.APP_URL);\n      redirectUrl.searchParams.set(\"error\", error.message);\n\n      return Response.redirect(redirectUrl.toString(), 302);\n    }\n  }\n}\n", "// src/routes/subscriptionRoutes.js\nimport { Router } from \"itty-router\";\nimport { SubscriptionController } from \"../controllers/subscriptionController.js\";\n\nexport function createSubscriptionRouter(env) {\n  const router = Router({ base: \"/api/subscriptions\" });\n  const controller = new SubscriptionController(env);\n\n  // Test endpoint\n  router.get(\"/test\", async () => {\n    try {\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: \"Subscription router is working\",\n          environment: {\n            hasPayPalConfig: !!(\n              env.PAYPAL_CLIENT_ID && env.PAYPAL_CLIENT_SECRET\n            ),\n            sandbox: env.PAYPAL_SANDBOX === \"true\",\n            appUrl: env.APP_URL,\n          },\n        }),\n        {\n          status: 200,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  // Create subscription endpoint\n  router.post(\"/\", (request) => controller.createSubscription(request));\n\n  // Success callback\n  router.get(\"/success\", (request) =>\n    controller.handleSubscriptionSuccess(request)\n  );\n\n  // Get subscription status\n  router.get(\"/status/:subscriptionId\", async (request) => {\n    try {\n      const subscriptionId = request.params.subscriptionId;\n      const subscription = await controller.paypalService.getSubscriptionStatus(\n        subscriptionId\n      );\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          data: subscription,\n        }),\n        {\n          status: 200,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  });\n\n  router.put(\"/addons\", (request) =>\n    controller.updateSubscriptionAddons(request)\n  );\n\n  // Handle 404s\n  router.all(\n    \"*\",\n    () =>\n      new Response(\n        JSON.stringify({\n          success: false,\n          error: \"Not Found\",\n        }),\n        {\n          status: 404,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      )\n  );\n\n  return router;\n}\n", "// src/swagger/swagger.js\nexport const swaggerDocument = {\n  openapi: \"3.0.0\",\n  info: {\n    title: \"API Documentation\",\n    version: \"1.0.0\",\n  },\n  paths: {\n    \"/api/users/\": {\n      post: {\n        summary: \"Create a new user\",\n        description:\n          \"Create a new user with auto-generated username and password. Credentials will be sent via email.\",\n        tags: [\"Users\"],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"email\", \"domain\"],\n                properties: {\n                  email: {\n                    type: \"string\",\n                    format: \"email\",\n                    description: \"User email where credentials will be sent\",\n                    example: \"<EMAIL>\",\n                  },\n                  domain: {\n                    type: \"string\",\n                    description: \"User domain (must be unique)\",\n                    example: \"example.com\",\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          201: {\n            description: \"User created successfully with generated credentials\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        id: {\n                          type: \"string\",\n                          example: \"550e8400-e29b-41d4-a716-446655440000\",\n                        },\n                        email: {\n                          type: \"string\",\n                          example: \"<EMAIL>\",\n                        },\n                        domain: {\n                          type: \"string\",\n                          example: \"example.com\",\n                        },\n                        api_key: {\n                          type: \"string\",\n                          example: \"550e8400-e29b-41d4-a716-446655440000\",\n                        },\n                        credentials: {\n                          type: \"object\",\n                          properties: {\n                            password: {\n                              type: \"string\",\n                              description: \"Auto-generated password\",\n                              example: \"aB3$xK9#mP2&\",\n                            },\n                            apiKey: {\n                              type: \"string\",\n                              description: \"API key for authentication\",\n                              example: \"550e8400-e29b-41d4-a716-446655440000\",\n                            },\n                          },\n                        },\n                        createdAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                        },\n                        updatedAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                        },\n                      },\n                    },\n                    message: {\n                      type: \"string\",\n                      example:\n                        \"User created successfully. Credentials have been sent to your email.\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Domain already registered or validation error\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: false },\n                    message: {\n                      type: \"string\",\n                      example:\n                        \"Domain is already registered. Each domain can only have one API key.\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/users/validatekey\": {\n      post: {\n        summary: \"Validate API Key\",\n        description: \"Validates whether a provided API key is valid and active\",\n        tags: [\"Users\"],\n        security: [\n          {\n            ApiKeyAuth: [],\n          },\n        ],\n        responses: {\n          200: {\n            description: \"API key validation response\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      description: \"Whether the operation was successful\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        isValid: {\n                          type: \"boolean\",\n                          description: \"Whether the API key is valid\",\n                          example: true,\n                        },\n                        message: {\n                          type: \"string\",\n                          description:\n                            \"Additional information about the validation result\",\n                          example: null,\n                        },\n                      },\n                    },\n                    timestamp: {\n                      type: \"string\",\n                      format: \"date-time\",\n                      description: \"Timestamp of the response\",\n                      example: \"2024-12-07T12:00:00.000Z\",\n                    },\n                  },\n                },\n                examples: {\n                  valid: {\n                    value: {\n                      success: true,\n                      data: {\n                        isValid: true,\n                      },\n                      timestamp: \"2024-12-07T12:00:00.000Z\",\n                    },\n                    summary: \"Valid API Key\",\n                  },\n                  invalid: {\n                    value: {\n                      success: true,\n                      data: {\n                        isValid: false,\n                        message: \"Invalid API Key\",\n                      },\n                      timestamp: \"2024-12-07T12:00:00.000Z\",\n                    },\n                    summary: \"Invalid API Key\",\n                  },\n                  missing: {\n                    value: {\n                      success: true,\n                      data: {\n                        isValid: false,\n                        message: \"API Key is required in x-sps-key header\",\n                      },\n                      timestamp: \"2024-12-07T12:00:00.000Z\",\n                    },\n                    summary: \"Missing API Key\",\n                  },\n                },\n              },\n            },\n          },\n          500: {\n            description: \"Server error occurred\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: false,\n                    },\n                    message: {\n                      type: \"string\",\n                      example: \"Server error occurred\",\n                    },\n                    timestamp: {\n                      type: \"string\",\n                      format: \"date-time\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/users/userdetail\": {\n      get: {\n        summary: \"Get user details\",\n        tags: [\"Users\"],\n        security: [\n          {\n            ApiKeyAuth: [],\n          },\n        ],\n        responses: {\n          200: {\n            description: \"User details retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  $ref: \"#/components/schemas/UserResponse\",\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/tiers/quota/zero\": {\n      post: {\n        summary: \"Set quota to zero\",\n        tags: [\"Tiers\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Sets both image and content quotas to their maximum values, effectively making remaining quota zero\",\n        responses: {\n          200: {\n            description: \"Quota set to zero successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        isQuotaZero: {\n                          type: \"boolean\",\n                          description: \"Confirms if quota is set to zero\",\n                          example: true,\n                        },\n                        tierStatus: {\n                          type: \"object\",\n                          properties: {\n                            remainingImages: {\n                              type: \"number\",\n                              description: \"Remaining image quota\",\n                              example: 0,\n                            },\n                            remainingContent: {\n                              type: \"number\",\n                              description: \"Remaining content quota\",\n                              example: 0,\n                            },\n                            totalImages: {\n                              type: \"number\",\n                              description: \"Total image quota for tier\",\n                              example: 1000,\n                            },\n                            totalContent: {\n                              type: \"number\",\n                              description: \"Total content quota for tier\",\n                              example: 1000,\n                            },\n                            currentTier: {\n                              type: \"string\",\n                              description: \"Current tier name\",\n                              example: \"free\",\n                            },\n                          },\n                        },\n                      },\n                    },\n                    timestamp: {\n                      type: \"string\",\n                      format: \"date-time\",\n                      example: \"2024-12-08T12:00:00.000Z\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Error setting quota to zero\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: false,\n                    },\n                    message: {\n                      type: \"string\",\n                      example: \"Error setting quota to zero\",\n                    },\n                    timestamp: {\n                      type: \"string\",\n                      format: \"date-time\",\n                      example: \"2024-12-08T12:00:00.000Z\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/tiers/settings\": {\n      get: {\n        summary: \"Get tier settings\",\n        tags: [\"Tiers\"],\n        description:\n          \"Retrieve all tier configurations including quotas and features\",\n        responses: {\n          200: {\n            description: \"Tier settings retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        config: {\n                          type: \"object\",\n                          properties: {\n                            free: { $ref: \"#/components/schemas/TierConfig\" },\n                            medium: { $ref: \"#/components/schemas/TierConfig\" },\n                            high: { $ref: \"#/components/schemas/TierConfig\" },\n                          },\n                        },\n                        updatedAt: { type: \"string\", format: \"date-time\" },\n                        version: { type: \"string\" },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/tiers/status\": {\n      get: {\n        summary: \"Get user tier status\",\n        tags: [\"Tiers\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Get current user tier status including usage and quota information\",\n        responses: {\n          200: {\n            description: \"User tier status retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  $ref: \"#/components/schemas/TierStatusResponse\",\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/tiers/upgrade\": {\n      put: {\n        summary: \"Upgrade user tier\",\n        tags: [\"Tiers\"],\n        security: [{ ApiKeyAuth: [] }],\n        description: \"Upgrade user to a different tier with optional add-ons\",\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"tier\"],\n                properties: {\n                  tier: {\n                    type: \"string\",\n                    enum: [\"free\", \"medium\", \"high\"],\n                    description: \"The tier to upgrade to\",\n                  },\n                  addon1: {\n                    type: \"boolean\",\n                    description: \"Enable addon1 features\",\n                    default: false,\n                  },\n                  addon2: {\n                    type: \"boolean\",\n                    description: \"Enable addon2 features\",\n                    default: false,\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"Tier upgraded successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        tier: { type: \"string\" },\n                        startDate: {\n                          type: \"string\",\n                          format: \"date-time\",\n                        },\n                        expirationDate: {\n                          type: \"string\",\n                          format: \"date-time\",\n                        },\n                        addon1: { type: \"boolean\" },\n                        addon2: { type: \"boolean\" },\n                        currentStatus: {\n                          type: \"object\",\n                          properties: {\n                            tierName: { type: \"string\" },\n                            maxQuota: { type: \"number\" },\n                            imagesQuota: { type: \"number\" },\n                            contentQuota: { type: \"number\" },\n                            price: { type: \"number\" },\n                            features: {\n                              type: \"array\",\n                              items: { type: \"string\" },\n                            },\n                            addon1_price: { type: \"number\" },\n                            addon2_price: { type: \"number\" },\n                            addon1_detail: {\n                              type: \"array\",\n                              items: { type: \"string\" },\n                            },\n                            addon2_detail: {\n                              type: \"array\",\n                              items: { type: \"string\" },\n                            },\n                          },\n                        },\n                      },\n                    },\n                    message: { type: \"string\" },\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Invalid request\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: false },\n                    message: {\n                      type: \"string\",\n                      example: \"Invalid tier specified\",\n                    },\n                    timestamp: {\n                      type: \"string\",\n                      format: \"date-time\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/tiers/quota\": {\n      get: {\n        summary: \"Get quota usage\",\n        tags: [\"Tiers\"],\n        security: [{ ApiKeyAuth: [] }],\n        description: \"Get current quota usage information\",\n        responses: {\n          200: {\n            description: \"Quota usage retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        usage: { type: \"number\" },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/tiers/quota/reset\": {\n      post: {\n        summary: \"Reset quota usage\",\n        tags: [\"Tiers\"],\n        security: [{ ApiKeyAuth: [] }],\n        description: \"Reset the quota usage counter to zero\",\n        responses: {\n          200: {\n            description: \"Quota reset successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        count: { type: \"number\" },\n                        lastReset: { type: \"string\", format: \"date-time\" },\n                      },\n                    },\n                    message: { type: \"string\" },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/users/activate\": {\n      post: {\n        summary: \"Activate user API key\",\n        tags: [\"Users\"],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"license\"],\n                properties: {\n                  license: {\n                    type: \"string\",\n                    description: \"License key to activate\",\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"API key activated successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    status: {\n                      type: \"boolean\",\n                    },\n                    message: {\n                      type: \"string\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/users/license/status\": {\n      post: {\n        summary: \"Check license activation status\",\n        tags: [\"Users\"],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"license\"],\n                properties: {\n                  license: {\n                    type: \"string\",\n                    description: \"License key to check\",\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"License status retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        isValid: {\n                          type: \"boolean\",\n                        },\n                        status: {\n                          type: \"string\",\n                          enum: [\"active\", \"pending\"],\n                        },\n                        activatedAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          nullable: true,\n                        },\n                        domain: {\n                          type: \"string\",\n                        },\n                        message: {\n                          type: \"string\",\n                        },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/usage/images\": {\n      post: {\n        summary: \"Track image generation usage\",\n        tags: [\"Usage\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Increment the image generation usage counter for the user\",\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"source\", \"timestamp\"],\n                properties: {\n                  source: {\n                    type: \"string\",\n                    description: \"Source of the image generation\",\n                    example: \"profile-picture\",\n                  },\n                  timestamp: {\n                    type: \"string\",\n                    format: \"date-time\",\n                    description: \"When the image was generated\",\n                    example: \"2024-12-08T02:30:00.000Z\",\n                  },\n                  metadata: {\n                    type: \"object\",\n                    description: \"Optional additional information\",\n                    example: {\n                      size: \"1024x1024\",\n                      model: \"stable-diffusion\",\n                    },\n                  },\n                },\n              },\n              examples: {\n                basic: {\n                  summary: \"Basic usage\",\n                  value: {\n                    source: \"profile-picture\",\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                  },\n                },\n                withMetadata: {\n                  summary: \"With metadata\",\n                  value: {\n                    source: \"product-image\",\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                    metadata: {\n                      size: \"1024x1024\",\n                      model: \"stable-diffusion\",\n                      style: \"photorealistic\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          201: {\n            description: \"Usage tracked successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    message: { type: \"string\" },\n                    data: { type: \"object\" },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/usage/content\": {\n      post: {\n        summary: \"Track content generation usage\",\n        tags: [\"Usage\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Increment the content generation usage counter for the user\",\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"source\", \"timestamp\"],\n                properties: {\n                  source: {\n                    type: \"string\",\n                    description: \"Source of the content generation\",\n                    example: \"product-description\",\n                  },\n                  timestamp: {\n                    type: \"string\",\n                    format: \"date-time\",\n                    description: \"When the content was generated\",\n                    example: \"2024-12-08T02:30:00.000Z\",\n                  },\n                  metadata: {\n                    type: \"object\",\n                    description: \"Optional additional information\",\n                    example: {\n                      wordCount: 500,\n                      language: \"en\",\n                    },\n                  },\n                },\n              },\n              examples: {\n                basic: {\n                  summary: \"Basic usage\",\n                  value: {\n                    source: \"product-description\",\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                  },\n                },\n                withMetadata: {\n                  summary: \"With metadata\",\n                  value: {\n                    source: \"blog-post\",\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                    metadata: {\n                      wordCount: 1500,\n                      language: \"en\",\n                      category: \"technology\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          201: {\n            description: \"Usage tracked successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    message: { type: \"string\" },\n                    data: { type: \"object\" },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/usage/stats\": {\n      get: {\n        summary: \"Get usage statistics\",\n        tags: [\"Usage\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Get detailed usage statistics for images, content, and title generation\",\n        responses: {\n          200: {\n            description: \"Usage statistics retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: true },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        images: {\n                          type: \"object\",\n                          properties: {\n                            used: { type: \"number\", example: 50 },\n                            remaining: { type: \"number\", example: 950 },\n                            total: { type: \"number\", example: 1000 },\n                            percentageUsed: { type: \"string\", example: \"5.00\" },\n                          },\n                        },\n                        content: {\n                          type: \"object\",\n                          properties: {\n                            used: { type: \"number\", example: 30 },\n                            remaining: { type: \"number\", example: 970 },\n                            total: { type: \"number\", example: 1000 },\n                            percentageUsed: { type: \"string\", example: \"3.00\" },\n                          },\n                        },\n                        title: {\n                          type: \"object\",\n                          properties: {\n                            used: { type: \"number\", example: 20 },\n                            remaining: { type: \"number\", example: 980 },\n                            total: { type: \"number\", example: 1000 },\n                            percentageUsed: { type: \"string\", example: \"2.00\" },\n                          },\n                        },\n                        tier: {\n                          type: \"object\",\n                          properties: {\n                            name: { type: \"string\", example: \"Medium Tier\" },\n                            current: { type: \"string\", example: \"medium\" },\n                            expirationDate: {\n                              type: \"string\",\n                              example: \"2024-12-31T23:59:59Z\",\n                            },\n                            isExpired: { type: \"boolean\", example: false },\n                            price: { type: \"number\", example: 9.99 },\n                          },\n                        },\n                        addons: {\n                          type: \"object\",\n                          properties: {\n                            addon1: {\n                              type: \"object\",\n                              properties: {\n                                enabled: { type: \"boolean\", example: false },\n                                price: { type: \"number\", example: 4.99 },\n                                features: {\n                                  type: \"array\",\n                                  items: { type: \"string\" },\n                                  example: [\n                                    \"Enhanced resolution\",\n                                    \"Advanced filters\",\n                                  ],\n                                },\n                              },\n                            },\n                            addon2: {\n                              type: \"object\",\n                              properties: {\n                                enabled: { type: \"boolean\", example: false },\n                                price: { type: \"number\", example: 9.99 },\n                                features: {\n                                  type: \"array\",\n                                  items: { type: \"string\" },\n                                  example: [\n                                    \"Premium support\",\n                                    \"1-on-1 training\",\n                                  ],\n                                },\n                              },\n                            },\n                          },\n                        },\n                        features: {\n                          type: \"array\",\n                          items: { type: \"string\" },\n                          example: [\"Basic API access\", \"Community support\"],\n                        },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n          500: {\n            description: \"Internal server error\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: false },\n                    error: { type: \"string\" },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/usage/history\": {\n      get: {\n        summary: \"Get usage history\",\n        tags: [\"Usage\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Retrieve detailed usage history with filtering and pagination options\",\n        parameters: [\n          {\n            name: \"startDate\",\n            in: \"query\",\n            description: \"Filter results from this date (ISO 8601)\",\n            schema: {\n              type: \"string\",\n              format: \"date\",\n              example: \"2024-12-01\",\n            },\n          },\n          {\n            name: \"endDate\",\n            in: \"query\",\n            description: \"Filter results until this date (ISO 8601)\",\n            schema: {\n              type: \"string\",\n              format: \"date\",\n              example: \"2024-12-31\",\n            },\n          },\n          {\n            name: \"type\",\n            in: \"query\",\n            description: \"Filter by usage type\",\n            schema: {\n              type: \"string\",\n              enum: [\"images\", \"content\"],\n            },\n          },\n          {\n            name: \"limit\",\n            in: \"query\",\n            description: \"Number of results per page\",\n            schema: {\n              type: \"integer\",\n              minimum: 1,\n              maximum: 100,\n              default: 100,\n            },\n          },\n          {\n            name: \"page\",\n            in: \"query\",\n            description: \"Page number\",\n            schema: {\n              type: \"integer\",\n              minimum: 1,\n              default: 1,\n            },\n          },\n        ],\n        responses: {\n          200: {\n            description: \"Usage history retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: true },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        entries: {\n                          type: \"array\",\n                          items: {\n                            $ref: \"#/components/schemas/UsageHistoryEntry\",\n                          },\n                        },\n                        pagination: {\n                          type: \"object\",\n                          properties: {\n                            total: { type: \"integer\", example: 50 },\n                            totalPages: { type: \"integer\", example: 5 },\n                            currentPage: { type: \"integer\", example: 1 },\n                            limit: { type: \"integer\", example: 10 },\n                          },\n                        },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/usage/daily\": {\n      get: {\n        summary: \"Get daily usage aggregates\",\n        tags: [\"Usage\"],\n        security: [{ ApiKeyAuth: [] }],\n        description: \"Retrieve daily aggregated usage statistics\",\n        parameters: [\n          {\n            name: \"startDate\",\n            in: \"query\",\n            description: \"Start date for daily stats (ISO 8601)\",\n            schema: {\n              type: \"string\",\n              format: \"date\",\n              example: \"2024-12-01\",\n            },\n          },\n          {\n            name: \"endDate\",\n            in: \"query\",\n            description: \"End date for daily stats (ISO 8601)\",\n            schema: {\n              type: \"string\",\n              format: \"date\",\n              example: \"2024-12-31\",\n            },\n          },\n        ],\n        responses: {\n          200: {\n            description: \"Daily usage statistics retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: true },\n                    data: {\n                      type: \"array\",\n                      items: {\n                        $ref: \"#/components/schemas/DailyUsage\",\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/subscriptions\": {\n      post: {\n        summary: \"Create new PayPal subscription\",\n        description:\n          \"Creates a new PayPal subscription for the specified tier with automatic price fetching from tier settings\",\n        tags: [\"Subscriptions\"],\n        security: [\n          {\n            ApiKeyAuth: [],\n          },\n        ],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"tier\"],\n                properties: {\n                  tier: {\n                    type: \"string\",\n                    enum: [\"medium\", \"high\"],\n                    description:\n                      \"The tier level to subscribe to. Price will be automatically fetched from tier settings.\",\n                  },\n                },\n              },\n              examples: {\n                medium: {\n                  summary: \"Medium tier subscription\",\n                  value: {\n                    tier: \"medium\",\n                  },\n                },\n                high: {\n                  summary: \"High tier subscription\",\n                  value: {\n                    tier: \"high\",\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"Subscription created successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        subscriptionId: {\n                          type: \"string\",\n                          description: \"PayPal subscription identifier\",\n                          example: \"I-BW452GLLEP1G\",\n                        },\n                        approvalUrl: {\n                          type: \"string\",\n                          description: \"PayPal URL for subscription approval\",\n                          example:\n                            \"https://www.sandbox.paypal.com/webapps/billing/subscriptions?token=...\",\n                        },\n                        tier: {\n                          type: \"string\",\n                          description: \"Selected subscription tier\",\n                          example: \"medium\",\n                        },\n                        price: {\n                          type: \"number\",\n                          description: \"Monthly subscription price in USD\",\n                          example: 9.99,\n                        },\n                        status: {\n                          type: \"string\",\n                          description: \"Initial subscription status\",\n                          enum: [\"APPROVAL_PENDING\", \"APPROVED\", \"ACTIVE\"],\n                          example: \"APPROVAL_PENDING\",\n                        },\n                      },\n                    },\n                  },\n                },\n                examples: {\n                  mediumTier: {\n                    summary: \"Medium tier response\",\n                    value: {\n                      success: true,\n                      data: {\n                        subscriptionId: \"I-BW452GLLEP1G\",\n                        approvalUrl:\n                          \"https://www.sandbox.paypal.com/webapps/billing/subscriptions?token=EC-5RT15012PY123456\",\n                        tier: \"medium\",\n                        price: 9.99,\n                        status: \"APPROVAL_PENDING\",\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Error creating subscription\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: false,\n                    },\n                    message: {\n                      type: \"string\",\n                    },\n                  },\n                },\n                examples: {\n                  invalidTier: {\n                    summary: \"Invalid tier specified\",\n                    value: {\n                      success: false,\n                      message: \"Invalid tier specified\",\n                    },\n                  },\n                  missingApiKey: {\n                    summary: \"Missing API key\",\n                    value: {\n                      success: false,\n                      message: \"API key is required in x-sps-key header\",\n                    },\n                  },\n                  paypalError: {\n                    summary: \"PayPal API error\",\n                    value: {\n                      success: false,\n                      message: \"Failed to create PayPal subscription\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/subscriptions/success\": {\n      get: {\n        summary: \"PayPal subscription success callback\",\n        description:\n          \"Endpoint called by PayPal after successful subscription approval\",\n        tags: [\"Subscriptions\"],\n        parameters: [\n          {\n            name: \"subscription_id\",\n            in: \"query\",\n            required: true,\n            schema: {\n              type: \"string\",\n            },\n            description: \"PayPal subscription identifier\",\n          },\n        ],\n        responses: {\n          302: {\n            description: \"Redirects to frontend success page\",\n            headers: {\n              Location: {\n                schema: {\n                  type: \"string\",\n                  example:\n                    \"https://your-app.com/subscription/success?subscription_id=I-BW452GLLEP1G\",\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/subscriptions/webhook\": {\n      post: {\n        summary: \"PayPal webhook endpoint\",\n        description: \"Handles PayPal subscription event notifications\",\n        tags: [\"Subscriptions\"],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                properties: {\n                  event_type: {\n                    type: \"string\",\n                    description: \"PayPal event type\",\n                    example: \"BILLING.SUBSCRIPTION.ACTIVATED\",\n                  },\n                  resource: {\n                    type: \"object\",\n                    description: \"Event resource details\",\n                  },\n                },\n              },\n              examples: {\n                activated: {\n                  summary: \"Subscription activated\",\n                  value: {\n                    event_type: \"BILLING.SUBSCRIPTION.ACTIVATED\",\n                    resource: {\n                      id: \"I-BW452GLLEP1G\",\n                      status: \"ACTIVE\",\n                    },\n                  },\n                },\n                payment: {\n                  summary: \"Payment completed\",\n                  value: {\n                    event_type: \"PAYMENT.SALE.COMPLETED\",\n                    resource: {\n                      id: \"5RT15012PY123456\",\n                      amount: {\n                        total: \"9.99\",\n                        currency: \"USD\",\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"Webhook processed successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    received: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/subscriptions/status/{subscriptionId}\": {\n      get: {\n        summary: \"Get subscription status\",\n        description:\n          \"Retrieves the current status and details of a PayPal subscription\",\n        tags: [\"Subscriptions\"],\n        parameters: [\n          {\n            name: \"subscriptionId\",\n            in: \"path\",\n            required: true,\n            schema: {\n              type: \"string\",\n            },\n            description: \"PayPal subscription identifier\",\n            example: \"I-FBN91UE2890B\",\n          },\n        ],\n        responses: {\n          200: {\n            description: \"Subscription status retrieved successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        subscriptionId: {\n                          type: \"string\",\n                          example: \"I-FBN91UE2890B\",\n                        },\n                        status: {\n                          type: \"string\",\n                          example: \"ACTIVE\",\n                        },\n                        planId: {\n                          type: \"string\",\n                          example: \"P-6L993232BE527384WM5Q53VA\",\n                        },\n                        startTime: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          example: \"2024-12-17T07:32:32Z\",\n                        },\n                        nextBillingTime: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          example: \"2025-01-17T08:00:00Z\",\n                        },\n                        lastPaymentTime: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          example: \"2024-12-17T07:33:23Z\",\n                        },\n                        failedPayments: {\n                          type: \"integer\",\n                          example: 0,\n                        },\n                        tier: {\n                          type: \"string\",\n                          example: \"medium\",\n                        },\n                        price: {\n                          type: \"number\",\n                          example: 9.99,\n                        },\n                        localStatus: {\n                          type: \"string\",\n                          example: \"ACTIVE\",\n                        },\n                        createdAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          example: \"2024-12-17T07:32:35.281Z\",\n                        },\n                        activatedAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          example: \"2024-12-17T08:24.356Z\",\n                        },\n                        lastUpdated: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          example: \"2024-12-17T08:27.870Z\",\n                        },\n                      },\n                    },\n                  },\n                },\n                example: {\n                  success: true,\n                  data: {\n                    subscriptionId: \"I-FBN91UE2890B\",\n                    status: \"ACTIVE\",\n                    planId: \"P-6L993232BE527384WM5Q53VA\",\n                    startTime: \"2024-12-17T07:32:32Z\",\n                    nextBillingTime: \"2025-01-17T08:00:00Z\",\n                    lastPaymentTime: \"2024-12-17T07:33:23Z\",\n                    failedPayments: 0,\n                    tier: \"medium\",\n                    price: 9.99,\n                    localStatus: \"ACTIVE\",\n                    createdAt: \"2024-12-17T07:32:35.281Z\",\n                    activatedAt: \"2024-12-17T08:24.356Z\",\n                    lastUpdated: \"2024-12-17T08:27.870Z\",\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Error retrieving subscription status\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: false,\n                    },\n                    error: {\n                      type: \"string\",\n                      example: \"Failed to get subscription status\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/subscriptions/create\": {\n      post: {\n        summary: \"Create subscription with optional add-ons\",\n        tags: [\"Subscriptions\"],\n        security: [{ ApiKeyAuth: [] }],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"tier\"],\n                properties: {\n                  tier: {\n                    type: \"string\",\n                    enum: [\"free\", \"medium\", \"high\"],\n                    description: \"Subscription tier level\",\n                  },\n                  addons: {\n                    type: \"object\",\n                    properties: {\n                      addon1: {\n                        type: \"boolean\",\n                        description: \"Enable addon 1 features\",\n                        default: false,\n                      },\n                      addon2: {\n                        type: \"boolean\",\n                        description: \"Enable addon 2 features\",\n                        default: false,\n                      },\n                    },\n                  },\n                },\n              },\n              examples: {\n                \"Basic Subscription\": {\n                  value: {\n                    tier: \"medium\",\n                    addons: {\n                      addon1: false,\n                      addon2: false,\n                    },\n                  },\n                },\n                \"Subscription with Add-ons\": {\n                  value: {\n                    tier: \"medium\",\n                    addons: {\n                      addon1: true,\n                      addon2: true,\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"Subscription created successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        subscriptionId: {\n                          type: \"string\",\n                          example: \"I-BW452GLLEP1G\",\n                        },\n                        approvalUrl: {\n                          type: \"string\",\n                          example:\n                            \"https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=EC-5Y366741JN879735H\",\n                        },\n                        tier: {\n                          type: \"string\",\n                          example: \"medium\",\n                        },\n                        basePrice: {\n                          type: \"number\",\n                          example: 9.99,\n                        },\n                        addons: {\n                          type: \"object\",\n                          properties: {\n                            addon1: {\n                              type: \"object\",\n                              properties: {\n                                enabled: {\n                                  type: \"boolean\",\n                                  example: true,\n                                },\n                                price: {\n                                  type: \"number\",\n                                  example: 9.99,\n                                },\n                                features: {\n                                  type: \"array\",\n                                  items: {\n                                    type: \"string\",\n                                  },\n                                  example: [\n                                    \"Enhanced resolution\",\n                                    \"Advanced filters\",\n                                    \"Batch processing\",\n                                    \"Priority processing\",\n                                  ],\n                                },\n                              },\n                            },\n                            addon2: {\n                              type: \"object\",\n                              properties: {\n                                enabled: {\n                                  type: \"boolean\",\n                                  example: false,\n                                },\n                                price: {\n                                  type: \"number\",\n                                  example: 19.99,\n                                },\n                                features: {\n                                  type: \"array\",\n                                  items: {\n                                    type: \"string\",\n                                  },\n                                },\n                              },\n                            },\n                          },\n                        },\n                        totalPrice: {\n                          type: \"number\",\n                          example: 19.98,\n                        },\n                        status: {\n                          type: \"string\",\n                          example: \"APPROVAL_PENDING\",\n                        },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Invalid request\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: false,\n                    },\n                    message: {\n                      type: \"string\",\n                      example: \"Invalid tier specified\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/subscriptions/addons\": {\n      put: {\n        summary: \"Update subscription add-ons\",\n        tags: [\"Subscriptions\"],\n        security: [{ ApiKeyAuth: [] }],\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"subscriptionId\", \"addons\"],\n                properties: {\n                  subscriptionId: {\n                    type: \"string\",\n                    description: \"PayPal subscription ID\",\n                  },\n                  addons: {\n                    type: \"object\",\n                    properties: {\n                      addon1: {\n                        type: \"boolean\",\n                        description: \"Enable/disable addon 1\",\n                      },\n                      addon2: {\n                        type: \"boolean\",\n                        description: \"Enable/disable addon 2\",\n                      },\n                    },\n                  },\n                },\n              },\n              example: {\n                subscriptionId: \"I-BW452GLLEP1G\",\n                addons: {\n                  addon1: true,\n                  addon2: false,\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          200: {\n            description: \"Add-ons updated successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: true,\n                    },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        subscriptionId: {\n                          type: \"string\",\n                          example: \"I-BW452GLLEP1G\",\n                        },\n                        newPrice: {\n                          type: \"number\",\n                          example: 19.98,\n                        },\n                        addons: {\n                          type: \"object\",\n                          properties: {\n                            addon1: {\n                              type: \"boolean\",\n                              example: true,\n                            },\n                            addon2: {\n                              type: \"boolean\",\n                              example: false,\n                            },\n                          },\n                        },\n                      },\n                    },\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Invalid request\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: {\n                      type: \"boolean\",\n                      example: false,\n                    },\n                    message: {\n                      type: \"string\",\n                      example: \"Invalid subscription ID\",\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n    \"/api/usage/title\": {\n      post: {\n        summary: \"Track title generation usage\",\n        tags: [\"Usage\"],\n        security: [{ ApiKeyAuth: [] }],\n        description:\n          \"Increment the title generation usage counter for the user\",\n        requestBody: {\n          required: true,\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                required: [\"source\", \"timestamp\"],\n                properties: {\n                  source: {\n                    type: \"string\",\n                    description: \"Source of the title generation\",\n                    example: \"product-title\",\n                  },\n                  timestamp: {\n                    type: \"string\",\n                    format: \"date-time\",\n                    description: \"When the title was generated\",\n                    example: \"2024-12-08T02:30:00.000Z\",\n                  },\n                  metadata: {\n                    type: \"object\",\n                    description: \"Optional additional information\",\n                    example: {\n                      language: \"en\",\n                      category: \"product\",\n                      length: \"short\",\n                    },\n                  },\n                },\n              },\n              examples: {\n                basic: {\n                  summary: \"Basic usage\",\n                  value: {\n                    source: \"product-title\",\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                  },\n                },\n                withMetadata: {\n                  summary: \"With metadata\",\n                  value: {\n                    source: \"blog-title\",\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                    metadata: {\n                      language: \"en\",\n                      category: \"blog\",\n                      length: \"medium\",\n                      keywords: [\"tech\", \"ai\"],\n                    },\n                  },\n                },\n              },\n            },\n          },\n        },\n        responses: {\n          201: {\n            description: \"Usage tracked successfully\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\" },\n                    message: { type: \"string\" },\n                    data: {\n                      type: \"object\",\n                      properties: {\n                        tracked: { type: \"boolean\" },\n                        currentUsage: { type: \"number\" },\n                        remainingQuota: { type: \"number\" },\n                        timestamp: {\n                          type: \"string\",\n                          format: \"date-time\",\n                        },\n                      },\n                    },\n                  },\n                },\n                example: {\n                  success: true,\n                  message: \"Title generation usage tracked successfully\",\n                  data: {\n                    tracked: true,\n                    currentUsage: 51,\n                    remainingQuota: 949,\n                    timestamp: \"2024-12-08T02:30:00.000Z\",\n                  },\n                },\n              },\n            },\n          },\n          400: {\n            description: \"Error tracking usage\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    success: { type: \"boolean\", example: false },\n                    message: { type: \"string\" },\n                    timestamp: {\n                      type: \"string\",\n                      format: \"date-time\",\n                    },\n                  },\n                },\n                example: {\n                  success: false,\n                  message: \"Invalid request format or quota exceeded\",\n                  timestamp: \"2024-12-08T02:30:00.000Z\",\n                },\n              },\n            },\n          },\n        },\n      },\n    },\n  },\n  components: {\n    schemas: {\n      UserResponse: {\n        type: \"object\",\n        properties: {\n          id: { type: \"string\" },\n          username: { type: \"string\" },\n          email: { type: \"string\" },\n          api_key: { type: \"string\" },\n        },\n      },\n      TierConfig: {\n        type: \"object\",\n        properties: {\n          name: { type: \"string\" },\n          maxQuota: { type: \"number\" },\n          price: { type: \"number\" },\n          features: {\n            type: \"array\",\n            items: { type: \"string\" },\n          },\n        },\n      },\n      TierStatusResponse: {\n        type: \"object\",\n        properties: {\n          success: { type: \"boolean\" },\n          data: {\n            type: \"object\",\n            properties: {\n              currentTier: { type: \"string\" },\n              tierName: { type: \"string\" },\n              usage: { type: \"number\" },\n              imagesUsage: { type: \"number\" },\n              contentUsage: { type: \"number\" },\n              titleUsage: { type: \"number\" },\n              maxQuota: { type: \"number\" },\n              imagesQuota: { type: \"number\" },\n              contentQuota: { type: \"number\" },\n              titleQuota: { type: \"number\" },\n              remainingImagesQuota: { type: \"number\" },\n              remainingContentQuota: { type: \"number\" },\n              remainingTitleQuota: { type: \"number\" },\n              price: { type: \"number\" },\n              features: {\n                type: \"array\",\n                items: { type: \"string\" },\n              },\n              quotaPercentage: { type: \"string\" },\n            },\n          },\n        },\n      },\n      UsageHistoryEntry: {\n        type: \"object\",\n        properties: {\n          id: { type: \"string\", format: \"uuid\" },\n          type: { type: \"string\", enum: [\"images\", \"content\"] },\n          source: { type: \"string\" },\n          timestamp: { type: \"string\", format: \"date-time\" },\n          metadata: {\n            type: \"object\",\n            additionalProperties: true,\n            example: {\n              size: \"1024x1024\",\n              model: \"stable-diffusion\",\n            },\n          },\n        },\n      },\n      DailyUsage: {\n        type: \"object\",\n        properties: {\n          date: { type: \"string\", format: \"date\" },\n          images: { type: \"integer\" },\n          content: { type: \"integer\" },\n          title: { type: \"integer\" },\n          details: {\n            type: \"array\",\n            items: {\n              type: \"object\",\n              properties: {\n                id: { type: \"string\", format: \"uuid\" },\n                type: { type: \"string\", enum: [\"images\", \"content\", \"title\"] },\n                source: { type: \"string\" },\n                time: { type: \"string\", format: \"date-time\" },\n              },\n            },\n          },\n        },\n        example: {\n          date: \"2024-12-08\",\n          images: 15,\n          content: 8,\n          title: 12,\n          details: [\n            {\n              id: \"123e4567-e89b-12d3-a456-************\",\n              type: \"title\",\n              source: \"product-title\",\n              time: \"2024-12-08T02:30:00.000Z\",\n            },\n          ],\n        },\n      },\n      PayPalSubscription: {\n        type: \"object\",\n        properties: {\n          subscriptionId: {\n            type: \"string\",\n            description: \"PayPal subscription identifier\",\n          },\n          status: {\n            type: \"string\",\n            enum: [\n              \"APPROVAL_PENDING\",\n              \"APPROVED\",\n              \"ACTIVE\",\n              \"SUSPENDED\",\n              \"CANCELLED\",\n              \"EXPIRED\",\n            ],\n          },\n          tier: {\n            type: \"string\",\n            enum: [\"medium\", \"high\"],\n          },\n          price: {\n            type: \"number\",\n            description: \"Monthly subscription price in USD\",\n          },\n          createdAt: {\n            type: \"string\",\n            format: \"date-time\",\n          },\n          nextBillingDate: {\n            type: \"string\",\n            format: \"date-time\",\n          },\n        },\n      },\n      Addons: {\n        type: \"object\",\n        properties: {\n          addon1: {\n            type: \"boolean\",\n            description: \"Enhanced features add-on\",\n          },\n          addon2: {\n            type: \"boolean\",\n            description: \"Premium support add-on\",\n          },\n        },\n      },\n    },\n    securitySchemes: {\n      ApiKeyAuth: {\n        type: \"apiKey\",\n        in: \"header\",\n        name: \"x-sps-key\",\n      },\n    },\n  },\n};\n\n// Function to serve Swagger UI HTML remains the same\nexport function getSwaggerHTML(swaggerSpec) {\n  return `\n      <!DOCTYPE html>\n      <html lang=\"en\">\n      <head>\n          <meta charset=\"utf-8\" />\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n          <title>API Documentation</title>\n          <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui.min.css\" />\n      </head>\n      <body>\n          <div id=\"swagger-ui\"></div>\n          <script src=\"https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui-bundle.min.js\"></script>\n          <script>\n              window.onload = () => {\n                  window.ui = SwaggerUIBundle({\n                      spec: ${JSON.stringify(swaggerSpec)},\n                      dom_id: '#swagger-ui',\n                      deepLinking: true,\n                      presets: [\n                          SwaggerUIBundle.presets.apis,\n                          SwaggerUIBundle.SwaggerUIStandalonePreset\n                      ],\n                  });\n              };\n          </script>\n      </body>\n      </html>\n    `;\n}\n", "// src/services/usageHistoryService.js\nexport class UsageHistoryService {\n  constructor(env) {\n    this.env = env;\n  }\n\n  static KEYS = {\n    HISTORY: \"usage_history\",\n    DAILY: \"daily_usage\",\n  };\n\n  async trackUsage(userId, data) {\n    try {\n      const historyId = crypto.randomUUID();\n      const timestamp = new Date().toISOString();\n      const day = timestamp.split(\"T\")[0]; // Get YYYY-MM-DD\n\n      const historyEntry = {\n        id: historyId,\n        userId,\n        type: data.type, // 'images' or 'content'\n        source: data.source,\n        timestamp,\n        metadata: data.metadata || {},\n      };\n\n      // Store individual history entry\n      await this.env.USERS_KV.put(\n        `${UsageHistoryService.KEYS.HISTORY}:${userId}:${historyId}`,\n        JSON.stringify(historyEntry)\n      );\n\n      // Update daily aggregation\n      const dailyKey = `${UsageHistoryService.KEYS.DAILY}:${userId}:${day}`;\n      const existingDaily = (await this.env.USERS_KV.get(dailyKey, \"json\")) || {\n        date: day,\n        images: 0,\n        content: 0,\n        details: [],\n      };\n\n      // Increment the appropriate counter\n      existingDaily[data.type] += 1;\n      // Add to details array, keeping only essential info\n      existingDaily.details.push({\n        id: historyId,\n        type: data.type,\n        source: data.source,\n        time: timestamp,\n      });\n\n      await this.env.USERS_KV.put(dailyKey, JSON.stringify(existingDaily));\n\n      return historyEntry;\n    } catch (error) {\n      console.error(\"Error tracking usage history:\", error);\n      throw error;\n    }\n  }\n\n  async getUserHistory(userId, options = {}) {\n    try {\n      const {\n        startDate,\n        endDate = new Date().toISOString(),\n        type,\n        limit = 100,\n        page = 1,\n      } = options;\n\n      // List all history entries for the user\n      const { keys } = await this.env.USERS_KV.list({\n        prefix: `${UsageHistoryService.KEYS.HISTORY}:${userId}:`,\n      });\n\n      // Fetch all entries\n      const entries = await Promise.all(\n        keys.map((key) => this.env.USERS_KV.get(key.name, \"json\"))\n      );\n\n      // Filter and sort entries\n      let filteredEntries = entries\n        .filter((entry) => {\n          if (!entry) return false;\n\n          const matchesType = !type || entry.type === type;\n          const matchesDateRange =\n            (!startDate || entry.timestamp >= startDate) &&\n            entry.timestamp <= endDate;\n\n          return matchesType && matchesDateRange;\n        })\n        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n      // Calculate pagination\n      const totalEntries = filteredEntries.length;\n      const totalPages = Math.ceil(totalEntries / limit);\n      const offset = (page - 1) * limit;\n\n      // Paginate results\n      filteredEntries = filteredEntries.slice(offset, offset + limit);\n\n      return {\n        entries: filteredEntries,\n        pagination: {\n          total: totalEntries,\n          totalPages,\n          currentPage: page,\n          limit,\n        },\n      };\n    } catch (error) {\n      console.error(\"Error getting user history:\", error);\n      throw error;\n    }\n  }\n\n  async getDailyUsage(userId, startDate, endDate = new Date().toISOString()) {\n    try {\n      const { keys } = await this.env.USERS_KV.list({\n        prefix: `${UsageHistoryService.KEYS.DAILY}:${userId}:`,\n      });\n\n      const dailyEntries = await Promise.all(\n        keys\n          .filter((key) => {\n            const date = key.name.split(\":\")[3];\n            return (\n              (!startDate || date >= startDate) && date <= endDate.split(\"T\")[0]\n            );\n          })\n          .map((key) => this.env.USERS_KV.get(key.name, \"json\"))\n      );\n\n      return dailyEntries.sort((a, b) => b.date.localeCompare(a.date));\n    } catch (error) {\n      console.error(\"Error getting daily usage:\", error);\n      throw error;\n    }\n  }\n}\n", "import { TierService } from \"../services/tierService\";\nimport { ResponseService } from \"../services/responseService\";\nimport { ApiKeyService } from \"../services/apiKeyService\";\nimport { UsageHistoryService } from \"../services/usageHistoryService\";\n\nexport class UsageController {\n  constructor(env) {\n    this.env = env;\n    this.tierService = new TierService(env);\n    this.responseService = new ResponseService();\n    this.apiKeyService = new ApiKeyService();\n    this.historyService = new UsageHistoryService(env);\n  }\n\n  async _validateApiKey(request) {\n    const apiKey = request.headers.get(\"x-sps-key\");\n    if (!apiKey) {\n      throw new Error(\"API Key is required\");\n    }\n    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, \"json\");\n    if (!user) {\n      throw new Error(\"Invalid API Key\");\n    }\n    return user;\n  }\n\n  async trackImageUsage(request) {\n    try {\n      const user = await this._validateApi<PERSON>ey(request);\n      const data = await request.json();\n\n      if (!data.source || !data.timestamp) {\n        throw new Error(\"Source and timestamp are required\");\n      }\n\n      // Track the usage\n      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(\n        user.id,\n        \"images\"\n      );\n      const tierStatus = await this.tierService.getUserTierStatus(user.id);\n\n      // Track in history\n      await this.historyService.trackUsage(user.id, {\n        type: \"images\",\n        source: data.source,\n        timestamp: data.timestamp,\n        metadata: data.metadata,\n      });\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess({\n            type: \"images\",\n            usage: updatedUsage,\n            source: data.source,\n            timestamp: data.timestamp,\n            tierStatus: {\n              current: updatedUsage,\n              remaining: tierStatus.remainingImagesQuota,\n              limit: tierStatus.imagesQuota,\n            },\n          })\n        ),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: error.message.includes(\"quota exceeded\") ? 403 : 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async trackContentUsage(request) {\n    try {\n      const user = await this._validateApiKey(request);\n      const data = await request.json();\n\n      if (!data.source || !data.timestamp) {\n        throw new Error(\"Source and timestamp are required\");\n      }\n\n      // Track the usage\n      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(\n        user.id,\n        \"content\"\n      );\n      const tierStatus = await this.tierService.getUserTierStatus(user.id);\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess(\n            {\n              type: \"content\",\n              usage: updatedUsage,\n              source: data.source,\n              timestamp: data.timestamp,\n              tierStatus: {\n                current: updatedUsage,\n                remaining: tierStatus.remainingContentQuota,\n                limit: tierStatus.contentQuota,\n              },\n            },\n            \"Content usage tracked successfully\"\n          )\n        ),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: error.message.includes(\"quota exceeded\") ? 403 : 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getUsageStats(request) {\n    try {\n      const user = await this._validateApiKey(request);\n      const tierStatus = await this.tierService.getUserTierStatus(user.id);\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess({\n            images: {\n              used: tierStatus.imagesUsage || 0,\n              remaining: tierStatus.remainingImagesQuota,\n              total: tierStatus.imagesQuota,\n              percentageUsed: (\n                ((tierStatus.imagesUsage || 0) / tierStatus.imagesQuota) *\n                100\n              ).toFixed(2),\n            },\n            content: {\n              used: tierStatus.contentUsage || 0,\n              remaining: tierStatus.remainingContentQuota,\n              total: tierStatus.contentQuota,\n              percentageUsed: (\n                ((tierStatus.contentUsage || 0) / tierStatus.contentQuota) *\n                100\n              ).toFixed(2),\n            },\n            title: {\n              used: tierStatus.titleUsage || 0,\n              remaining: tierStatus.remainingTitleQuota,\n              total: tierStatus.titleQuota,\n              percentageUsed: (\n                ((tierStatus.titleUsage || 0) / tierStatus.titleQuota) *\n                100\n              ).toFixed(2),\n            },\n            tier: {\n              name: tierStatus.tierName,\n              current: tierStatus.currentTier,\n              expirationDate: tierStatus.expirationDate,\n              isExpired: tierStatus.isExpired,\n              price: tierStatus.price,\n            },\n            addons: {\n              addon1: {\n                enabled: tierStatus.addon1,\n                price: tierStatus.addon1_price,\n                features: tierStatus.addon1_detail,\n              },\n              addon2: {\n                enabled: tierStatus.addon2,\n                price: tierStatus.addon2_price,\n                features: tierStatus.addon2_detail,\n              },\n            },\n            features: tierStatus.features,\n          })\n        ),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getUsageHistory(request) {\n    try {\n      const user = await this._validateApiKey(request);\n      const url = new URL(request.url);\n\n      const options = {\n        startDate: url.searchParams.get(\"startDate\"),\n        endDate: url.searchParams.get(\"endDate\"),\n        type: url.searchParams.get(\"type\"),\n        limit: parseInt(url.searchParams.get(\"limit\") || \"100\"),\n        page: parseInt(url.searchParams.get(\"page\") || \"1\"),\n      };\n\n      const history = await this.historyService.getUserHistory(\n        user.id,\n        options\n      );\n\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(history)),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getDailyUsage(request) {\n    try {\n      const user = await this._validateApiKey(request);\n      const url = new URL(request.url);\n\n      const startDate = url.searchParams.get(\"startDate\");\n      const endDate = url.searchParams.get(\"endDate\");\n\n      const dailyUsage = await this.historyService.getDailyUsage(\n        user.id,\n        startDate,\n        endDate\n      );\n\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(dailyUsage)),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async trackTitleUsage(request) {\n    try {\n      const user = await this._validateApiKey(request);\n      const data = await request.json();\n\n      if (!data.source || !data.timestamp) {\n        throw new Error(\"Source and timestamp are required\");\n      }\n\n      // Track the usage\n      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(\n        user.id,\n        \"title\"\n      );\n      const tierStatus = await this.tierService.getUserTierStatus(user.id);\n\n      // Track in history\n      await this.historyService.trackUsage(user.id, {\n        type: \"title\",\n        source: data.source,\n        timestamp: data.timestamp,\n        metadata: data.metadata,\n      });\n\n      return new Response(\n        JSON.stringify(\n          this.responseService.formatSuccess({\n            tracked: true,\n            currentUsage: updatedUsage,\n            remainingQuota: tierStatus.remainingTitleQuota,\n            timestamp: data.timestamp,\n            tierStatus: {\n              current: updatedUsage,\n              remaining: tierStatus.remainingTitleQuota,\n              limit: tierStatus.titleQuota,\n            },\n          })\n        ),\n        {\n          status: 201,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: error.message.includes(\"quota exceeded\") ? 403 : 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n}\n", "import { Router } from \"itty-router\";\nimport { UsageController } from \"../controllers/usageController\";\n\nexport function createUsageRouter(env) {\n  const router = Router({ base: \"/api/usage\" });\n  const controller = new UsageController(env);\n\n  router.post(\"/images\", (request) => controller.trackImageUsage(request));\n  router.post(\"/content\", (request) => controller.trackContentUsage(request));\n  router.post(\"/title\", (request) => controller.trackTitleUsage(request));\n  router.get(\"/stats\", (request) => controller.getUsageStats(request));\n  // In usageRoutes.js\n  router.get(\"/history\", (request) => controller.getUsageHistory(request));\n  router.get(\"/daily\", (request) => controller.getDailyUsage(request));\n  return router;\n}\n", "// src/services/webhookService.js\nimport { TierService } from \"./tierService\";\nimport { EmailQueueService } from \"./emailQueueService\";\n\nexport class WebhookService {\n  constructor(env) {\n    this.env = env;\n    this.tierService = new TierService(env);\n    this.emailQueueService = new EmailQueueService(env);\n    this.isDevelopment =\n      env.NODE_ENV === \"development\" ||\n      env.ENVIRONMENT === \"development\" ||\n      env.PAYPAL_SANDBOX === \"true\";\n  }\n\n  logWebhook(message, data = {}) {\n    const timestamp = new Date().toISOString();\n    console.log(\"\\n📮 WEBHOOK:\", message);\n    console.log(\"⌚\", timestamp);\n    console.log(\"📄 Data:\", JSON.stringify(data, null, 2), \"\\n\");\n  }\n\n  logError(message, error) {\n    console.error(\"\\n❌ WEBHOOK ERROR:\", message);\n    console.error(\"🔍 Details:\", error);\n    console.error(\"📍 Stack:\", error.stack, \"\\n\");\n  }\n\n  async verifyWebhookSignature(request) {\n    const headers = request.headers;\n\n    // Check if this is a test/simulation request\n    const isSimulation = headers.get(\"PAYPAL-SIMULATION\") === \"true\";\n\n    // Skip verification for development/sandbox mode or simulated webhooks\n    if (this.isDevelopment || isSimulation) {\n      console.log(\n        \"📝 Development/Sandbox mode or simulation: Skipping webhook signature verification\"\n      );\n      return true;\n    }\n\n    // Production verification logic\n    const webhookId = this.env.PAYPAL_WEBHOOK_ID;\n    const transmissionId = headers.get(\"paypal-transmission-id\");\n    const timestamp = headers.get(\"paypal-transmission-time\");\n    const signature = headers.get(\"paypal-transmission-sig\");\n    const certUrl = headers.get(\"paypal-cert-url\");\n\n    console.log(\"📨 Webhook headers received:\", {\n      webhookId: webhookId ? \"(present)\" : \"(missing)\",\n      transmissionId,\n      timestamp,\n      signature: signature ? \"(present)\" : \"(missing)\",\n      certUrl,\n      environment: this.isDevelopment ? \"development\" : \"production\",\n    });\n\n    if (!webhookId || !transmissionId || !timestamp || !signature || !certUrl) {\n      if (this.isDevelopment) {\n        console.warn(\n          \"⚠️ Missing webhook headers in development mode - continuing anyway\"\n        );\n        return true;\n      }\n      throw new Error(\"Missing required webhook headers\");\n    }\n\n    // Here you would implement actual PayPal signature verification\n    // using their API for production environments\n    return true;\n  }\n\n  async processWebhookEvent(event) {\n    this.logWebhook(\"Received webhook event\", {\n      eventType: event.event_type,\n      eventId: event.id,\n      timestamp: event.create_time,\n    });\n    try {\n      // Store webhook event\n      await this.storeWebhookEvent(event);\n\n      // Process based on event type\n      let result;\n      switch (event.event_type) {\n        case \"BILLING.SUBSCRIPTION.ACTIVATED\":\n          this.logWebhook(\"Processing subscription activation\");\n          result = await this.handleSubscriptionActivated(event);\n          break;\n\n        case \"PAYMENT.SALE.COMPLETED\":\n          this.logWebhook(\"Processing payment completion\");\n          result = await this.handlePaymentCompleted(event);\n          break;\n\n        case \"BILLING.SUBSCRIPTION.CANCELLED\":\n          this.logWebhook(\"Processing subscription cancellation\");\n          result = await this.handleSubscriptionCancelled(event);\n          break;\n\n        case \"BILLING.SUBSCRIPTION.PAYMENT.FAILED\":\n          this.logWebhook(\"Processing payment failure\");\n          result = await this.handlePaymentFailed(event);\n          break;\n\n        default:\n          this.logWebhook(\"Unhandled event type\", {\n            eventType: event.event_type,\n          });\n          return { status: \"unhandled\", eventType: event.event_type };\n      }\n\n      this.logWebhook(\"Successfully processed webhook\", result);\n      return result;\n    } catch (error) {\n      this.logError(\"Failed to process webhook\", error);\n      throw error;\n    }\n  }\n\n  async handleSubscriptionActivated(event) {\n    try {\n      const subscription = event.resource;\n      this.logWebhook(\"Processing subscription activation\", {\n        subscriptionId: subscription.id,\n      });\n\n      // Find our stored subscription\n      const subscriptionData = await this.getSubscriptionData(subscription.id);\n      if (!subscriptionData) {\n        throw new Error(`Subscription not found: ${subscription.id}`);\n      }\n\n      this.logWebhook(\"Found subscription data\", subscriptionData);\n\n      // Update user tier\n      this.logWebhook(\"Updating user tier\", {\n        userId: subscriptionData.userId,\n        tier: subscriptionData.tier,\n      });\n      await this.tierService.setUserTier(\n        subscriptionData.userId,\n        subscriptionData.tier\n      );\n\n      // Queue welcome email\n      this.logWebhook(\"Queueing welcome email\", {\n        email: subscription.subscriber.email_address,\n      });\n      await this.emailQueueService.addToQueue({\n        email: subscription.subscriber.email_address,\n        type: \"subscription_activated\",\n        data: {\n          tier: subscriptionData.tier,\n          price: subscriptionData.price,\n          nextBillingDate: subscription.billing_info?.next_billing_time,\n        },\n      });\n\n      // Update subscription status\n      this.logWebhook(\"Updating subscription status\", {\n        status: subscription.status,\n        nextBilling: subscription.billing_info?.next_billing_time,\n      });\n      await this.updateSubscriptionStatus(\n        subscriptionData.userId,\n        subscription.id,\n        {\n          status: subscription.status,\n          nextBillingTime: subscription.billing_info?.next_billing_time,\n          lastPayment: subscription.billing_info?.last_payment,\n          startTime: subscription.start_time,\n        }\n      );\n\n      const result = { status: \"activated\", subscriptionId: subscription.id };\n      this.logWebhook(\"Successfully activated subscription\", result);\n      return result;\n    } catch (error) {\n      this.logError(\"Failed to activate subscription\", error);\n      throw error;\n    }\n  }\n\n  async handlePaymentCompleted(event) {\n    const payment = event.resource;\n    const subscriptionId = payment.billing_agreement_id;\n    this.logWebhook(\"Processing payment completion\", {\n      paymentId: payment.id,\n      amount: payment.amount,\n    });\n\n    const subscriptionData = await this.getSubscriptionData(subscriptionId);\n    if (!subscriptionData) {\n      throw new Error(`Subscription not found: ${subscriptionId}`);\n    }\n\n    // Record payment\n    await this.recordPayment({\n      id: payment.id,\n      subscriptionId,\n      userId: subscriptionData.userId,\n      amount: payment.amount.total,\n      currency: payment.amount.currency,\n      status: payment.state,\n      createTime: payment.create_time,\n      updateTime: payment.update_time,\n    });\n\n    // Send confirmation email\n    await this.emailQueueService.addToQueue({\n      email: subscriptionData.email,\n      type: \"payment_success\",\n      data: {\n        amount: payment.amount.total,\n        currency: payment.amount.currency,\n        date: payment.create_time,\n        tier: subscriptionData.tier,\n      },\n    });\n\n    return { status: \"completed\", paymentId: payment.id };\n  }\n\n  async handleSubscriptionCancelled(event) {\n    const subscription = event.resource;\n    this.logWebhook(\"Processing subscription cancellation\", {\n      subscriptionId: subscription.id,\n    });\n    const subscriptionData = await this.getSubscriptionData(subscription.id);\n    if (!subscriptionData) {\n      throw new Error(`Subscription not found: ${subscription.id}`);\n    }\n\n    // Downgrade to free tier\n    await this.tierService.setUserTier(subscriptionData.userId, \"free\");\n\n    // Send cancellation email\n    await this.emailQueueService.addToQueue({\n      email: subscription.subscriber.email_address,\n      type: \"subscription_cancelled\",\n      data: {\n        cancellationDate: subscription.status_update_time,\n        previousTier: subscriptionData.tier,\n      },\n    });\n\n    // Update subscription status\n    await this.updateSubscriptionStatus(\n      subscriptionData.userId,\n      subscription.id,\n      {\n        status: subscription.status,\n        cancellationTime: subscription.status_update_time,\n      }\n    );\n\n    return { status: \"cancelled\", subscriptionId: subscription.id };\n  }\n\n  async handlePaymentFailed(event) {\n    const subscription = event.resource;\n    const subscriptionData = await this.getSubscriptionData(subscription.id);\n    if (!subscriptionData) {\n      throw new Error(`Subscription not found: ${subscription.id}`);\n    }\n\n    // Send failure notification\n    await this.emailQueueService.addToQueue({\n      email: subscription.subscriber.email_address,\n      type: \"payment_failed\",\n      data: {\n        amount: subscription.billing_info.outstanding_balance.value,\n        currency: subscription.billing_info.outstanding_balance.currency_code,\n        failedPaymentsCount: subscription.billing_info.failed_payments_count,\n        nextRetry: subscription.billing_info.next_payment_retry_time,\n      },\n    });\n\n    // Update subscription status\n    await this.updateSubscriptionStatus(\n      subscriptionData.userId,\n      subscription.id,\n      {\n        status: subscription.status,\n        failedPaymentsCount: subscription.billing_info.failed_payments_count,\n        lastFailedPayment: subscription.billing_info.last_failed_payment,\n      }\n    );\n\n    return { status: \"failed\", subscriptionId: subscription.id };\n  }\n\n  // Helper methods\n  async getSubscriptionData(subscriptionId) {\n    this.logWebhook(\"Looking up subscription data\", { subscriptionId });\n    const { keys } = await this.env.USERS_KV.list({ prefix: \"subscription:\" });\n\n    for (const key of keys) {\n      const data = await this.env.USERS_KV.get(key.name, \"json\");\n      if (data?.subscriptionId === subscriptionId) {\n        this.logWebhook(\"Found subscription data\", {\n          userId: key.name.split(\":\")[1],\n          subscription: data,\n        });\n        return {\n          ...data,\n          userId: key.name.split(\":\")[1],\n        };\n      }\n    }\n\n    this.logWebhook(\"Subscription not found\", { subscriptionId });\n    return null;\n  }\n\n  async storeWebhookEvent(event) {\n    const eventId = `webhook_event:${event.id}`;\n    await this.env.USERS_KV.put(\n      eventId,\n      JSON.stringify({\n        ...event,\n        receivedAt: new Date().toISOString(),\n      })\n    );\n  }\n\n  async updateSubscriptionStatus(userId, subscriptionId, updates) {\n    const key = `subscription:${userId}`;\n    const data = await this.env.USERS_KV.get(key, \"json\");\n\n    if (data && data.subscriptionId === subscriptionId) {\n      await this.env.USERS_KV.put(\n        key,\n        JSON.stringify({\n          ...data,\n          ...updates,\n          updatedAt: new Date().toISOString(),\n        })\n      );\n    }\n  }\n\n  async recordPayment(payment) {\n    await this.env.USERS_KV.put(\n      `payment:${payment.id}`,\n      JSON.stringify({\n        ...payment,\n        recordedAt: new Date().toISOString(),\n      })\n    );\n  }\n}\n", "import { WebhookService } from \"../services/webhookService\";\nimport { ResponseService } from \"../services/responseService\";\n\nexport class WebhookController {\n  constructor(env) {\n    this.env = env;\n    this.webhookService = new WebhookService(env);\n    this.responseService = new ResponseService();\n  }\n\n  async handlePayPalWebhook(request) {\n    try {\n      // Verify webhook signature\n      await this.webhookService.verifyWebhookSignature(request);\n\n      // Process the webhook\n      const event = await request.json();\n      const result = await this.webhookService.processWebhookEvent(event);\n\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(result)),\n        {\n          status: 200,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      console.error(\"Webhook processing error:\", error);\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: error.status || 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getWebhookStatus(request) {\n    try {\n      const status = await this.webhookService.getWebhookStatus();\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(status)),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  async getRecentEvents(request) {\n    try {\n      const events = await this.webhookService.getRecentEvents();\n      return new Response(\n        JSON.stringify(this.responseService.formatSuccess(events)),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify(this.responseService.formatError(error.message)),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n}\n", "import { Router } from \"itty-router\";\nimport { WebhookController } from \"../controllers/webhookController\";\n\nexport function createWebhookRouter(env) {\n  const router = Router({ base: \"/api/webhooks\" });\n  const webhookController = new WebhookController(env);\n\n  // PayPal webhook endpoint\n  router.post(\"/paypal\", (request) =>\n    webhookController.handlePayPalWebhook(request)\n  );\n\n  // Webhook status/health check\n  router.get(\"/status\", (request) =>\n    webhookController.getWebhookStatus(request)\n  );\n\n  // Debug endpoint for webhook events (development only)\n  router.get(\"/events\", (request) =>\n    webhookController.getRecentEvents(request)\n  );\n\n  return router;\n}\n", "// src/controllers/webhookTestController.js\nexport class WebhookTestController {\n  constructor(env) {\n    this.env = env;\n  }\n\n  async simulatePayPalWebhook(request) {\n    try {\n      const { eventType, subscriptionId } = await request.json();\n\n      // Get the real subscription data\n      const { keys } = await this.env.USERS_KV.list({\n        prefix: \"subscription:\",\n      });\n      let subscriptionData = null;\n      let userId = null;\n\n      for (const key of keys) {\n        const data = await this.env.USERS_KV.get(key.name, \"json\");\n        if (data?.subscriptionId === subscriptionId) {\n          subscriptionData = data;\n          userId = key.name.split(\":\")[1];\n          break;\n        }\n      }\n\n      if (!subscriptionData || !userId) {\n        throw new Error(`Subscription not found: ${subscriptionId}`);\n      }\n\n      // Get real user data\n      const user = await this.env.USERS_KV.get(`user:${userId}`, \"json\");\n      if (!user) {\n        throw new Error(`User not found for subscription: ${subscriptionId}`);\n      }\n\n      // Generate event based on type\n      let webhookEvent;\n      if (eventType === \"PAYMENT.SALE.COMPLETED\") {\n        webhookEvent = this.generatePaymentEvent(\n          subscriptionId,\n          subscriptionData,\n          user\n        );\n      } else {\n        webhookEvent = this.generateSubscriptionEvent(\n          eventType,\n          subscriptionId,\n          subscriptionData,\n          user\n        );\n      }\n\n      // Generate real PayPal-style webhook headers\n      const transmissionId = crypto.randomUUID();\n      const webhookHeaders = {\n        \"paypal-auth-algo\": \"SHA256withRSA\",\n        \"paypal-cert-url\":\n          \"https://api.sandbox.paypal.com/v1/notifications/certs/CERT-360caa42-fca2a594-bc34f77b\",\n        \"paypal-transmission-id\": transmissionId,\n        \"paypal-transmission-sig\": `mock_${transmissionId}`,\n        \"paypal-transmission-time\": webhookEvent.create_time,\n        \"Content-Type\": \"application/json\",\n      };\n\n      // Call webhook endpoint\n      const response = await fetch(\n        new URL(\"/api/webhooks/paypal\", request.url),\n        {\n          method: \"POST\",\n          headers: webhookHeaders,\n          body: JSON.stringify(webhookEvent),\n        }\n      );\n\n      const result = await response.json();\n\n      return new Response(\n        JSON.stringify({\n          success: true,\n          message: `PayPal ${eventType} webhook simulated`,\n          details: {\n            webhookId: webhookEvent.id,\n            transmissionId: transmissionId,\n            subscription: subscriptionId,\n            user: user.email,\n            timestamp: webhookEvent.create_time,\n            amount: subscriptionData.price,\n          },\n          result,\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    } catch (error) {\n      console.error(\"Webhook simulation error:\", error);\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      );\n    }\n  }\n\n  generatePaymentEvent(subscriptionId, subscriptionData, user) {\n    const now = new Date().toISOString();\n    const paymentId = `PAY-${crypto.randomUUID()}`;\n\n    return {\n      id: `WH-${crypto.randomUUID()}`,\n      create_time: now,\n      resource_type: \"sale\",\n      event_type: \"PAYMENT.SALE.COMPLETED\",\n      summary: \"Payment completed for subscription\",\n      resource: {\n        id: paymentId,\n        state: \"completed\",\n        amount: {\n          total: subscriptionData.price.toString(),\n          currency: \"USD\",\n          details: {\n            subtotal: subscriptionData.price.toString(),\n          },\n        },\n        payment_mode: \"INSTANT_TRANSFER\",\n        protection_eligibility: \"ELIGIBLE\",\n        protection_eligibility_type:\n          \"ITEM_NOT_RECEIVED_ELIGIBLE,UNAUTHORIZED_PAYMENT_ELIGIBLE\",\n        transaction_fee: {\n          value: (subscriptionData.price * 0.029 + 0.3).toFixed(2),\n          currency: \"USD\",\n        },\n        billing_agreement_id: subscriptionId,\n        create_time: now,\n        update_time: now,\n        links: [\n          {\n            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}`,\n            rel: \"self\",\n            method: \"GET\",\n          },\n          {\n            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}/refund`,\n            rel: \"refund\",\n            method: \"POST\",\n          },\n        ],\n      },\n      links: [\n        {\n          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,\n          rel: \"self\",\n          method: \"GET\",\n        },\n        {\n          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,\n          rel: \"resend\",\n          method: \"POST\",\n        },\n      ],\n    };\n  }\n\n  generateSubscriptionEvent(eventType, subscriptionId, subscriptionData, user) {\n    const now = new Date().toISOString();\n\n    return {\n      id: `WH-${crypto.randomUUID()}`,\n      event_type: eventType,\n      event_version: \"1.0\",\n      create_time: now,\n      resource_type: \"subscription\",\n      resource_version: \"2.0\",\n      summary: `Subscription ${eventType.split(\".\").pop().toLowerCase()}`,\n      resource: {\n        start_time: subscriptionData.createdAt,\n        quantity: \"1\",\n        subscriber: {\n          name: {\n            given_name: user.email.split(\"@\")[0],\n            surname: \"\",\n          },\n          email_address: user.email,\n          payer_id: user.id,\n        },\n        status: \"ACTIVE\",\n        status_update_time: now,\n        id: subscriptionId,\n        plan_id: subscriptionData.planId,\n        billing_info: {\n          outstanding_balance: {\n            currency_code: \"USD\",\n            value: \"0.00\",\n          },\n          cycle_executions: [\n            {\n              tenure_type: \"REGULAR\",\n              sequence: 1,\n              cycles_completed: 1,\n              cycles_remaining: 0,\n              current_pricing_scheme_version: 1,\n            },\n          ],\n          last_payment: {\n            amount: {\n              currency_code: \"USD\",\n              value: subscriptionData.price.toString(),\n            },\n            time: now,\n          },\n          next_billing_time: new Date(\n            Date.now() + 30 * 24 * 60 * 60 * 1000\n          ).toISOString(),\n          failed_payments_count: 0,\n        },\n      },\n      links: [\n        {\n          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,\n          rel: \"self\",\n          method: \"GET\",\n        },\n        {\n          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,\n          rel: \"resend\",\n          method: \"POST\",\n        },\n      ],\n    };\n  }\n}\n", "// src/routes/webhookTestRoutes.js\nimport { Router } from \"itty-router\";\nimport { WebhookTestController } from \"../controllers/webhookTestController\";\n\nexport function createWebhookTestRouter(env) {\n  const router = Router({ base: \"/api/test/webhooks\" });\n  const controller = new WebhookTestController(env);\n\n  // Endpoint to simulate PayPal webhooks\n  router.post(\"/simulate/paypal\", (request) =>\n    controller.simulatePayPalWebhook(request)\n  );\n\n  return router;\n}\n", "// src/index.js\nimport { Router } from \"itty-router\";\nimport { createUserRouter } from \"./routes/userRoutes\";\nimport { createTierRouter } from \"./routes/tierRoutes\";\nimport { createDebugRouter } from \"./routes/debugRoutes\";\nimport { createSubscriptionRouter } from \"./routes/subscriptionRoutes\";\nimport { swaggerDocument, getSwaggerHTML } from \"./swagger/swagger\";\nimport { TierService } from \"./services/tierService\";\nimport { EmailQueueService } from \"./services/emailQueueService\";\nimport { createUsageRouter } from \"./routes/usageRoutes\";\nimport { createWebhookRouter } from \"./routes/webhookRoutes\";\nimport { createWebhookTestRouter } from \"./routes/webhookTestRoutes\";\n// CORS headers configuration\nconst corsHeaders = {\n  \"Access-Control-Allow-Origin\": \"*\", // In production, replace with your frontend domain\n  \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n  \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, x-api-key\",\n  \"Access-Control-Max-Age\": \"86400\",\n};\n\n// Helper function to handle OPTIONS requests\nfunction handleOptions(request) {\n  return new Response(null, {\n    headers: corsHeaders,\n  });\n}\n\n// Helper function to add CORS headers to any response\nfunction addCorsHeaders(response) {\n  const newHeaders = new Headers(response.headers);\n  Object.entries(corsHeaders).forEach(([key, value]) => {\n    newHeaders.set(key, value);\n  });\n\n  return new Response(response.body, {\n    status: response.status,\n    statusText: response.statusText,\n    headers: newHeaders,\n  });\n}\n\nconst router = Router();\n\n// Initialize tier settings\nasync function initializeTierSettings(env) {\n  const tierService = new TierService(env);\n  await tierService.initializeTierSettings();\n  console.log(\"Tier settings initialized\");\n}\n\n// Process email queue function\nasync function processEmailQueue(env) {\n  try {\n    console.log(\"Starting scheduled email queue processing...\");\n    const emailQueueService = new EmailQueueService(env);\n    const beforeStatus = await emailQueueService.getQueueStatus();\n    console.log(\"Queue status before processing:\", beforeStatus);\n    const results = await emailQueueService.processQueue(true);\n    console.log(\"Queue processing results:\", results);\n    const afterStatus = await emailQueueService.getQueueStatus();\n    console.log(\"Queue status after processing:\", afterStatus);\n    return { beforeStatus, results, afterStatus };\n  } catch (error) {\n    console.error(\"Error processing email queue:\", error);\n    throw error;\n  }\n}\n\n// Handle OPTIONS requests for all routes\nrouter.options(\"*\", handleOptions);\n\n// Swagger documentation route\nrouter.get(\"/api/docs\", () => {\n  return addCorsHeaders(\n    new Response(getSwaggerHTML(swaggerDocument), {\n      headers: {\n        \"content-type\": \"text/html;charset=UTF-8\",\n      },\n    })\n  );\n});\n\n// Mount subscription routes\nrouter.all(\"/api/subscriptions/*\", async (request, env) => {\n  try {\n    const subscriptionRouter = createSubscriptionRouter(env);\n    const response = await subscriptionRouter.handle(request);\n    return addCorsHeaders(response);\n  } catch (error) {\n    console.error(\"Subscription route error:\", error);\n    return addCorsHeaders(\n      new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      )\n    );\n  }\n});\n\n// Mount tier routes\nrouter.all(\"/api/tiers/*\", async (request, env) => {\n  const tierRouter = createTierRouter(env);\n  const response = await tierRouter.handle(request);\n  return addCorsHeaders(response);\n});\n\n// Mount usage routes\nrouter.all(\"/api/usage/*\", async (request, env) => {\n  const usageRouter = createUsageRouter(env);\n  const response = await usageRouter.handle(request);\n  return addCorsHeaders(response);\n});\n\n// Mount user routes\nrouter.all(\"/api/users/*\", async (request, env) => {\n  const userRouter = createUserRouter(env);\n  const response = await userRouter.handle(request);\n  return addCorsHeaders(response);\n});\n\n// Mount email queue routes\nrouter.all(\"/api/debug/*\", async (request, env) => {\n  try {\n    const debugRouter = createDebugRouter(env);\n    const response = await debugRouter.handle(request);\n    return addCorsHeaders(response);\n  } catch (error) {\n    console.error(\"Debug route error:\", error);\n    return addCorsHeaders(\n      new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message,\n        }),\n        {\n          status: 500,\n          headers: { \"Content-Type\": \"application/json\" },\n        }\n      )\n    );\n  }\n});\n\n// simulasi test paypal\nrouter.all(\"/api/test/webhooks/*\", async (request, env) => {\n  const testRouter = createWebhookTestRouter(env);\n  const response = await testRouter.handle(request);\n  return addCorsHeaders(response);\n});\n\n// Mount webhook routes\nrouter.all(\"/api/webhooks/*\", async (request, env) => {\n  const webhookRouter = createWebhookRouter(env);\n  const response = await webhookRouter.handle(request);\n  return addCorsHeaders(response);\n});\n\n// 404 handler\nrouter.all(\"*\", () =>\n  addCorsHeaders(new Response(\"Not Found\", { status: 404 }))\n);\n\n// Export worker\nexport default {\n  // Regular request handler\n  async fetch(request, env, ctx) {\n    try {\n      await initializeTierSettings(env);\n\n      // Handle CORS preflight requests\n      if (request.method === \"OPTIONS\") {\n        return handleOptions(request);\n      }\n\n      // For manual queue processing endpoint\n      if (\n        request.method === \"POST\" &&\n        request.url.endsWith(\"/api/debug/process-queue\")\n      ) {\n        const results = await processEmailQueue(env);\n        return addCorsHeaders(\n          new Response(\n            JSON.stringify({\n              success: true,\n              message: \"Queue processed manually\",\n              ...results,\n            }),\n            {\n              headers: { \"Content-Type\": \"application/json\" },\n            }\n          )\n        );\n      }\n\n      const response = await router.handle(request, env, ctx);\n      return addCorsHeaders(response);\n    } catch (error) {\n      console.error(\"Worker error:\", error);\n      return addCorsHeaders(\n        new Response(\n          JSON.stringify({\n            success: false,\n            error: error.message,\n          }),\n          {\n            status: 500,\n            headers: { \"Content-Type\": \"application/json\" },\n          }\n        )\n      );\n    }\n  },\n\n  // Scheduled handler for cron\n  async scheduled(event, env, ctx) {\n    console.log(`Cron triggered: ${event.cron} at ${new Date().toISOString()}`);\n    try {\n      ctx.waitUntil(processEmailQueue(env));\n    } catch (error) {\n      console.error(\"Scheduled job error:\", error);\n    }\n  },\n};\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\n// A middleware has to be a function of type Middleware\nconst scheduled: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\tconst url = new URL(request.url);\n\tif (url.pathname === \"/__scheduled\") {\n\t\tconst cron = url.searchParams.get(\"cron\") ?? \"\";\n\t\tawait middlewareCtx.dispatch(\"scheduled\", { cron });\n\n\t\treturn new Response(\"Ran scheduled event\");\n\t}\n\n\tconst resp = await middlewareCtx.next(request, env);\n\n\t// If you open the `/__scheduled` page in a browser, the browser will automatically make a request to `/favicon.ico`.\n\t// For scheduled Workers _without_ a fetch handler, this will result in a 500 response that clutters the log with unhelpful error messages.\n\t// To avoid this, inject a 404 response to favicon.ico loads on the `/__scheduled` page\n\tif (\n\t\trequest.headers.get(\"referer\")?.endsWith(\"/__scheduled\") &&\n\t\turl.pathname === \"/favicon.ico\" &&\n\t\tresp.status === 500\n\t) {\n\t\treturn new Response(null, { status: 404 });\n\t}\n\n\treturn resp;\n};\n\nexport default scheduled;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/home/<USER>/Docker/lemp/html/backend_awp/src/index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/home/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/home/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/middleware-scheduled.ts\";\nimport * as __MIDDLEWARE_2__ from \"/home/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/home/<USER>/Docker/lemp/html/backend_awp/src/index.js\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default,__MIDDLEWARE_2__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/home/<USER>/Docker/lemp/html/backend_awp/.wrangler/tmp/bundle-mB1F46/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/home/<USER>/Docker/lemp/html/backend_awp/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/home/<USER>/Docker/lemp/html/backend_awp/.wrangler/tmp/bundle-mB1F46/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/home/<USER>/Docker/lemp/html/backend_awp/.wrangler/tmp/bundle-mB1F46/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;;;;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS;AAAA;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC7BD,IAAM,IAAE,wBAAC,EAAC,MAAKA,KAAE,IAAG,QAAO,IAAE,CAAC,EAAC,IAAE,CAAC,OAAK,EAAC,WAAU,IAAI,MAAM,CAAC,GAAE,EAAC,KAAI,CAAC,GAAE,GAAE,MAAI,CAACC,OAAK,MAAI,EAAE,KAAK,CAAC,EAAE,YAAY,GAAE,OAAO,KAAKD,KAAEC,IAAG,QAAQ,YAAW,SAAS,EAAE,QAAQ,qBAAoB,EAAE,EAAE,QAAQ,cAAa,WAAW,EAAE,QAAQ,qBAAoB,oBAAoB,EAAE,QAAQ,eAAc,KAAK,EAAE,QAAQ,yBAAwB,wBAAwB,MAAM,GAAE,CAAC,CAAC,KAAG,EAAC,CAAC,GAAE,QAAO,GAAE,MAAM,OAAOD,OAAK,GAAE;AAAC,MAAI,GAAE,GAAE,IAAE,IAAI,IAAIA,GAAE,GAAG,GAAE,IAAEA,GAAE,QAAM,CAAC;AAAE,WAAO,CAACA,IAAEE,EAAC,KAAI,EAAE;AAAa,MAAEF,EAAC,IAAE,WAAS,EAAEA,EAAC,IAAEE,KAAE,CAAC,EAAEF,EAAC,GAAEE,EAAC,EAAE,KAAK;AAAE,WAAO,CAACC,IAAE,GAAE,CAAC,KAAI;AAAE,SAAIA,OAAIH,GAAE,UAAQ,UAAQG,QAAK,IAAE,EAAE,SAAS,MAAM,CAAC,IAAG;AAAC,MAAAH,GAAE,SAAO,EAAE,UAAQ,CAAC;AAAE,eAAQE,MAAK;AAAE,YAAG,YAAU,IAAE,MAAMA,GAAEF,GAAE,SAAOA,IAAE,GAAG,CAAC;AAAG,iBAAO;AAAA,IAAC;AAAC,EAAC,IAAzpB;;;ACAD,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc,MAAM,UAAU,MAAM;AAClC,WAAO;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAAA,EACF;AAAA,EAEA,YAAY,SAAS,aAAa,KAAK;AACrC,WAAO;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAAA,EACF;AACF;AAlBW;;;ACAN,IAAM,eAAN,MAAmB;AAAA,EACxB,YAAY,KAAK;AACf,QAAI,CAAC,IAAI,aAAa;AACpB,cAAQ;AAAA,QACN;AAAA,MACF;AACA,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAEA,QAAI,CAAC,IAAI,oBAAoB;AAC3B,cAAQ;AAAA,QACN;AAAA,MACF;AACA,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAEA,SAAK,UAAU;AACf,SAAK,QAAQ,IAAI;AACjB,SAAK,cAAc,IAAI,mBAAmB,SAAS,GAAG,IAClD,IAAI,qBACJ,WAAW,IAAI;AACnB,SAAK,cACH;AAAA,EACJ;AAAA,EAEA,MAAM,gBAAgB,OAAO,UAAU,QAAQ,UAAU,QAAQ;AAC/D,YAAQ,IAAI,wCAAiC,KAAK;AAElD,QAAI;AACF,YAAM,cAAc;AAAA,QAClB,cAAc,KAAK;AAAA,QACnB,MAAM;AAAA,UACJ,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,QACR;AAAA,QACA,IAAI;AAAA,UACF;AAAA,YACE,eAAe;AAAA,cACb,SAAS;AAAA,cACT,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA;AAAA,UACT,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,OAAM,oBAAI,KAAK,GAAE,YAAY;AAAA,UAC7B,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,UACV,eAAe;AAAA,QACjB;AAAA,QACA,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAEA,cAAQ,IAAI,yCAAkC,MAAM;AAEpD,YAAM,WAAW,MAAM,MAAM,KAAK,SAAS;AAAA,QACzC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,eAAe,KAAK;AAAA,QACtB;AAAA,QACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAClC,CAAC;AAED,YAAM,eAAe,MAAM,SAAS,KAAK;AACzC,cAAQ;AAAA,QACN;AAAA,QACA,KAAK,UAAU,cAAc,MAAM,CAAC;AAAA,MACtC;AAEA,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,UACR,oBAAoB,SAAS,YAAY,KAAK;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,WAAW,aAAa;AAAA,MAC1B;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA0B,KAAK;AAC7C,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AA/Fa;;;ACEN,IAAM,oBAAN,MAAwB;AAAA,EAC7B,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,eAAe,IAAI,aAAa,GAAG;AAAA,EAC1C;AAAA,EAEA,MAAM,WAAW,WAAW;AAC1B,QAAI;AACF,cAAQ,IAAI,oCAA6B;AAAA,QACvC,OAAO,UAAU;AAAA,QACjB,MAAM,UAAU;AAAA,QAChB,QAAQ,UAAU;AAAA,MACpB,CAAC;AAED,YAAM,KAAK,OAAO,WAAW;AAC7B,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAU,oBAAI,KAAK,GAAE,YAAY;AAAA,QACjC,eAAc,oBAAI,KAAK,GAAE,YAAY;AAAA,MACvC;AAEA,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB,eAAe;AAAA,QACf,KAAK,UAAU,SAAS;AAAA,MAC1B;AAGA,YAAM,KAAK,iBAAiB,SAAS;AAErC,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,iCAA4B,KAAK;AAC/C,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,WAAW;AAChC,YAAQ,IAAI,8BAAuB,UAAU,UAAU,UAAU,OAAO;AAExE,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,aAAa;AAAA,QACrC,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAEA,UAAI,OAAO,SAAS;AAClB,gBAAQ,IAAI,qCAAgC,UAAU,OAAO;AAE7D,cAAM,QAAQ,IAAI;AAAA,UAChB,KAAK,IAAI,SAAS,OAAO,eAAe,UAAU,IAAI;AAAA,UACtD,KAAK,IAAI,SAAS;AAAA,YAChB,cAAc,UAAU;AAAA,YACxB,KAAK,UAAU;AAAA,cACb,GAAG;AAAA,cACH,QAAQ;AAAA,cACR,SAAQ,oBAAI,KAAK,GAAE,YAAY;AAAA,cAC/B,WAAW,OAAO;AAAA,YACpB,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,OAAO;AACL,cAAM,IAAI,MAAM,OAAO,SAAS,sBAAsB;AAAA,MACxD;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,qCAAgC,UAAU,UAAU,KAAK;AAGvE,YAAM,YAAY,UAAU,YAAY,KAAK;AAC7C,UAAI,YAAY,GAAG;AACjB,cAAM,KAAK,aAAa,WAAW,KAAK;AAAA,MAC1C,OAAO;AACL,cAAM,KAAK,cAAc,WAAW,UAAU,KAAK;AAAA,MACrD;AAEA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,WAAW,OAAO;AACnC,UAAM,QAAQ,IAAI;AAAA,MAChB,KAAK,IAAI,SAAS,OAAO,eAAe,UAAU,IAAI;AAAA,MACtD,KAAK,IAAI,SAAS;AAAA,QAChB,gBAAgB,UAAU;AAAA,QAC1B,KAAK,UAAU;AAAA,UACb,GAAG;AAAA,UACH,QAAQ;AAAA,UACR,OAAO,MAAM;AAAA,UACb,WAAU,oBAAI,KAAK,GAAE,YAAY;AAAA,QACnC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,cAAc,WAAW,UAAU,OAAO;AAE9C,UAAM,eAAe,KAAK,IAAI,GAAG,WAAW,CAAC;AAC7C,UAAM,eAAe,IAAI;AAAA,MACvB,KAAK,IAAI,IAAI,eAAe,KAAK;AAAA,IACnC,EAAE,YAAY;AAEd,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,eAAe,UAAU;AAAA,MACzB,KAAK,UAAU;AAAA,QACb,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA,WAAW,MAAM;AAAA;AAAA,QACjB,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACtC,CAAC;AAAA,IACH;AAEA,YAAQ;AAAA,MACN,2BAAsB,gBAAgB,UAAU,eAAe;AAAA,IACjE;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB;AACrB,QAAI;AACF,YAAM,CAAC,SAAS,MAAM,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,QAChD,KAAK,IAAI,SAAS,KAAK,EAAE,QAAQ,eAAe,CAAC;AAAA,QACjD,KAAK,IAAI,SAAS,KAAK,EAAE,QAAQ,cAAc,CAAC;AAAA,QAChD,KAAK,IAAI,SAAS,KAAK,EAAE,QAAQ,gBAAgB,CAAC;AAAA,MACpD,CAAC;AAED,YAAM,SAAS;AAAA,QACb,SAAS,QAAQ,KAAK;AAAA,QACtB,MAAM,KAAK,KAAK;AAAA,QAChB,QAAQ,OAAO;AAAA,QACf,SAAS;AAAA,UACP,SAAS,MAAM,KAAK,eAAe,cAAc;AAAA,UACjD,MAAM,MAAM,KAAK,eAAe,aAAa;AAAA,UAC7C,QAAQ,MAAM,KAAK,eAAe,eAAe;AAAA,QACnD;AAAA,MACF;AAEA,cAAQ,IAAI,2BAAoB,MAAM;AACtC,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,sCAAiC,KAAK;AACpD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,eAAe,QAAQ;AAC3B,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK,EAAE,OAAO,CAAC;AACxD,UAAM,QAAQ,MAAM,QAAQ;AAAA,MAC1B,KAAK,IAAI,OAAO,QAAQ;AACtB,cAAM,QAAQ,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AAC1D,eAAO,EAAE,KAAK,IAAI,MAAM,GAAG,MAAM;AAAA,MACnC,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,eAAe;AACnB,UAAM,SAAS,MAAM,KAAK,eAAe;AACzC,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,aAAa;AAAA,QACX,eAAe,CAAC,CAAC,KAAK,IAAI;AAAA,QAC1B,gBAAgB,CAAC,CAAC,KAAK,IAAI;AAAA,QAC3B,YAAY;AAAA,UACV,QAAQ,CAAC,CAAC,KAAK,IAAI;AAAA,UACnB,QAAQ,KAAK,IAAI,aAAa,UAAU;AAAA,UACxC,SAAS,KAAK,IAAI,cACd,GAAG,KAAK,IAAI,YAAY,UAAU,GAAG,EAAE,SACvC;AAAA,QACN;AAAA,QACA,aAAa,KAAK,IAAI,sBAAsB;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,eAAe;AACnB,UAAM,SAAS,MAAM,KAAK,eAAe;AACzC,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,aAAa;AAAA,QACX,aAAa,CAAC,CAAC,KAAK,IAAI;AAAA,QACxB,WAAW,KAAK,IAAI,mBAAmB,UAAU;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACF;AAhMa;;;ACCN,IAAM,cAAN,MAAkB;AAAA,EACvB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,eAAe,IAAI,aAAa,GAAG;AACxC,SAAK,oBAAoB,IAAI,kBAAkB,GAAG;AAAA,EACpD;AAAA,EAEA,oBAAoB;AAClB,QAAI;AAEF,YAAM,SAAS;AACf,YAAM,YAAY;AAClB,YAAM,YAAY;AAClB,YAAM,UAAU;AAChB,YAAM,UAAU;AAEhB,UAAI,WAAW;AAGf,kBAAY,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,MAAM,CAAC;AAClE,kBAAY,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,MAAM,CAAC;AAClE,kBAAY,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,QAAQ,MAAM,CAAC;AAC9D,kBAAY,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,QAAQ,MAAM,CAAC;AAG9D,YAAM,WAAW,YAAY,YAAY,UAAU;AACnD,eAAS,IAAI,SAAS,QAAQ,IAAI,QAAQ,KAAK;AAC7C,oBAAY,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC;AAAA,MAClE;AAGA,aAAO,SACJ,MAAM,EAAE,EACR,KAAK,MAAM,MAAM,KAAK,OAAO,CAAC,EAC9B,KAAK,EAAE;AAAA,IACZ,SAAS,OAAP;AACA,cAAQ,MAAM,8BAA8B,KAAK;AAEjD,aAAO,OAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,IAC1D;AAAA,EACF;AAAA,EAEA,MAAM,WAAW,UAAU;AACzB,QAAI;AAEF,UAAI,CAAC,SAAS,UAAU,CAAC,SAAS,OAAO;AACvC,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAGA,YAAM,kBAAkB,SAAS,MAAM,YAAY,EAAE,KAAK;AAC1D,YAAM,mBAAmB,SAAS,OAAO,YAAY,EAAE,KAAK;AAG5D,YAAM,eAAe,MAAM,KAAK,IAAI,SAAS;AAAA,QAC3C,SAAS;AAAA,QACT;AAAA,MACF;AAEA,UAAI,gBAAgB,aAAa,WAAW,kBAAkB;AAC5D,cAAM,IAAI,MAAM,8CAA8C;AAAA,MAChE;AAGA,YAAM,iBAAiB,MAAM,KAAK,IAAI,SAAS;AAAA,QAC7C,UAAU;AAAA,QACV;AAAA,MACF;AAEA,UAAI,gBAAgB;AAClB,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAGA,UAAI,gBAAgB,MAAM,KAAK,IAAI,SAAS;AAAA,QAC1C,SAAS;AAAA,QACT;AAAA,MACF;AAGA,YAAM,SAAS,OAAO,WAAW;AACjC,UAAI;AACJ,UAAI;AAEJ,UAAI,eAAe;AAEjB,cAAM,sBAAsB,MAAM,KAAK,IAAI,SAAS;AAAA,UAClD,eAAe,cAAc;AAAA,UAC7B;AAAA,QACF;AAGA,YAAI,CAAC,qBAAqB;AACxB,kBAAQ;AAAA,YACN;AAAA,UACF;AACA,0BAAgB,KAAK,kBAAkB;AACvC,2BAAiB,MAAM,KAAK,cAAc,aAAa;AAAA,QACzD,OAAO;AAEL,0BAAgB,MAAM,KAAK;AAAA,YACzB;AAAA,UACF;AACA,2BAAiB,oBAAoB;AAAA,QACvC;AAAA,MACF,OAAO;AAEL,wBAAgB,KAAK,kBAAkB;AACvC,yBAAiB,MAAM,KAAK,cAAc,aAAa;AAAA,MACzD;AAGA,YAAM,OAAO;AAAA,QACX,IAAI,OAAO,WAAW;AAAA,QACtB,OAAO,SAAS;AAAA,QAChB,QAAQ,SAAS;AAAA,QACjB,UAAU;AAAA;AAAA,QACV,SAAS;AAAA,QACT,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAGA,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,WAAW,KAAK;AAAA,MAClB;AAGA,YAAM,QAAQ,IAAI;AAAA,QAChB,KAAK,IAAI,SAAS,IAAI,QAAQ,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,QAC7D,KAAK,IAAI,SAAS,IAAI,SAAS,KAAK,SAAS,KAAK,UAAU,IAAI,CAAC;AAAA,QACjE,KAAK,IAAI,SAAS,IAAI,UAAU,KAAK,UAAU,KAAK,UAAU,IAAI,CAAC;AAAA,QACnE,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,KAAK,UAAU,IAAI,CAAC;AAAA;AAAA,QAE/D,KAAK,IAAI,SAAS;AAAA,UAChB,eAAe,KAAK;AAAA,UACpB,KAAK,UAAU,WAAW;AAAA,QAC5B;AAAA,MACF,CAAC;AAGD,UAAI;AACF,cAAM,eAAe,MAAM,KAAK,kBAAkB,WAAW;AAAA,UAC3D,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,UAAU;AAAA;AAAA,UACV;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAED,gBAAQ,IAAI,mCAA4B,YAAY;AAAA,MACtD,SAAS,OAAP;AACA,gBAAQ,MAAM,wCAAmC,KAAK;AAAA,MACxD;AAGA,YAAM,WAAW;AAAA,QACf,GAAG,KAAK,gBAAgB,IAAI;AAAA,QAC5B,aAAa;AAAA,UACX,UAAU;AAAA;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,wBAAwB,KAAK;AAC3C,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,UAAU;AAC5B,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,QAAQ,OAAO,QAAQ;AACpC,UAAM,OAAO,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AACvD,WAAO,MAAM,KAAK,IAAI,WAAW,IAAI,CAAC,EACnC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAC1C,KAAK,EAAE;AAAA,EACZ;AAAA;AAAA,EAGA,MAAM,iCAAiC,aAAa;AAElD,WAAO,YAAY;AAAA,EACrB;AAAA,EAEA,MAAM,cAAc,QAAQ;AAC1B,UAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AACpE,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AACA,WAAO,KAAK,gBAAgB,IAAI;AAAA,EAClC;AAAA,EAEA,MAAM,eAAe,QAAQ,UAAU;AACrC,UAAM,cAAc,MAAM,KAAK,IAAI,SAAS;AAAA,MAC1C,eAAe;AAAA,MACf;AAAA,IACF;AACA,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,UAAM,cAAc,MAAM,KAAK,cAAc,QAAQ;AACrD,WAAO,gBAAgB,YAAY;AAAA,EACrC;AAAA,EAEA,MAAM,aAAa,SAAS;AAC1B,QAAI;AAEF,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,WAAW,MAAM;AAErE,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AAGA,YAAM,cAAc;AAAA,QAClB,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACtC;AAGA,YAAM,QAAQ,IAAI;AAAA,QAChB,KAAK,IAAI,SAAS,IAAI,QAAQ,KAAK,MAAM,KAAK,UAAU,WAAW,CAAC;AAAA,QACpE,KAAK,IAAI,SAAS;AAAA,UAChB,SAAS,KAAK;AAAA,UACd,KAAK,UAAU,WAAW;AAAA,QAC5B;AAAA,QACA,KAAK,IAAI,SAAS;AAAA,UAChB,WAAW;AAAA,UACX,KAAK,UAAU,WAAW;AAAA,QAC5B;AAAA,QACA,KAAK,UACH,KAAK,IAAI,SAAS;AAAA,UAChB,UAAU,KAAK;AAAA,UACf,KAAK,UAAU,WAAW;AAAA,QAC5B;AAAA,MACJ,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,SAAS;AAC9B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,WAAW,MAAM;AAErE,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ,KAAK,UAAU;AAAA,QACvB,aAAa,KAAK,eAAe;AAAA,QACjC,QAAQ,KAAK;AAAA,QACb,SACE,KAAK,WAAW,WACZ,yBACA;AAAA,MACR;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,kCAAkC,KAAK;AACrD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,gBAAgB,MAAM;AACpB,UAAM,EAAE,UAAU,GAAG,SAAS,IAAI;AAClC,WAAO;AAAA,EACT;AACF;AA/Ra;;;ACHN,IAAM,gBAAN,MAAoB;AAAA,EACvB,MAAM,eAAe,SAAS,KAAK;AACjC,UAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAE9C,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC3D;AAEA,UAAM,OAAO,MAAM,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AAC/D,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AACF;AAfW;;;ACAN,IAAM,oBAAN,MAAwB;AAAA,EAC7B,iBAAiB,UAAU;AACzB,UAAM,SAAS,CAAC;AAEhB,QAAI,CAAC,SAAS;AAAO,aAAO,KAAK,mBAAmB;AACpD,QAAI,CAAC,SAAS;AAAQ,aAAO,KAAK,oBAAoB;AAEtD,QAAI,SAAS,SAAS,CAAC,KAAK,cAAc,SAAS,KAAK,GAAG;AACzD,aAAO,KAAK,sBAAsB;AAAA,IACpC;AAEA,QAAI,SAAS,UAAU,CAAC,KAAK,eAAe,SAAS,MAAM,GAAG;AAC5D,aAAO,KAAK,uBAAuB;AAAA,IACrC;AAEA,WAAO;AAAA,MACL,SAAS,OAAO,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc,OAAO;AACnB,UAAM,aAAa;AACnB,WAAO,WAAW,KAAK,KAAK;AAAA,EAC9B;AAAA,EAEA,eAAe,QAAQ;AAErB,UAAM,cACJ;AACF,WAAO,YAAY,KAAK,MAAM;AAAA,EAChC;AACF;AAhCa;;;ACMN,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,cAAc,IAAI,YAAY,GAAG;AACtC,SAAK,gBAAgB,IAAI,cAAc;AACvC,SAAK,kBAAkB,IAAI,gBAAgB;AAC3C,SAAK,oBAAoB,IAAI,kBAAkB;AAAA,EACjD;AAAA,EAEA,MAAM,WAAW,SAAS;AACxB,QAAI;AAEF,YAAM,WAAW,MAAM,QAAQ,KAAK;AACpC,cAAQ,IAAI,uBAAuB,QAAQ;AAG3C,YAAM,aAAa,KAAK,kBAAkB,iBAAiB,QAAQ;AACnE,UAAI,CAAC,WAAW,SAAS;AACvB,eAAO,IAAI;AAAA,UACT,KAAK;AAAA,YACH,KAAK,gBAAgB,YAAY,WAAW,OAAO,KAAK,IAAI,CAAC;AAAA,UAC/D;AAAA,UACA,EAAE,QAAQ,KAAK,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,QACjE;AAAA,MACF;AAGA,UAAI,CAAC,SAAS,UAAU,CAAC,SAAS,OAAO;AACvC,eAAO,IAAI;AAAA,UACT,KAAK;AAAA,YACH,KAAK,gBAAgB,YAAY,+BAA+B;AAAA,UAClE;AAAA,UACA,EAAE,QAAQ,KAAK,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,QACjE;AAAA,MACF;AAEA,YAAM,OAAO,MAAM,KAAK,YAAY,WAAW,QAAQ;AAEvD,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB;AAAA,YACnB;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,EAAE,QAAQ,KAAK,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACjE;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,wBAAwB,KAAK;AAE3C,UAAI,MAAM,QAAQ,SAAS,8BAA8B,GAAG;AAC1D,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS,MAAM;AAAA,YACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UACpC,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,MAAM;AAAA,UACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,eAAe,SAAS;AAC5B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAE9C,UAAI,CAAC,QAAQ;AACX,eAAO,IAAI;AAAA,UACT,KAAK;AAAA,YACH,KAAK,gBAAgB,cAAc;AAAA,cACjC,SAAS;AAAA,cACT,SAAS;AAAA,YACX,CAAC;AAAA,UACH;AAAA,UACA,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,QACpD;AAAA,MACF;AAEA,UAAI;AAEF,cAAM,KAAK,cAAc,eAAe,SAAS,KAAK,GAAG;AAEzD,eAAO,IAAI;AAAA,UACT,KAAK;AAAA,YACH,KAAK,gBAAgB,cAAc;AAAA,cACjC,SAAS;AAAA,YACX,CAAC;AAAA,UACH;AAAA,UACA,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,QACpD;AAAA,MACF,SAAS,OAAP;AAEA,eAAO,IAAI;AAAA,UACT,KAAK;AAAA,YACH,KAAK,gBAAgB,cAAc;AAAA,cACjC,SAAS;AAAA,cACT,SAAS,MAAM;AAAA,YACjB,CAAC;AAAA,UACH;AAAA,UACA,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,QACpD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB,YAAY,uBAAuB;AAAA,QAC1D;AAAA,QACA,EAAE,QAAQ,KAAK,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,SAAS;AAC3B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,UAAI,CAAC,QAAQ;AACX,eAAO,IAAI;AAAA,UACT,KAAK;AAAA,YACH,KAAK,gBAAgB,YAAY,qBAAqB;AAAA,UACxD;AAAA,UACA,EAAE,QAAQ,KAAK,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,QACjE;AAAA,MACF;AACA,YAAM,OAAO,MAAM,KAAK,YAAY,cAAc,MAAM;AACxD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,IAAI,CAAC;AAAA,QACvD,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D,EAAE,QAAQ,KAAK,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SAAS,SAAS;AACtB,QAAI;AACF,YAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,YAAM,EAAE,QAAQ,IAAI;AAEpB,UAAI,CAAC,SAAS;AACZ,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,QAAQ;AAAA,YACR,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,YAAY,MAAM,KAAK,YAAY,aAAa,OAAO;AAE7D,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,QAAQ;AAAA,UACR,SAAS,YACL,6BACA;AAAA,QACN,CAAC;AAAA,QACD;AAAA,UACE,QAAQ,YAAY,MAAM;AAAA,UAC1B,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,mBAAmB,SAAS;AAChC,QAAI;AACF,YAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,YAAM,EAAE,QAAQ,IAAI;AAEpB,UAAI,CAAC,SAAS;AACZ,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS,MAAM,KAAK,YAAY,iBAAiB,OAAO;AAC9D,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS,OAAO;AAAA,UAChB,MAAM;AAAA,QACR,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,kCAAkC,KAAK;AACrD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAnPa;;;ACLN,IAAM,kBAAN,MAAsB;AAAA,EAC3B,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EAEA,MAAM,cAAc,SAAS;AAC3B,QAAI;AAEF,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,KAAK;AAG1C,YAAM,OAAO,MAAM,QAAQ;AAAA,QACzB,KAAK,KAAK,IAAI,OAAO,QAAQ;AAC3B,gBAAM,QAAQ,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AAC1D,iBAAO;AAAA,YACL,KAAK,IAAI;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAGA,YAAM,cAAc,KAAK,OAAO,CAAC,KAAK,SAAS;AAC7C,cAAM,SAAS,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC;AACpC,YAAI,CAAC,IAAI,MAAM,GAAG;AAChB,cAAI,MAAM,IAAI,CAAC;AAAA,QACjB;AACA,YAAI,MAAM,EAAE,KAAK,IAAI;AACrB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,SAAS;AAAA,cACP,WAAW,KAAK,KAAK;AAAA,cACrB,UAAU,OAAO,KAAK,WAAW;AAAA,cACjC,eAAe,OAAO;AAAA,gBACpB,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC;AAAA,cAC3D;AAAA,YACF;AAAA,YACA,SAAS;AAAA,YACT,KAAK;AAAA,UACP;AAAA,QACF,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,SAAS;AAC1B,QAAI;AACF,YAAM,EAAE,IAAI,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AACA,YAAM,KAAK,IAAI,SAAS,OAAO,GAAG;AAClC,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,OAAO;AAAA,QAClB,CAAC;AAAA,QACD,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AACpE,YAAM,WAAW,MAAM,KAAK,IAAI,SAAS;AAAA,QACvC,GAAG,YAAY,KAAK,aAAa,KAAK;AAAA,QACtC;AAAA,MACF;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,QACD,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA5Ha;;;ACDN,IAAM,qBAAN,MAAyB;AAAA,EAC9B,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EAEA,MAAM,WAAW,SAAS;AACxB,QAAI;AACF,YAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,YAAM,EAAE,QAAQ,MAAM,QAAQ,UAAU,IAAI;AAG5C,UAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;AAC7C,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,aAAa,CAAC,UAAU,UAAU;AACxC,UAAI,CAAC,WAAW,SAAS,IAAI,GAAG;AAC9B,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,UAAU,OAAO,WAAW;AAElC,YAAM,YAAY;AAAA,QAChB,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAGA,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB,eAAe;AAAA,QACf,KAAK,UAAU,SAAS;AAAA,MAC1B;AAGA,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB,eAAe,UAAU;AAAA,QACzB,KAAK,UAAU,SAAS;AAAA,MAC1B;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,QACR,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,MAAM;AAAA,QACjB,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,SAAS;AAC1B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAE9C,UAAI,CAAC,QAAQ;AACX,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK;AAAA,QAC5C,QAAQ,eAAe;AAAA,MACzB,CAAC;AAED,YAAM,YAAY,MAAM,QAAQ;AAAA,QAC9B,KAAK,IAAI,OAAO,QAAQ;AACtB,gBAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AACzD,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,QACR,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,MAAM;AAAA,QACjB,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA3Ia;;;ACKN,SAAS,iBAAiB,KAAK;AACpC,QAAMI,UAAS,EAAO,EAAE,MAAM,aAAa,CAAC;AAC5C,QAAM,iBAAiB,IAAI,eAAe,GAAG;AAC7C,QAAM,kBAAkB,IAAI,gBAAgB,GAAG;AAC/C,QAAM,qBAAqB,IAAI,mBAAmB,GAAG;AAErD,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAgB,CAAC,YAC3B,eAAe,eAAe,OAAO;AAAA,EACvC;AAGA,EAAAA,QAAO,KAAK,aAAa,CAAC,YAAY,eAAe,SAAS,OAAO,CAAC;AACtE,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAmB,CAAC,YAC9B,eAAe,mBAAmB,OAAO;AAAA,EAC3C;AAGA,EAAAA,QAAO,IAAI,eAAe,CAAC,YAAY,eAAe,cAAc,OAAO,CAAC;AAC5E,EAAAA,QAAO,KAAK,KAAK,CAAC,YAAY,eAAe,WAAW,OAAO,CAAC;AAEhE,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAgB,CAAC,YAC3B,mBAAmB,WAAW,OAAO;AAAA,EACvC;AACA,EAAAA,QAAO,IAAI,UAAU,CAAC,YAAY,mBAAmB,aAAa,OAAO,CAAC;AAG1E,EAAAA,QAAO,IAAI,aAAa,CAAC,YAAY,gBAAgB,cAAc,OAAO,CAAC;AAC3E,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAoB,CAAC,YAC/B,gBAAgB,aAAa,OAAO;AAAA,EACtC;AAEA,SAAOA;AACT;AAhCgB;;;ACJT,IAAM,eAAN,MAAkB;AAAA,EACvB,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EAkGA,MAAM,kBAAkB,QAAQ,MAAM;AACpC,UAAM,QAAQ,MAAM,KAAK,IAAI,SAAS;AAAA,MACpC,GAAG,aAAY,KAAK,eAAe,UAAU;AAAA,MAC7C;AAAA,IACF;AACA,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EAEA,MAAM,wBAAwB,QAAQ,MAAM;AAC1C,UAAM,eAAe,MAAM,KAAK,kBAAkB,QAAQ,IAAI;AAC9D,UAAM,WAAW,MAAM,KAAK,YAAY,MAAM;AAC9C,UAAM,eAAe,MAAM,KAAK,gBAAgB;AAEhD,UAAM,WACJ,SAAS,WACL,gBACA,SAAS,YACT,iBACA;AAEN,UAAM,WAAW,aAAa,OAAO,SAAS,IAAI,EAAE,QAAQ;AAE5D,QAAI,gBAAgB,UAAU;AAC5B,YAAM,IAAI,MAAM,GAAG,sCAAsC;AAAA,IAC3D;AAEA,UAAM,WAAW;AAAA,MACf,OAAO,eAAe;AAAA,MACtB,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,IACtC;AAEA,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,GAAG,aAAY,KAAK,eAAe,UAAU;AAAA,MAC7C,KAAK,UAAU,QAAQ;AAAA,IACzB;AAEA,WAAO,SAAS;AAAA,EAClB;AAAA,EAEA,MAAM,yBAAyB;AAC7B,UAAM,gBAAgB;AAAA,MACpB,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU,CAAC,oBAAoB,mBAAmB;AAAA,MACpD;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU,CAAC,mBAAmB,eAAe;AAAA,MAC/C;AAAA,MACA,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU,CAAC,iBAAiB,oBAAoB,oBAAoB;AAAA,MACtE;AAAA,IACF;AAGA,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,GAAG,aAAY,KAAK;AAAA,MACpB,KAAK,UAAU;AAAA,QACb,QAAQ;AAAA,QACR,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QAClC,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,gBAAgB;AAAA,EACpC;AAAA,EAEA,MAAM,kBAAkB;AACtB,WAAO,MAAM,KAAK,IAAI,SAAS;AAAA,MAC7B,GAAG,aAAY,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,mBAAmB,YAAY;AACnC,UAAM,WAAW;AAAA,MACf,QAAQ;AAAA,MACR,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,SAAS;AAAA,IACX;AACA,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,GAAG,aAAY,KAAK;AAAA,MACpB,KAAK,UAAU,QAAQ;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,YAAY,QAAQ;AACxB,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,IAAI,SAAS;AAAA,QACvC,GAAG,aAAY,KAAK,aAAa;AAAA,QACjC;AAAA,MACF;AAEA,aACE,YAAY;AAAA,QACV,MAAM,aAAY,MAAM;AAAA,QACxB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,IAEJ,SAAS,OAAP;AACA,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,YAAY,QAAQ,SAAS,UAAU,CAAC,GAAG;AAC/C,QAAI,CAAC,OAAO,OAAO,aAAY,KAAK,EAAE,SAAS,OAAO,GAAG;AACvD,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAEA,UAAM,eAAe,MAAM,KAAK,gBAAgB;AAChD,UAAM,gBAAgB,aAAa,OAAO,OAAO;AAEjD,QAAI,CAAC,eAAe;AAClB,YAAM,IAAI,MAAM,qCAAqC,SAAS;AAAA,IAChE;AAGA,UAAM,CAAC,aAAa,YAAY,IAAI,MAAM,QAAQ,IAAI;AAAA,MACpD,KAAK,kBAAkB,QAAQ,QAAQ;AAAA,MACvC,KAAK,kBAAkB,QAAQ,SAAS;AAAA,IAC1C,CAAC;AAED,UAAM,YAAY,oBAAI,KAAK;AAC3B,UAAM,iBAAiB,IAAI,KAAK,SAAS;AACzC,mBAAe,QAAQ,UAAU,QAAQ,IAAI,cAAc,cAAc;AAEzE,UAAM,WAAW;AAAA,MACf,MAAM;AAAA,MACN,WAAW,UAAU,YAAY;AAAA,MACjC,gBAAgB,eAAe,YAAY;AAAA,MAC3C,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,QAAQ,QAAQ,UAAU;AAAA,MAC1B,QAAQ,QAAQ,UAAU;AAAA,MAC1B,eAAe;AAAA,QACb,QAAQ,eAAe;AAAA,QACvB,SAAS,gBAAgB;AAAA,MAC3B;AAAA,IACF;AAEA,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,GAAG,aAAY,KAAK,aAAa;AAAA,MACjC,KAAK,UAAU,QAAQ;AAAA,IACzB;AAEA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,eAAe,MAAM,KAAK,kBAAkB,MAAM;AAAA,IACpD;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,QAAQ;AAC9B,UAAM,QAAQ,MAAM,KAAK,IAAI,SAAS;AAAA,MACpC,GAAG,aAAY,KAAK,eAAe;AAAA,MACnC;AAAA,IACF;AACA,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EAEA,MAAM,oBAAoB,QAAQ;AAChC,UAAM,eAAe,MAAM,KAAK,kBAAkB,MAAM;AACxD,UAAM,WAAW,MAAM,KAAK,YAAY,MAAM;AAC9C,UAAM,eAAe,MAAM,KAAK,gBAAgB;AAEhD,QAAI,gBAAgB,aAAa,OAAO,QAAQ,EAAE,UAAU;AAC1D,YAAM,IAAI,MAAM,iCAAiC;AAAA,IACnD;AAEA,UAAM,WAAW;AAAA,MACf,OAAO,eAAe;AAAA,MACtB,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,IACtC;AAEA,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,GAAG,aAAY,KAAK,eAAe;AAAA,MACnC,KAAK,UAAU,QAAQ;AAAA,IACzB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EAEA,MAAM,gBAAgB,QAAQ;AAC5B,UAAM,YAAY;AAAA,MAChB,OAAO;AAAA,MACP,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,UAAM,QAAQ,IAAI;AAAA,MAChB,KAAK,IAAI,SAAS;AAAA,QAChB,GAAG,aAAY,KAAK,eAAe;AAAA,QACnC,KAAK,UAAU,SAAS;AAAA,MAC1B;AAAA,MACA,KAAK,IAAI,SAAS;AAAA,QAChB,GAAG,aAAY,KAAK,eAAe;AAAA,QACnC,KAAK,UAAU,SAAS;AAAA,MAC1B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,kBAAkB,QAAQ;AAC9B,QAAI;AACF,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,MAAM,QAAQ,IAAI;AAAA,QACpB,KAAK,YAAY,MAAM;AAAA,QACvB,KAAK,gBAAgB;AAAA,QACrB,KAAK,kBAAkB,QAAQ,QAAQ;AAAA,QACvC,KAAK,kBAAkB,QAAQ,SAAS;AAAA,QACxC,KAAK,kBAAkB,QAAQ,OAAO;AAAA,MACxC,CAAC;AAED,YAAM,cACJ,OAAO,iBAAiB,WAAW,aAAa,OAAO;AACzD,YAAM,aAAa,aAAa,OAAO,WAAW;AAGlD,YAAM,cACH,eAAe,MAAM,gBAAgB,MAAM,cAAc;AAC5D,YAAM,uBAAuB,KAAK;AAAA,QAChC;AAAA,QACA,WAAW,eAAe,eAAe;AAAA,MAC3C;AACA,YAAM,wBAAwB,KAAK;AAAA,QACjC;AAAA,QACA,WAAW,gBAAgB,gBAAgB;AAAA,MAC7C;AACA,YAAM,sBAAsB,KAAK;AAAA,QAC/B;AAAA,QACA,WAAW,cAAc,cAAc;AAAA,MACzC;AAGA,YAAM,YACJ,aAAa,kBACb,IAAI,KAAK,aAAa,cAAc,IAAI,oBAAI,KAAK;AAEnD,aAAO;AAAA,QACL;AAAA,QACA,UAAU,WAAW;AAAA,QACrB,OAAO;AAAA,QACP,aAAa,eAAe;AAAA,QAC5B,cAAc,gBAAgB;AAAA,QAC9B,YAAY,cAAc;AAAA,QAC1B,UAAU,WAAW;AAAA,QACrB,aAAa,WAAW;AAAA,QACxB,cAAc,WAAW;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,WAAW;AAAA,QAClB,UAAU,WAAW;AAAA,QACrB,kBAAmB,aAAa,WAAW,WAAY,KAAK,QAAQ,CAAC;AAAA;AAAA,QAErE,gBAAgB,WAAW;AAAA,QAC3B,WAAW,aAAa;AAAA,QACxB,gBAAgB,aAAa;AAAA,QAC7B;AAAA,QACA,QAAQ,aAAa,UAAU;AAAA,QAC/B,QAAQ,aAAa,UAAU;AAAA,QAC/B,cAAc,WAAW;AAAA,QACzB,cAAc,WAAW;AAAA,QACzB,eAAe,WAAW;AAAA,QAC1B,eAAe,WAAW;AAAA,MAC5B;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,mCAAmC,KAAK;AACtD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,eAAe,QAAQ;AAC3B,QAAI;AAEF,YAAM,QAAQ;AAAA,QACZ,OAAO,OAAO;AAAA;AAAA,QACd,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACtC;AAGA,YAAM,QAAQ,IAAI;AAAA,QAChB,KAAK,IAAI,SAAS;AAAA,UAChB,GAAG,aAAY,KAAK,eAAe;AAAA,UACnC,KAAK,UAAU,KAAK;AAAA,QACtB;AAAA,QACA,KAAK,IAAI,SAAS;AAAA,UAChB,GAAG,aAAY,KAAK,eAAe;AAAA,UACnC,KAAK,UAAU,KAAK;AAAA,QACtB;AAAA,MACF,CAAC;AAGD,YAAM,aAAa,MAAM,KAAK,kBAAkB,MAAM;AAGtD,YAAM,cACJ,WAAW,yBAAyB,KACpC,WAAW,0BAA0B;AAEvC,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,iBAAiB,WAAW;AAAA,UAC5B,kBAAkB,WAAW;AAAA,UAC7B,aAAa,WAAW;AAAA,UACxB,cAAc,WAAW;AAAA,UACzB,aAAa,WAAW;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,gCAAgC,KAAK;AACnD,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAzeO,IAAMC,eAAN;AAAM,OAAAA,cAAA;AAAA;AAMX,cANWA,cAMJ,QAAO;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AACf;AAAA;AAGA,cAbWA,cAaJ,SAAQ;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AACR;AAAA;AAGA,cApBWA,cAoBJ,uBAAsB;AAAA,EAC3B,CAAC,aAAY,MAAM,IAAI,GAAG;AAAA,IACxB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,CAAC,oBAAoB,mBAAmB;AAAA,EACpD;AAAA,EACA,CAAC,aAAY,MAAM,MAAM,GAAG;AAAA,IAC1B,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,CAAC,mBAAmB,eAAe;AAAA,EAC/C;AAAA,EACA,CAAC,aAAY,MAAM,IAAI,GAAG;AAAA,IACxB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,CAAC,iBAAiB,oBAAoB,oBAAoB;AAAA,EACtE;AACF;;;AC/FK,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,cAAc,IAAIC,aAAY,GAAG;AACtC,SAAK,kBAAkB,IAAI,gBAAgB;AAC3C,SAAK,gBAAgB,IAAI,cAAc;AAAA,EACzC;AAAA,EAEA,MAAM,mBAAmB,QAAQ;AAC/B,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AACA,UAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AACpE,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY,gBAAgB;AACxD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,QAAQ,CAAC;AAAA,QAC3D,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,SAAS;AAC/B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,OAAO,MAAM,KAAK,mBAAmB,MAAM;AACjD,YAAM,SAAS,MAAM,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAE/D,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,MAAM,CAAC;AAAA,QACzD,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,eAAe,SAAS;AAC5B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,EAAE,KAAK,IAAI,MAAM,QAAQ,KAAK;AAEpC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACpC;AAEA,YAAM,OAAO,MAAM,KAAK,mBAAmB,MAAM;AACjD,YAAM,kBAAkB,MAAM,KAAK,YAAY,YAAY,KAAK,IAAI,IAAI;AAExE,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB;AAAA,YACnB;AAAA,cACE,MAAM,gBAAgB;AAAA,cACtB,WAAW,gBAAgB;AAAA,cAC3B,eAAe,gBAAgB;AAAA,YACjC;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,SAAS;AAC3B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,OAAO,MAAM,KAAK,mBAAmB,MAAM;AAGjD,YAAM,CAAC,OAAO,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,QAC5C,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAAA,QAC1C,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAAA,MAC5C,CAAC;AAED,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB,cAAc;AAAA,YACjC;AAAA,YACA,aAAa,WAAW;AAAA,YACxB,UAAU,WAAW;AAAA,YACrB,OAAO,WAAW;AAAA,YAClB,WAAW,WAAW;AAAA,YACtB,iBAAiB,WAAW,kBAAkB;AAAA,YAC9C,OAAO,WAAW;AAAA,YAClB,UAAU,WAAW;AAAA,UACvB,CAAC;AAAA,QACH;AAAA,QACA,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,OAAO,MAAM,KAAK,mBAAmB,MAAM;AACjD,YAAM,YAAY,MAAM,KAAK,YAAY,gBAAgB,KAAK,EAAE;AAEhE,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB;AAAA,YACnB;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,eAAe,SAAS;AAC5B,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,OAAO,MAAM,KAAK,mBAAmB,MAAM;AAEjD,YAAM,SAAS,MAAM,KAAK,YAAY,eAAe,KAAK,EAAE;AAE5D,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,MAAM,CAAC;AAAA,QACzD,EAAE,SAAS,EAAE,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAlLa;;;AC6EN,SAAS,iBAAiB,KAAK;AACpC,QAAMC,UAAS,EAAO,EAAE,MAAM,aAAa,CAAC;AAC5C,QAAM,iBAAiB,IAAI,eAAe,GAAG;AAG7C,EAAAA,QAAO,IAAI,aAAa,CAAC,YAAY,eAAe,gBAAgB,OAAO,CAAC;AAG5E,EAAAA,QAAO,IAAI,WAAW,CAAC,YAAY,eAAe,kBAAkB,OAAO,CAAC;AAE5E,EAAAA,QAAO,IAAI,YAAY,CAAC,YAAY,eAAe,eAAe,OAAO,CAAC;AAE1E,EAAAA,QAAO,IAAI,UAAU,CAAC,YAAY,eAAe,cAAc,OAAO,CAAC;AAEvE,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAgB,CAAC,YAC3B,eAAe,gBAAgB,OAAO;AAAA,EACxC;AAEA,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAe,CAAC,YAC1B,eAAe,eAAe,OAAO;AAAA,EACvC;AAEA,EAAAA,QAAO,KAAK,0BAA0B,OAAO,YAAY;AACvD,UAAM,cAAc,IAAIC,aAAY,GAAG;AACvC,UAAM,IAAI,SAAS,OAAO,GAAGA,aAAY,KAAK,gBAAgB;AAC9D,UAAM,WAAW,MAAM,YAAY,uBAAuB;AAC1D,WAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,GAAG;AAAA,MACrE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC;AAED,SAAOD;AACT;AAhCgB;;;AC9ET,SAAS,kBAAkB,KAAK;AACrC,QAAME,UAAS,EAAO,EAAE,MAAM,aAAa,CAAC;AAC5C,QAAM,oBAAoB,IAAI,kBAAkB,GAAG;AAEnD,EAAAA,QAAO,KAAK,wBAAwB,YAAY;AAC9C,QAAI;AACF,YAAM,UAAU,MAAM,kBAAkB,aAAa;AACrD,YAAM,YAAY,MAAM,kBAAkB,eAAe;AAEzD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,UACT;AAAA,UACA,eAAe;AAAA,UACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,EAAAA,QAAO,IAAI,gBAAgB,YAAY;AACrC,QAAI;AACF,YAAM,SAAS,MAAM,kBAAkB,eAAe;AACtD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,UACN,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,EAAAA,QAAO,IAAI,eAAe,OAAO,YAAY;AAC3C,UAAMC,qBAAoB,IAAI,kBAAkB,GAAG;AACnD,UAAM,YAAY,MAAMA,mBAAkB,aAAa;AAEvD,WAAO,IAAI;AAAA,MACT,KAAK,UAAU;AAAA,QACb,SAAS;AAAA,QACT,MAAM;AAAA,MACR,CAAC;AAAA,MACD;AAAA,QACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD;AAAA,IACF;AAAA,EACF,CAAC;AAGD,EAAAD,QAAO,KAAK,kBAAkB,YAAY;AACxC,QAAI;AACF,YAAM,UAAU,MAAM,kBAAkB,aAAa;AACrD,YAAM,YAAY,MAAM,kBAAkB,eAAe;AAEzD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,UACT;AAAA,UACA,eAAe;AAAA,UACf,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,EAAAA,QAAO,OAAO,gBAAgB,YAAY;AACxC,QAAI;AACF,YAAM,eAAe,MAAM,kBAAkB,WAAW;AACxD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,WAAW;AAAA,UACpB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,EAAAA,QAAO,IAAI,uBAAuB,OAAO,SAASE,SAAQ;AACxD,QAAI;AACF,YAAMD,qBAAoB,IAAI,kBAAkBC,IAAG;AACnD,YAAM,SAAS,MAAMD,mBAAkB,eAAe;AAEtD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,UACN,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA+B,KAAK;AAClD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,UACb,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,EAAAD,QAAO,IAAI,sBAAsB,OAAO,SAASE,SAAQ;AACvD,QAAI;AACF,YAAMD,qBAAoB,IAAI,kBAAkBC,IAAG;AACnD,YAAM,YAAY,MAAMD,mBAAkB,aAAa;AAEvD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,UACN,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,UACb,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAOD;AACT;AAzMgB;;;ACDT,IAAM,gBAAN,MAAoB;AAAA,EACzB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,WAAW,IAAI;AACpB,SAAK,eAAe,IAAI;AACxB,SAAK,UACH,IAAI,mBAAmB,SACnB,qCACA;AACN,SAAK,cAAc,IAAIG,aAAY,GAAG;AAAA,EACxC;AAAA,EAEA,MAAM,cAAc,UAAU;AAC5B,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,YAAM,cAAc;AAAA,QAClB,MAAM,GAAG;AAAA,QACT,aAAa,aAAa;AAAA,QAC1B,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAEA,YAAM,WAAW,MAAM,MAAM,GAAG,KAAK,gCAAgC;AAAA,QACnE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,eAAe,UAAU;AAAA,UACzB,qBAAqB,QAAQ,KAAK,IAAI;AAAA,QACxC;AAAA,QACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAClC,CAAC;AAED,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI,MAAM,KAAK,WAAW,0BAA0B;AAAA,MAC5D;AAEA,aAAO,KAAK;AAAA,IACd,SAAS,OAAP;AACA,cAAQ,MAAM,kCAAkC,KAAK;AACrD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,mBACJ,QACA,MACA,SAAS,EAAE,QAAQ,OAAO,QAAQ,MAAM,GACxC;AACA,QAAI;AACF,cAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,eAAe,MAAM,KAAK,YAAY,gBAAgB;AAC5D,cAAQ,IAAI,kBAAkB,YAAY;AAE1C,UAAI,CAAC,cAAc,SAAS,IAAI,GAAG;AACjC,cAAM,IAAI,MAAM,iBAAiB,MAAM;AAAA,MACzC;AAEA,YAAM,aAAa,aAAa,OAAO,IAAI;AAC3C,UAAI,YAAY,WAAW;AAG3B,UAAI,aAAa;AACjB,UAAI,OAAO,QAAQ;AACjB,sBAAc,WAAW;AAAA,MAC3B;AACA,UAAI,OAAO,QAAQ;AACjB,sBAAc,WAAW;AAAA,MAC3B;AAGA,YAAM,cAAc,GAAG,WAAW,OAChC,OAAO,SAAS,eAAe,KAC9B,OAAO,SAAS,eAAe;AAClC,YAAM,qBAAqB,KAAK;AAAA,QAC9B;AAAA,QACA;AAAA,MACF;AAEA,YAAM,YAAY,MAAM,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAGA,YAAM,WAAW;AAAA,QACf,MAAM,GAAG;AAAA,QACT,YAAY;AAAA,QACZ,gBAAgB;AAAA,UACd;AAAA,YACE,WAAW;AAAA,cACT,eAAe;AAAA,cACf,gBAAgB;AAAA,YAClB;AAAA,YACA,aAAa;AAAA,YACb,UAAU;AAAA,YACV,cAAc;AAAA,YACd,gBAAgB;AAAA,cACd,aAAa;AAAA,gBACX,OAAO,WAAW,QAAQ,CAAC;AAAA,gBAC3B,eAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,qBAAqB;AAAA,UACnB,uBAAuB;AAAA,UACvB,0BAA0B;AAAA,UAC1B,2BAA2B;AAAA,QAC7B;AAAA,MACF;AAGA,YAAM,OAAO,MAAM,KAAK,YAAY,QAAQ;AAC5C,YAAM,eAAe,MAAM,KAAK;AAAA,QAC9B,KAAK;AAAA,QACL;AAAA,MACF;AAGA,YAAM,qBAAqB;AAAA,QACzB,gBAAgB,aAAa;AAAA,QAC7B,QAAQ,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACN,QAAQ,OAAO,SACX;AAAA,YACE,SAAS;AAAA,YACT,OAAO,WAAW;AAAA,YAClB,UAAU,WAAW;AAAA,UACvB,IACA;AAAA,UACJ,QAAQ,OAAO,SACX;AAAA,YACE,SAAS;AAAA,YACT,OAAO,WAAW;AAAA,YAClB,UAAU,WAAW;AAAA,UACvB,IACA;AAAA,QACN;AAAA,QACA;AAAA,QACA,QAAQ,aAAa;AAAA,QACrB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAEA,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB,gBAAgB;AAAA,QAChB,KAAK,UAAU,kBAAkB;AAAA,MACnC;AAEA,aAAO;AAAA,QACL,GAAG;AAAA,QACH,aAAa,aAAa,MAAM,KAAK,CAAC,SAAS,KAAK,QAAQ,SAAS,GACjE;AAAA,MACN;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,yBAAyB,gBAAgB,QAAQ;AACrD,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,YAAM,eAAe,MAAM,KAAK,uBAAuB,cAAc;AACrE,YAAM,mBAAmB,MAAM,KAAK,oBAAoB,cAAc;AAEtE,YAAM,aAAa,MAAM,KAAK,YAAY,gBAAgB;AAC1D,YAAM,eAAe,WAAW,OAAO,iBAAiB,IAAI;AAG5D,UAAI,WAAW,aAAa;AAC5B,UAAI,OAAO;AAAQ,oBAAY,aAAa;AAC5C,UAAI,OAAO;AAAQ,oBAAY,aAAa;AAG5C,YAAM,WAAW,MAAM;AAAA,QACrB,GAAG,KAAK,oCAAoC;AAAA,QAC5C;AAAA,UACE,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,eAAe,UAAU;AAAA,UAC3B;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,SAAS,aAAa;AAAA,YACtB,iBAAiB;AAAA,cACf,eAAe;AAAA,cACf,OAAO,SAAS,QAAQ,CAAC;AAAA,YAC3B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAGA,YAAM,KAAK,yBAAyB,gBAAgB;AAAA,QAClD;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAED,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB;AACrB,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,GAAG,KAAK,2BAA2B;AAAA,QAC9D,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,QAAQ;AAAA,UACR,eAAe,SAAS;AAAA,YACtB,GAAG,KAAK,YAAY,KAAK;AAAA,UAC3B;AAAA,UACA,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAED,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI,MAAM,KAAK,qBAAqB,4BAA4B;AAAA,MACxE;AAEA,aAAO,KAAK;AAAA,IACd,SAAS,OAAP;AACA,cAAQ,MAAM,8BAA8B,KAAK;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,sBAAsB,gBAAgB;AAC1C,QAAI;AAEF,YAAM,cAAc,MAAM,KAAK,eAAe;AAG9C,YAAM,WAAW,MAAM;AAAA,QACrB,GAAG,KAAK,oCAAoC;AAAA,QAC5C;AAAA,UACE,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,eAAe,UAAU;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,QAAQ,MAAM,SAAS,KAAK;AAClC,cAAM,IAAI,MAAM,MAAM,WAAW,mCAAmC;AAAA,MACtE;AAEA,YAAM,eAAe,MAAM,SAAS,KAAK;AAGzC,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK;AAAA,QAC5C,QAAQ;AAAA,MACV,CAAC;AAED,UAAI,oBAAoB;AACxB,UAAI,SAAS;AAGb,iBAAW,OAAO,MAAM;AACtB,cAAM,SAAS,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AAC3D,YAAI,UAAU,OAAO,mBAAmB,gBAAgB;AACtD,8BAAoB;AACpB,mBAAS,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAC9B;AAAA,QACF;AAAA,MACF;AAGA,aAAO;AAAA,QACL,gBAAgB,aAAa;AAAA,QAC7B,QAAQ,aAAa;AAAA,QACrB,QAAQ,aAAa;AAAA,QACrB,WAAW,aAAa;AAAA,QACxB,iBAAiB,aAAa,cAAc;AAAA,QAC5C,iBAAiB,aAAa,cAAc,cAAc;AAAA,QAC1D,gBAAgB,aAAa,cAAc,yBAAyB;AAAA,QACpE,MAAM,mBAAmB;AAAA,QACzB,OAAO,mBAAmB;AAAA,QAC1B,aAAa,mBAAmB;AAAA,QAChC,WAAW,mBAAmB;AAAA,QAC9B,aAAa,mBAAmB;AAAA,QAChC,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACtC;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,sCAAsC,KAAK;AACzD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,qBAAqB,gBAAgB;AACzC,QAAI;AAEF,YAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,YAAM,WAAW,MAAM;AAAA,QACrB,GAAG,KAAK,oCAAoC;AAAA,QAC5C;AAAA,UACE,SAAS;AAAA,YACP,eAAe,UAAU;AAAA,YACzB,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe,MAAM,SAAS,KAAK;AAEzC,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI;AAAA,UACR,aAAa,WAAW;AAAA,QAC1B;AAAA,MACF;AAGA,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK;AAAA,QAC5C,QAAQ;AAAA,MACV,CAAC;AACD,UAAI,qBAAqB;AACzB,UAAI,SAAS;AAEb,iBAAW,OAAO,MAAM;AACtB,cAAM,SAAS,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AAC3D,YAAI,UAAU,OAAO,mBAAmB,gBAAgB;AACtD,+BAAqB;AACrB,mBAAS,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAC9B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,sBAAsB,CAAC,QAAQ;AAClC,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAGA,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH,QAAQ,aAAa;AAAA,QACrB,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,cAAc,aAAa;AAAA,QAC3B,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACtC;AAGA,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB,gBAAgB;AAAA,QAChB,KAAK,UAAU,aAAa;AAAA,MAC9B;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,cAAc;AAAA,MAChB;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,kCAAkC,KAAK;AACrD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,4BAA4B,YAAY,QAAQ;AAC9C,QAAI,cAAc,GAAG,WAAW;AAAA;AAChC,mBAAe,WAAW,SAAS,KAAK,IAAI,IAAI;AAEhD,QAAI,OAAO,QAAQ;AACjB,qBAAe;AACf,qBAAe,WAAW,cAAc,KAAK,IAAI;AAAA,IACnD;AAEA,QAAI,OAAO,QAAQ;AACjB,qBAAe;AACf,qBAAe,WAAW,cAAc,KAAK,IAAI;AAAA,IACnD;AAEA,WAAO;AAAA,EACT;AACF;AA9Ya;;;ACEN,IAAM,yBAAN,MAA6B;AAAA,EAClC,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,gBAAgB,IAAI,cAAc,GAAG;AAC1C,SAAK,cAAc,IAAIC,aAAY,GAAG;AACtC,SAAK,kBAAkB,IAAI,gBAAgB;AAAA,EAC7C;AAAA,EAEA,MAAM,mBAAmB,SAAS;AAChC,QAAI;AACF,cAAQ,IAAI,mCAAmC;AAG/C,YAAM,UAAU,CAAC;AACjB,cAAQ,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACtC,gBAAQ,GAAG,IAAI;AAAA,MACjB,CAAC;AACD,cAAQ,IAAI,oBAAoB,OAAO;AAEvC,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,cAAQ,IAAI,oBAAoB,CAAC,CAAC,MAAM;AAExC,UAAI,CAAC,QAAQ;AACX,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AACpE,cAAQ,IAAI,eAAe,CAAC,CAAC,IAAI;AAEjC,UAAI,CAAC,MAAM;AACT,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAe,MAAM,KAAK,YAAY,gBAAgB;AAC5D,cAAQ,IAAI,kBAAkB,YAAY;AAG1C,YAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,cAAQ,IAAI,iBAAiB,IAAI;AAEjC,YAAM,EAAE,MAAM,SAAS,EAAE,QAAQ,OAAO,QAAQ,MAAM,EAAE,IAAI;AAE5D,UAAI,OAAO,WAAW,UAAa,OAAO,OAAO,WAAW,WAAW;AACrE,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AACA,UAAI,OAAO,WAAW,UAAa,OAAO,OAAO,WAAW,WAAW;AACrE,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AAEA,UAAI,CAAC,MAAM;AACT,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,UAAI,CAAC,cAAc,SAAS,IAAI,GAAG;AACjC,eAAO,IAAI;AAAA,UACT,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,SAAS,iBAAiB,0BAA0B,OAAO;AAAA,cACzD,cAAc,UAAU,CAAC;AAAA,YAC3B,EAAE,KAAK,IAAI;AAAA,UACb,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,cAAQ,IAAI,kBAAkB;AAAA,QAC5B,aAAa,KAAK,IAAI,mBAAmB;AAAA,QACzC,aAAa,CAAC,CAAC,KAAK,IAAI;AAAA,QACxB,iBAAiB,CAAC,CAAC,KAAK,IAAI;AAAA,QAC5B,QAAQ,KAAK,IAAI;AAAA,MACnB,CAAC;AAKD,cAAQ,IAAI,0CAA0C,IAAI;AAC1D,YAAM,eAAe,MAAM,KAAK,cAAc;AAAA,QAC5C,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAEA,cAAQ,IAAI,yBAAyB,YAAY;AAEjD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,gBAAgB,aAAa;AAAA,YAC7B,aAAa,aAAa;AAAA,YAC1B,MAAM,aAAa;AAAA,YACnB,WAAW,aAAa;AAAA,YACxB,QAAQ,aAAa;AAAA,YACrB,YAAY,aAAa;AAAA,YACzB,QAAQ,aAAa;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,gCAAgC,KAAK;AACnD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,MAAM,WAAW;AAAA,UAC1B,SAAS,MAAM;AAAA,QACjB,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,yBAAyB,SAAS;AACtC,QAAI;AACF,YAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,YAAM,EAAE,gBAAgB,OAAO,IAAI,MAAM,QAAQ,KAAK;AAEtD,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,qBAAqB;AAAA,MACvC;AAEA,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AACpE,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AAEA,YAAM,SAAS,MAAM,KAAK,cAAc;AAAA,QACtC;AAAA,QACA;AAAA,MACF;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,QACR,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AAAA,IAEF;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB;AACtB,QAAI;AACF,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,UACT,KAAK;AAAA,YACH,iBAAiB,CAAC,EAChB,KAAK,IAAI,oBAAoB,KAAK,IAAI;AAAA,YAExC,SAAS,KAAK,IAAI,mBAAmB;AAAA,YACrC,QAAQ,KAAK,IAAI;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,0BAA0B,SAAS;AACvC,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,iBAAiB,IAAI,aAAa,IAAI,iBAAiB;AAE7D,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAEA,cAAQ,IAAI,uCAAuC,cAAc;AAGjE,YAAM,SAAS,MAAM,KAAK,cAAc;AAAA,QACtC;AAAA,MACF;AAGA,YAAM,cAAc,IAAI,IAAI,yBAAyB,KAAK,IAAI,OAAO;AACrE,kBAAY,aAAa,IAAI,UAAU,SAAS;AAChD,kBAAY,aAAa,IAAI,mBAAmB,cAAc;AAE9D,aAAO,SAAS,SAAS,YAAY,SAAS,GAAG,GAAG;AAAA,IACtD,SAAS,OAAP;AACA,cAAQ,MAAM,wCAAwC,KAAK;AAG3D,YAAM,cAAc,IAAI,IAAI,uBAAuB,KAAK,IAAI,OAAO;AACnE,kBAAY,aAAa,IAAI,SAAS,MAAM,OAAO;AAEnD,aAAO,SAAS,SAAS,YAAY,SAAS,GAAG,GAAG;AAAA,IACtD;AAAA,EACF;AACF;AA3Pa;;;ACDN,SAAS,yBAAyB,KAAK;AAC5C,QAAMC,UAAS,EAAO,EAAE,MAAM,qBAAqB,CAAC;AACpD,QAAM,aAAa,IAAI,uBAAuB,GAAG;AAGjD,EAAAA,QAAO,IAAI,SAAS,YAAY;AAC9B,QAAI;AACF,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,UACT,aAAa;AAAA,YACX,iBAAiB,CAAC,EAChB,IAAI,oBAAoB,IAAI;AAAA,YAE9B,SAAS,IAAI,mBAAmB;AAAA,YAChC,QAAQ,IAAI;AAAA,UACd;AAAA,QACF,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,EAAAA,QAAO,KAAK,KAAK,CAAC,YAAY,WAAW,mBAAmB,OAAO,CAAC;AAGpE,EAAAA,QAAO;AAAA,IAAI;AAAA,IAAY,CAAC,YACtB,WAAW,0BAA0B,OAAO;AAAA,EAC9C;AAGA,EAAAA,QAAO,IAAI,2BAA2B,OAAO,YAAY;AACvD,QAAI;AACF,YAAM,iBAAiB,QAAQ,OAAO;AACtC,YAAM,eAAe,MAAM,WAAW,cAAc;AAAA,QAClD;AAAA,MACF;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,MAAM;AAAA,QACR,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,EAAAA,QAAO;AAAA,IAAI;AAAA,IAAW,CAAC,YACrB,WAAW,yBAAyB,OAAO;AAAA,EAC7C;AAGA,EAAAA,QAAO;AAAA,IACL;AAAA,IACA,MACE,IAAI;AAAA,MACF,KAAK,UAAU;AAAA,QACb,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC;AAAA,MACD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD;AAAA,IACF;AAAA,EACJ;AAEA,SAAOA;AACT;AAnGgB;;;ACHT,IAAM,kBAAkB;AAAA,EAC7B,SAAS;AAAA,EACT,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,eAAe;AAAA,MACb,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aACE;AAAA,QACF,MAAM,CAAC,OAAO;AAAA,QACd,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,SAAS,QAAQ;AAAA,gBAC5B,YAAY;AAAA,kBACV,OAAO;AAAA,oBACL,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,IAAI;AAAA,0BACF,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,OAAO;AAAA,0BACL,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,SAAS;AAAA,0BACP,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,UAAU;AAAA,8BACR,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,4BACA,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,0BACF;AAAA,wBACF;AAAA,wBACA,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,QAAQ;AAAA,wBACV;AAAA,wBACA,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,QAAQ;AAAA,wBACV;AAAA,sBACF;AAAA,oBACF;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SACE;AAAA,oBACJ;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,oBAC3C,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SACE;AAAA,oBACJ;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM,CAAC,OAAO;AAAA,QACd,UAAU;AAAA,UACR;AAAA,YACE,YAAY,CAAC;AAAA,UACf;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,aAAa;AAAA,sBACb,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,SAAS;AAAA,0BACP,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,SAAS;AAAA,wBACX;AAAA,wBACA,SAAS;AAAA,0BACP,MAAM;AAAA,0BACN,aACE;AAAA,0BACF,SAAS;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,oBACA,WAAW;AAAA,sBACT,MAAM;AAAA,sBACN,QAAQ;AAAA,sBACR,aAAa;AAAA,sBACb,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,kBACR,OAAO;AAAA,oBACL,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,MAAM;AAAA,wBACJ,SAAS;AAAA,sBACX;AAAA,sBACA,WAAW;AAAA,oBACb;AAAA,oBACA,SAAS;AAAA,kBACX;AAAA,kBACA,SAAS;AAAA,oBACP,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,MAAM;AAAA,wBACJ,SAAS;AAAA,wBACT,SAAS;AAAA,sBACX;AAAA,sBACA,WAAW;AAAA,oBACb;AAAA,oBACA,SAAS;AAAA,kBACX;AAAA,kBACA,SAAS;AAAA,oBACP,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,MAAM;AAAA,wBACJ,SAAS;AAAA,wBACT,SAAS;AAAA,sBACX;AAAA,sBACA,WAAW;AAAA,oBACb;AAAA,oBACA,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,WAAW;AAAA,sBACT,MAAM;AAAA,sBACN,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU;AAAA,UACR;AAAA,YACE,YAAY,CAAC;AAAA,UACf;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,SAAS;AAAA,wBACX;AAAA,wBACA,YAAY;AAAA,0BACV,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,iBAAiB;AAAA,8BACf,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,4BACA,kBAAkB;AAAA,8BAChB,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,4BACA,aAAa;AAAA,8BACX,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,4BACA,cAAc;AAAA,8BACZ,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,4BACA,aAAa;AAAA,8BACX,MAAM;AAAA,8BACN,aAAa;AAAA,8BACb,SAAS;AAAA,4BACX;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,oBACA,WAAW;AAAA,sBACT,MAAM;AAAA,sBACN,QAAQ;AAAA,sBACR,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,WAAW;AAAA,sBACT,MAAM;AAAA,sBACN,QAAQ;AAAA,sBACR,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,aACE;AAAA,QACF,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,MAAM,EAAE,MAAM,kCAAkC;AAAA,4BAChD,QAAQ,EAAE,MAAM,kCAAkC;AAAA,4BAClD,MAAM,EAAE,MAAM,kCAAkC;AAAA,0BAClD;AAAA,wBACF;AAAA,wBACA,WAAW,EAAE,MAAM,UAAU,QAAQ,YAAY;AAAA,wBACjD,SAAS,EAAE,MAAM,SAAS;AAAA,sBAC5B;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aAAa;AAAA,QACb,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,MAAM;AAAA,gBACjB,YAAY;AAAA,kBACV,MAAM;AAAA,oBACJ,MAAM;AAAA,oBACN,MAAM,CAAC,QAAQ,UAAU,MAAM;AAAA,oBAC/B,aAAa;AAAA,kBACf;AAAA,kBACA,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,MAAM,EAAE,MAAM,SAAS;AAAA,wBACvB,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,QAAQ;AAAA,wBACV;AAAA,wBACA,gBAAgB;AAAA,0BACd,MAAM;AAAA,0BACN,QAAQ;AAAA,wBACV;AAAA,wBACA,QAAQ,EAAE,MAAM,UAAU;AAAA,wBAC1B,QAAQ,EAAE,MAAM,UAAU;AAAA,wBAC1B,eAAe;AAAA,0BACb,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,UAAU,EAAE,MAAM,SAAS;AAAA,4BAC3B,UAAU,EAAE,MAAM,SAAS;AAAA,4BAC3B,aAAa,EAAE,MAAM,SAAS;AAAA,4BAC9B,cAAc,EAAE,MAAM,SAAS;AAAA,4BAC/B,OAAO,EAAE,MAAM,SAAS;AAAA,4BACxB,UAAU;AAAA,8BACR,MAAM;AAAA,8BACN,OAAO,EAAE,MAAM,SAAS;AAAA,4BAC1B;AAAA,4BACA,cAAc,EAAE,MAAM,SAAS;AAAA,4BAC/B,cAAc,EAAE,MAAM,SAAS;AAAA,4BAC/B,eAAe;AAAA,8BACb,MAAM;AAAA,8BACN,OAAO,EAAE,MAAM,SAAS;AAAA,4BAC1B;AAAA,4BACA,eAAe;AAAA,8BACb,MAAM;AAAA,8BACN,OAAO,EAAE,MAAM,SAAS;AAAA,4BAC1B;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,oBACA,SAAS,EAAE,MAAM,SAAS;AAAA,kBAC5B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,oBAC3C,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,WAAW;AAAA,sBACT,MAAM;AAAA,sBACN,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aAAa;AAAA,QACb,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,OAAO,EAAE,MAAM,SAAS;AAAA,sBAC1B;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aAAa;AAAA,QACb,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,OAAO,EAAE,MAAM,SAAS;AAAA,wBACxB,WAAW,EAAE,MAAM,UAAU,QAAQ,YAAY;AAAA,sBACnD;AAAA,oBACF;AAAA,oBACA,SAAS,EAAE,MAAM,SAAS;AAAA,kBAC5B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,SAAS;AAAA,gBACpB,YAAY;AAAA,kBACV,SAAS;AAAA,oBACP,MAAM;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,QAAQ;AAAA,sBACN,MAAM;AAAA,oBACR;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,oBACR;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,SAAS;AAAA,gBACpB,YAAY;AAAA,kBACV,SAAS;AAAA,oBACP,MAAM;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,oBACR;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,SAAS;AAAA,0BACP,MAAM;AAAA,wBACR;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,MAAM,CAAC,UAAU,SAAS;AAAA,wBAC5B;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,UAAU;AAAA,wBACZ;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,wBACR;AAAA,wBACA,SAAS;AAAA,0BACP,MAAM;AAAA,wBACR;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,UAAU,WAAW;AAAA,gBAChC,YAAY;AAAA,kBACV,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,WAAW;AAAA,oBACT,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,UAAU;AAAA,oBACR,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,OAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,QAAQ;AAAA,oBACR,WAAW;AAAA,kBACb;AAAA,gBACF;AAAA,gBACA,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX,UAAU;AAAA,sBACR,MAAM;AAAA,sBACN,OAAO;AAAA,sBACP,OAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,SAAS,EAAE,MAAM,SAAS;AAAA,oBAC1B,MAAM,EAAE,MAAM,SAAS;AAAA,kBACzB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,UAAU,WAAW;AAAA,gBAChC,YAAY;AAAA,kBACV,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,WAAW;AAAA,oBACT,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,UAAU;AAAA,oBACR,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,sBACP,WAAW;AAAA,sBACX,UAAU;AAAA,oBACZ;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,QAAQ;AAAA,oBACR,WAAW;AAAA,kBACb;AAAA,gBACF;AAAA,gBACA,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX,UAAU;AAAA,sBACR,WAAW;AAAA,sBACX,UAAU;AAAA,sBACV,UAAU;AAAA,oBACZ;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,SAAS,EAAE,MAAM,SAAS;AAAA,oBAC1B,MAAM,EAAE,MAAM,SAAS;AAAA,kBACzB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,KAAK;AAAA,oBAC1C,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,MAAM,EAAE,MAAM,UAAU,SAAS,GAAG;AAAA,4BACpC,WAAW,EAAE,MAAM,UAAU,SAAS,IAAI;AAAA,4BAC1C,OAAO,EAAE,MAAM,UAAU,SAAS,IAAK;AAAA,4BACvC,gBAAgB,EAAE,MAAM,UAAU,SAAS,OAAO;AAAA,0BACpD;AAAA,wBACF;AAAA,wBACA,SAAS;AAAA,0BACP,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,MAAM,EAAE,MAAM,UAAU,SAAS,GAAG;AAAA,4BACpC,WAAW,EAAE,MAAM,UAAU,SAAS,IAAI;AAAA,4BAC1C,OAAO,EAAE,MAAM,UAAU,SAAS,IAAK;AAAA,4BACvC,gBAAgB,EAAE,MAAM,UAAU,SAAS,OAAO;AAAA,0BACpD;AAAA,wBACF;AAAA,wBACA,OAAO;AAAA,0BACL,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,MAAM,EAAE,MAAM,UAAU,SAAS,GAAG;AAAA,4BACpC,WAAW,EAAE,MAAM,UAAU,SAAS,IAAI;AAAA,4BAC1C,OAAO,EAAE,MAAM,UAAU,SAAS,IAAK;AAAA,4BACvC,gBAAgB,EAAE,MAAM,UAAU,SAAS,OAAO;AAAA,0BACpD;AAAA,wBACF;AAAA,wBACA,MAAM;AAAA,0BACJ,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,MAAM,EAAE,MAAM,UAAU,SAAS,cAAc;AAAA,4BAC/C,SAAS,EAAE,MAAM,UAAU,SAAS,SAAS;AAAA,4BAC7C,gBAAgB;AAAA,8BACd,MAAM;AAAA,8BACN,SAAS;AAAA,4BACX;AAAA,4BACA,WAAW,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,4BAC7C,OAAO,EAAE,MAAM,UAAU,SAAS,KAAK;AAAA,0BACzC;AAAA,wBACF;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,YAAY;AAAA,gCACV,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,gCAC3C,OAAO,EAAE,MAAM,UAAU,SAAS,KAAK;AAAA,gCACvC,UAAU;AAAA,kCACR,MAAM;AAAA,kCACN,OAAO,EAAE,MAAM,SAAS;AAAA,kCACxB,SAAS;AAAA,oCACP;AAAA,oCACA;AAAA,kCACF;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,4BACA,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,YAAY;AAAA,gCACV,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,gCAC3C,OAAO,EAAE,MAAM,UAAU,SAAS,KAAK;AAAA,gCACvC,UAAU;AAAA,kCACR,MAAM;AAAA,kCACN,OAAO,EAAE,MAAM,SAAS;AAAA,kCACxB,SAAS;AAAA,oCACP;AAAA,oCACA;AAAA,kCACF;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAAA,wBACA,UAAU;AAAA,0BACR,MAAM;AAAA,0BACN,OAAO,EAAE,MAAM,SAAS;AAAA,0BACxB,SAAS,CAAC,oBAAoB,mBAAmB;AAAA,wBACnD;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,oBAC3C,OAAO,EAAE,MAAM,SAAS;AAAA,kBAC1B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,MAAM,CAAC,UAAU,SAAS;AAAA,YAC5B;AAAA,UACF;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,KAAK;AAAA,oBAC1C,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,SAAS;AAAA,0BACP,MAAM;AAAA,0BACN,OAAO;AAAA,4BACL,MAAM;AAAA,0BACR;AAAA,wBACF;AAAA,wBACA,YAAY;AAAA,0BACV,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,OAAO,EAAE,MAAM,WAAW,SAAS,GAAG;AAAA,4BACtC,YAAY,EAAE,MAAM,WAAW,SAAS,EAAE;AAAA,4BAC1C,aAAa,EAAE,MAAM,WAAW,SAAS,EAAE;AAAA,4BAC3C,OAAO,EAAE,MAAM,WAAW,SAAS,GAAG;AAAA,0BACxC;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aAAa;AAAA,QACb,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,KAAK;AAAA,oBAC1C,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,OAAO;AAAA,wBACL,MAAM;AAAA,sBACR;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aACE;AAAA,QACF,MAAM,CAAC,eAAe;AAAA,QACtB,UAAU;AAAA,UACR;AAAA,YACE,YAAY,CAAC;AAAA,UACf;AAAA,QACF;AAAA,QACA,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,MAAM;AAAA,gBACjB,YAAY;AAAA,kBACV,MAAM;AAAA,oBACJ,MAAM;AAAA,oBACN,MAAM,CAAC,UAAU,MAAM;AAAA,oBACvB,aACE;AAAA,kBACJ;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,QAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,MAAM;AAAA,kBACR;AAAA,gBACF;AAAA,gBACA,MAAM;AAAA,kBACJ,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,MAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,gBAAgB;AAAA,0BACd,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,SAAS;AAAA,wBACX;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,SACE;AAAA,wBACJ;AAAA,wBACA,MAAM;AAAA,0BACJ,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,SAAS;AAAA,wBACX;AAAA,wBACA,OAAO;AAAA,0BACL,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,aAAa;AAAA,0BACb,MAAM,CAAC,oBAAoB,YAAY,QAAQ;AAAA,0BAC/C,SAAS;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,kBACR,YAAY;AAAA,oBACV,SAAS;AAAA,oBACT,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,MAAM;AAAA,wBACJ,gBAAgB;AAAA,wBAChB,aACE;AAAA,wBACF,MAAM;AAAA,wBACN,OAAO;AAAA,wBACP,QAAQ;AAAA,sBACV;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,oBACR;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,kBACR,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,kBACA,eAAe;AAAA,oBACb,SAAS;AAAA,oBACT,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,kBACA,aAAa;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA,sBACL,SAAS;AAAA,sBACT,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B,KAAK;AAAA,QACH,SAAS;AAAA,QACT,aACE;AAAA,QACF,MAAM,CAAC,eAAe;AAAA,QACtB,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,QAAQ;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,UAAU;AAAA,gBACR,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,SACE;AAAA,gBACJ;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,MAAM,CAAC,eAAe;AAAA,QACtB,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,YAAY;AAAA,kBACV,YAAY;AAAA,oBACV,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,UAAU;AAAA,oBACR,MAAM;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,YAAY;AAAA,oBACZ,UAAU;AAAA,sBACR,IAAI;AAAA,sBACJ,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,YAAY;AAAA,oBACZ,UAAU;AAAA,sBACR,IAAI;AAAA,sBACJ,QAAQ;AAAA,wBACN,OAAO;AAAA,wBACP,UAAU;AAAA,sBACZ;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,UAAU;AAAA,sBACR,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,8CAA8C;AAAA,MAC5C,KAAK;AAAA,QACH,SAAS;AAAA,QACT,aACE;AAAA,QACF,MAAM,CAAC,eAAe;AAAA,QACtB,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,QAAQ;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA,aAAa;AAAA,YACb,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,gBAAgB;AAAA,0BACd,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,SAAS;AAAA,wBACX;AAAA,wBACA,iBAAiB;AAAA,0BACf,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,SAAS;AAAA,wBACX;AAAA,wBACA,iBAAiB;AAAA,0BACf,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,SAAS;AAAA,wBACX;AAAA,wBACA,gBAAgB;AAAA,0BACd,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,MAAM;AAAA,0BACJ,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,OAAO;AAAA,0BACL,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,SAAS;AAAA,wBACX;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,SAAS;AAAA,wBACX;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,SAAS;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,MAAM;AAAA,oBACJ,gBAAgB;AAAA,oBAChB,QAAQ;AAAA,oBACR,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX,iBAAiB;AAAA,oBACjB,iBAAiB;AAAA,oBACjB,gBAAgB;AAAA,oBAChB,MAAM;AAAA,oBACN,OAAO;AAAA,oBACP,aAAa;AAAA,oBACb,WAAW;AAAA,oBACX,aAAa;AAAA,oBACb,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,OAAO;AAAA,sBACL,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,eAAe;AAAA,QACtB,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,MAAM;AAAA,gBACjB,YAAY;AAAA,kBACV,MAAM;AAAA,oBACJ,MAAM;AAAA,oBACN,MAAM,CAAC,QAAQ,UAAU,MAAM;AAAA,oBAC/B,aAAa;AAAA,kBACf;AAAA,kBACA,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,YAAY;AAAA,sBACV,QAAQ;AAAA,wBACN,MAAM;AAAA,wBACN,aAAa;AAAA,wBACb,SAAS;AAAA,sBACX;AAAA,sBACA,QAAQ;AAAA,wBACN,MAAM;AAAA,wBACN,aAAa;AAAA,wBACb,SAAS;AAAA,sBACX;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,sBAAsB;AAAA,kBACpB,OAAO;AAAA,oBACL,MAAM;AAAA,oBACN,QAAQ;AAAA,sBACN,QAAQ;AAAA,sBACR,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,6BAA6B;AAAA,kBAC3B,OAAO;AAAA,oBACL,MAAM;AAAA,oBACN,QAAQ;AAAA,sBACN,QAAQ;AAAA,sBACR,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,gBAAgB;AAAA,0BACd,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,aAAa;AAAA,0BACX,MAAM;AAAA,0BACN,SACE;AAAA,wBACJ;AAAA,wBACA,MAAM;AAAA,0BACJ,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,YAAY;AAAA,gCACV,SAAS;AAAA,kCACP,MAAM;AAAA,kCACN,SAAS;AAAA,gCACX;AAAA,gCACA,OAAO;AAAA,kCACL,MAAM;AAAA,kCACN,SAAS;AAAA,gCACX;AAAA,gCACA,UAAU;AAAA,kCACR,MAAM;AAAA,kCACN,OAAO;AAAA,oCACL,MAAM;AAAA,kCACR;AAAA,kCACA,SAAS;AAAA,oCACP;AAAA,oCACA;AAAA,oCACA;AAAA,oCACA;AAAA,kCACF;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,4BACA,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,YAAY;AAAA,gCACV,SAAS;AAAA,kCACP,MAAM;AAAA,kCACN,SAAS;AAAA,gCACX;AAAA,gCACA,OAAO;AAAA,kCACL,MAAM;AAAA,kCACN,SAAS;AAAA,gCACX;AAAA,gCACA,UAAU;AAAA,kCACR,MAAM;AAAA,kCACN,OAAO;AAAA,oCACL,MAAM;AAAA,kCACR;AAAA,gCACF;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAAA,wBACA,YAAY;AAAA,0BACV,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,6BAA6B;AAAA,MAC3B,KAAK;AAAA,QACH,SAAS;AAAA,QACT,MAAM,CAAC,eAAe;AAAA,QACtB,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,kBAAkB,QAAQ;AAAA,gBACrC,YAAY;AAAA,kBACV,gBAAgB;AAAA,oBACd,MAAM;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,kBACA,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,YAAY;AAAA,sBACV,QAAQ;AAAA,wBACN,MAAM;AAAA,wBACN,aAAa;AAAA,sBACf;AAAA,sBACA,QAAQ;AAAA,wBACN,MAAM;AAAA,wBACN,aAAa;AAAA,sBACf;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,SAAS;AAAA,gBACP,gBAAgB;AAAA,gBAChB,QAAQ;AAAA,kBACN,QAAQ;AAAA,kBACR,QAAQ;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,gBAAgB;AAAA,0BACd,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,UAAU;AAAA,0BACR,MAAM;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,wBACA,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,YAAY;AAAA,4BACV,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,SAAS;AAAA,4BACX;AAAA,4BACA,QAAQ;AAAA,8BACN,MAAM;AAAA,8BACN,SAAS;AAAA,4BACX;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,oBACA,SAAS;AAAA,sBACP,MAAM;AAAA,sBACN,SAAS;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,OAAO;AAAA,QACd,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAAA,QAC7B,aACE;AAAA,QACF,aAAa;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,YACP,oBAAoB;AAAA,cAClB,QAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU,CAAC,UAAU,WAAW;AAAA,gBAChC,YAAY;AAAA,kBACV,QAAQ;AAAA,oBACN,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,WAAW;AAAA,oBACT,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,aAAa;AAAA,oBACb,SAAS;AAAA,kBACX;AAAA,kBACA,UAAU;AAAA,oBACR,MAAM;AAAA,oBACN,aAAa;AAAA,oBACb,SAAS;AAAA,sBACP,UAAU;AAAA,sBACV,UAAU;AAAA,sBACV,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,QAAQ;AAAA,oBACR,WAAW;AAAA,kBACb;AAAA,gBACF;AAAA,gBACA,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,OAAO;AAAA,oBACL,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX,UAAU;AAAA,sBACR,UAAU;AAAA,sBACV,UAAU;AAAA,sBACV,QAAQ;AAAA,sBACR,UAAU,CAAC,QAAQ,IAAI;AAAA,oBACzB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,UAAU;AAAA,oBAC3B,SAAS,EAAE,MAAM,SAAS;AAAA,oBAC1B,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,YAAY;AAAA,wBACV,SAAS,EAAE,MAAM,UAAU;AAAA,wBAC3B,cAAc,EAAE,MAAM,SAAS;AAAA,wBAC/B,gBAAgB,EAAE,MAAM,SAAS;AAAA,wBACjC,WAAW;AAAA,0BACT,MAAM;AAAA,0BACN,QAAQ;AAAA,wBACV;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS;AAAA,kBACT,MAAM;AAAA,oBACJ,SAAS;AAAA,oBACT,cAAc;AAAA,oBACd,gBAAgB;AAAA,oBAChB,WAAW;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,aAAa;AAAA,YACb,SAAS;AAAA,cACP,oBAAoB;AAAA,gBAClB,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,YAAY;AAAA,oBACV,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM;AAAA,oBAC3C,SAAS,EAAE,MAAM,SAAS;AAAA,oBAC1B,WAAW;AAAA,sBACT,MAAM;AAAA,sBACN,QAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS;AAAA,kBACT,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,MACP,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,YAAY;AAAA,UACV,IAAI,EAAE,MAAM,SAAS;AAAA,UACrB,UAAU,EAAE,MAAM,SAAS;AAAA,UAC3B,OAAO,EAAE,MAAM,SAAS;AAAA,UACxB,SAAS,EAAE,MAAM,SAAS;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,UACV,MAAM,EAAE,MAAM,SAAS;AAAA,UACvB,UAAU,EAAE,MAAM,SAAS;AAAA,UAC3B,OAAO,EAAE,MAAM,SAAS;AAAA,UACxB,UAAU;AAAA,YACR,MAAM;AAAA,YACN,OAAO,EAAE,MAAM,SAAS;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB,MAAM;AAAA,QACN,YAAY;AAAA,UACV,SAAS,EAAE,MAAM,UAAU;AAAA,UAC3B,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,YAAY;AAAA,cACV,aAAa,EAAE,MAAM,SAAS;AAAA,cAC9B,UAAU,EAAE,MAAM,SAAS;AAAA,cAC3B,OAAO,EAAE,MAAM,SAAS;AAAA,cACxB,aAAa,EAAE,MAAM,SAAS;AAAA,cAC9B,cAAc,EAAE,MAAM,SAAS;AAAA,cAC/B,YAAY,EAAE,MAAM,SAAS;AAAA,cAC7B,UAAU,EAAE,MAAM,SAAS;AAAA,cAC3B,aAAa,EAAE,MAAM,SAAS;AAAA,cAC9B,cAAc,EAAE,MAAM,SAAS;AAAA,cAC/B,YAAY,EAAE,MAAM,SAAS;AAAA,cAC7B,sBAAsB,EAAE,MAAM,SAAS;AAAA,cACvC,uBAAuB,EAAE,MAAM,SAAS;AAAA,cACxC,qBAAqB,EAAE,MAAM,SAAS;AAAA,cACtC,OAAO,EAAE,MAAM,SAAS;AAAA,cACxB,UAAU;AAAA,gBACR,MAAM;AAAA,gBACN,OAAO,EAAE,MAAM,SAAS;AAAA,cAC1B;AAAA,cACA,iBAAiB,EAAE,MAAM,SAAS;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB,MAAM;AAAA,QACN,YAAY;AAAA,UACV,IAAI,EAAE,MAAM,UAAU,QAAQ,OAAO;AAAA,UACrC,MAAM,EAAE,MAAM,UAAU,MAAM,CAAC,UAAU,SAAS,EAAE;AAAA,UACpD,QAAQ,EAAE,MAAM,SAAS;AAAA,UACzB,WAAW,EAAE,MAAM,UAAU,QAAQ,YAAY;AAAA,UACjD,UAAU;AAAA,YACR,MAAM;AAAA,YACN,sBAAsB;AAAA,YACtB,SAAS;AAAA,cACP,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,UACV,MAAM,EAAE,MAAM,UAAU,QAAQ,OAAO;AAAA,UACvC,QAAQ,EAAE,MAAM,UAAU;AAAA,UAC1B,SAAS,EAAE,MAAM,UAAU;AAAA,UAC3B,OAAO,EAAE,MAAM,UAAU;AAAA,UACzB,SAAS;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,cACL,MAAM;AAAA,cACN,YAAY;AAAA,gBACV,IAAI,EAAE,MAAM,UAAU,QAAQ,OAAO;AAAA,gBACrC,MAAM,EAAE,MAAM,UAAU,MAAM,CAAC,UAAU,WAAW,OAAO,EAAE;AAAA,gBAC7D,QAAQ,EAAE,MAAM,SAAS;AAAA,gBACzB,MAAM,EAAE,MAAM,UAAU,QAAQ,YAAY;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,SAAS;AAAA,YACP;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB,MAAM;AAAA,QACN,YAAY;AAAA,UACV,gBAAgB;AAAA,YACd,MAAM;AAAA,YACN,aAAa;AAAA,UACf;AAAA,UACA,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,MAAM,CAAC,UAAU,MAAM;AAAA,UACzB;AAAA,UACA,OAAO;AAAA,YACL,MAAM;AAAA,YACN,aAAa;AAAA,UACf;AAAA,UACA,WAAW;AAAA,YACT,MAAM;AAAA,YACN,QAAQ;AAAA,UACV;AAAA,UACA,iBAAiB;AAAA,YACf,MAAM;AAAA,YACN,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,UACV,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,UACf;AAAA,UACA,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,YAAY;AAAA,QACV,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAGO,SAAS,eAAe,aAAa;AAC1C,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAeqB,KAAK,UAAU,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaxD;AA7BgB;;;AC/iET,IAAM,uBAAN,MAA0B;AAAA,EAC/B,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EAOA,MAAM,WAAW,QAAQ,MAAM;AAC7B,QAAI;AACF,YAAM,YAAY,OAAO,WAAW;AACpC,YAAM,aAAY,oBAAI,KAAK,GAAE,YAAY;AACzC,YAAM,MAAM,UAAU,MAAM,GAAG,EAAE,CAAC;AAElC,YAAM,eAAe;AAAA,QACnB,IAAI;AAAA,QACJ;AAAA,QACA,MAAM,KAAK;AAAA;AAAA,QACX,QAAQ,KAAK;AAAA,QACb;AAAA,QACA,UAAU,KAAK,YAAY,CAAC;AAAA,MAC9B;AAGA,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB,GAAG,qBAAoB,KAAK,WAAW,UAAU;AAAA,QACjD,KAAK,UAAU,YAAY;AAAA,MAC7B;AAGA,YAAM,WAAW,GAAG,qBAAoB,KAAK,SAAS,UAAU;AAChE,YAAM,gBAAiB,MAAM,KAAK,IAAI,SAAS,IAAI,UAAU,MAAM,KAAM;AAAA,QACvE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS,CAAC;AAAA,MACZ;AAGA,oBAAc,KAAK,IAAI,KAAK;AAE5B,oBAAc,QAAQ,KAAK;AAAA,QACzB,IAAI;AAAA,QACJ,MAAM,KAAK;AAAA,QACX,QAAQ,KAAK;AAAA,QACb,MAAM;AAAA,MACR,CAAC;AAED,YAAM,KAAK,IAAI,SAAS,IAAI,UAAU,KAAK,UAAU,aAAa,CAAC;AAEnE,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,iCAAiC,KAAK;AACpD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,eAAe,QAAQ,UAAU,CAAC,GAAG;AACzC,QAAI;AACF,YAAM;AAAA,QACJ;AAAA,QACA,WAAU,oBAAI,KAAK,GAAE,YAAY;AAAA,QACjC;AAAA,QACA,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,IAAI;AAGJ,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK;AAAA,QAC5C,QAAQ,GAAG,qBAAoB,KAAK,WAAW;AAAA,MACjD,CAAC;AAGD,YAAM,UAAU,MAAM,QAAQ;AAAA,QAC5B,KAAK,IAAI,CAAC,QAAQ,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM,CAAC;AAAA,MAC3D;AAGA,UAAI,kBAAkB,QACnB,OAAO,CAAC,UAAU;AACjB,YAAI,CAAC;AAAO,iBAAO;AAEnB,cAAM,cAAc,CAAC,QAAQ,MAAM,SAAS;AAC5C,cAAM,oBACH,CAAC,aAAa,MAAM,aAAa,cAClC,MAAM,aAAa;AAErB,eAAO,eAAe;AAAA,MACxB,CAAC,EACA,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AAG/D,YAAM,eAAe,gBAAgB;AACrC,YAAM,aAAa,KAAK,KAAK,eAAe,KAAK;AACjD,YAAM,UAAU,OAAO,KAAK;AAG5B,wBAAkB,gBAAgB,MAAM,QAAQ,SAAS,KAAK;AAE9D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,YAAY;AAAA,UACV,OAAO;AAAA,UACP;AAAA,UACA,aAAa;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA+B,KAAK;AAClD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,QAAQ,WAAW,WAAU,oBAAI,KAAK,GAAE,YAAY,GAAG;AACzE,QAAI;AACF,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK;AAAA,QAC5C,QAAQ,GAAG,qBAAoB,KAAK,SAAS;AAAA,MAC/C,CAAC;AAED,YAAM,eAAe,MAAM,QAAQ;AAAA,QACjC,KACG,OAAO,CAAC,QAAQ;AACf,gBAAM,OAAO,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAClC,kBACG,CAAC,aAAa,QAAQ,cAAc,QAAQ,QAAQ,MAAM,GAAG,EAAE,CAAC;AAAA,QAErE,CAAC,EACA,IAAI,CAAC,QAAQ,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM,CAAC;AAAA,MACzD;AAEA,aAAO,aAAa,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,cAAc,EAAE,IAAI,CAAC;AAAA,IACjE,SAAS,OAAP;AACA,cAAQ,MAAM,8BAA8B,KAAK;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AACF;AA3IO,IAAM,sBAAN;AAAM;AAKX,cALW,qBAKJ,QAAO;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AACT;;;ACJK,IAAM,kBAAN,MAAsB;AAAA,EAC3B,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,cAAc,IAAIC,aAAY,GAAG;AACtC,SAAK,kBAAkB,IAAI,gBAAgB;AAC3C,SAAK,gBAAgB,IAAI,cAAc;AACvC,SAAK,iBAAiB,IAAI,oBAAoB,GAAG;AAAA,EACnD;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,UAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAC9C,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AACA,UAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,WAAW,UAAU,MAAM;AACpE,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,iBAAiB;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,gBAAgB,OAAO;AAC/C,YAAM,OAAO,MAAM,QAAQ,KAAK;AAEhC,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW;AACnC,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAGA,YAAM,eAAe,MAAM,KAAK,YAAY;AAAA,QAC1C,KAAK;AAAA,QACL;AAAA,MACF;AACA,YAAM,aAAa,MAAM,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAGnE,YAAM,KAAK,eAAe,WAAW,KAAK,IAAI;AAAA,QAC5C,MAAM;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,MACjB,CAAC;AAED,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB,cAAc;AAAA,YACjC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,KAAK;AAAA,YACb,WAAW,KAAK;AAAA,YAChB,YAAY;AAAA,cACV,SAAS;AAAA,cACT,WAAW,WAAW;AAAA,cACtB,OAAO,WAAW;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ,MAAM,QAAQ,SAAS,gBAAgB,IAAI,MAAM;AAAA,UACzD,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,SAAS;AAC/B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,gBAAgB,OAAO;AAC/C,YAAM,OAAO,MAAM,QAAQ,KAAK;AAEhC,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW;AACnC,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAGA,YAAM,eAAe,MAAM,KAAK,YAAY;AAAA,QAC1C,KAAK;AAAA,QACL;AAAA,MACF;AACA,YAAM,aAAa,MAAM,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAEnE,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB;AAAA,YACnB;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ,KAAK;AAAA,cACb,WAAW,KAAK;AAAA,cAChB,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,WAAW,WAAW;AAAA,gBACtB,OAAO,WAAW;AAAA,cACpB;AAAA,YACF;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ,MAAM,QAAQ,SAAS,gBAAgB,IAAI,MAAM;AAAA,UACzD,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,SAAS;AAC3B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,gBAAgB,OAAO;AAC/C,YAAM,aAAa,MAAM,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAEnE,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB,cAAc;AAAA,YACjC,QAAQ;AAAA,cACN,MAAM,WAAW,eAAe;AAAA,cAChC,WAAW,WAAW;AAAA,cACtB,OAAO,WAAW;AAAA,cAClB,kBACI,WAAW,eAAe,KAAK,WAAW,cAC5C,KACA,QAAQ,CAAC;AAAA,YACb;AAAA,YACA,SAAS;AAAA,cACP,MAAM,WAAW,gBAAgB;AAAA,cACjC,WAAW,WAAW;AAAA,cACtB,OAAO,WAAW;AAAA,cAClB,kBACI,WAAW,gBAAgB,KAAK,WAAW,eAC7C,KACA,QAAQ,CAAC;AAAA,YACb;AAAA,YACA,OAAO;AAAA,cACL,MAAM,WAAW,cAAc;AAAA,cAC/B,WAAW,WAAW;AAAA,cACtB,OAAO,WAAW;AAAA,cAClB,kBACI,WAAW,cAAc,KAAK,WAAW,aAC3C,KACA,QAAQ,CAAC;AAAA,YACb;AAAA,YACA,MAAM;AAAA,cACJ,MAAM,WAAW;AAAA,cACjB,SAAS,WAAW;AAAA,cACpB,gBAAgB,WAAW;AAAA,cAC3B,WAAW,WAAW;AAAA,cACtB,OAAO,WAAW;AAAA,YACpB;AAAA,YACA,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SAAS,WAAW;AAAA,gBACpB,OAAO,WAAW;AAAA,gBAClB,UAAU,WAAW;AAAA,cACvB;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS,WAAW;AAAA,gBACpB,OAAO,WAAW;AAAA,gBAClB,UAAU,WAAW;AAAA,cACvB;AAAA,YACF;AAAA,YACA,UAAU,WAAW;AAAA,UACvB,CAAC;AAAA,QACH;AAAA,QACA;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,gBAAgB,OAAO;AAC/C,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAE/B,YAAM,UAAU;AAAA,QACd,WAAW,IAAI,aAAa,IAAI,WAAW;AAAA,QAC3C,SAAS,IAAI,aAAa,IAAI,SAAS;AAAA,QACvC,MAAM,IAAI,aAAa,IAAI,MAAM;AAAA,QACjC,OAAO,SAAS,IAAI,aAAa,IAAI,OAAO,KAAK,KAAK;AAAA,QACtD,MAAM,SAAS,IAAI,aAAa,IAAI,MAAM,KAAK,GAAG;AAAA,MACpD;AAEA,YAAM,UAAU,MAAM,KAAK,eAAe;AAAA,QACxC,KAAK;AAAA,QACL;AAAA,MACF;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,OAAO,CAAC;AAAA,QAC1D;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,SAAS;AAC3B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,gBAAgB,OAAO;AAC/C,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAE/B,YAAM,YAAY,IAAI,aAAa,IAAI,WAAW;AAClD,YAAM,UAAU,IAAI,aAAa,IAAI,SAAS;AAE9C,YAAM,aAAa,MAAM,KAAK,eAAe;AAAA,QAC3C,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,UAAU,CAAC;AAAA,QAC7D;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,gBAAgB,OAAO;AAC/C,YAAM,OAAO,MAAM,QAAQ,KAAK;AAEhC,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW;AACnC,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAGA,YAAM,eAAe,MAAM,KAAK,YAAY;AAAA,QAC1C,KAAK;AAAA,QACL;AAAA,MACF;AACA,YAAM,aAAa,MAAM,KAAK,YAAY,kBAAkB,KAAK,EAAE;AAGnE,YAAM,KAAK,eAAe,WAAW,KAAK,IAAI;AAAA,QAC5C,MAAM;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,MACjB,CAAC;AAED,aAAO,IAAI;AAAA,QACT,KAAK;AAAA,UACH,KAAK,gBAAgB,cAAc;AAAA,YACjC,SAAS;AAAA,YACT,cAAc;AAAA,YACd,gBAAgB,WAAW;AAAA,YAC3B,WAAW,KAAK;AAAA,YAChB,YAAY;AAAA,cACV,SAAS;AAAA,cACT,WAAW,WAAW;AAAA,cACtB,OAAO,WAAW;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ,MAAM,QAAQ,SAAS,gBAAgB,IAAI,MAAM;AAAA,UACzD,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAzTa;;;ACFN,SAAS,kBAAkB,KAAK;AACrC,QAAMC,UAAS,EAAO,EAAE,MAAM,aAAa,CAAC;AAC5C,QAAM,aAAa,IAAI,gBAAgB,GAAG;AAE1C,EAAAA,QAAO,KAAK,WAAW,CAAC,YAAY,WAAW,gBAAgB,OAAO,CAAC;AACvE,EAAAA,QAAO,KAAK,YAAY,CAAC,YAAY,WAAW,kBAAkB,OAAO,CAAC;AAC1E,EAAAA,QAAO,KAAK,UAAU,CAAC,YAAY,WAAW,gBAAgB,OAAO,CAAC;AACtE,EAAAA,QAAO,IAAI,UAAU,CAAC,YAAY,WAAW,cAAc,OAAO,CAAC;AAEnE,EAAAA,QAAO,IAAI,YAAY,CAAC,YAAY,WAAW,gBAAgB,OAAO,CAAC;AACvE,EAAAA,QAAO,IAAI,UAAU,CAAC,YAAY,WAAW,cAAc,OAAO,CAAC;AACnE,SAAOA;AACT;AAZgB;;;ACCT,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,cAAc,IAAIC,aAAY,GAAG;AACtC,SAAK,oBAAoB,IAAI,kBAAkB,GAAG;AAClD,SAAK,gBACH,IAAI,aAAa,iBACjB,IAAI,gBAAgB,iBACpB,IAAI,mBAAmB;AAAA,EAC3B;AAAA,EAEA,WAAW,SAAS,OAAO,CAAC,GAAG;AAC7B,UAAM,aAAY,oBAAI,KAAK,GAAE,YAAY;AACzC,YAAQ,IAAI,wBAAiB,OAAO;AACpC,YAAQ,IAAI,UAAK,SAAS;AAC1B,YAAQ,IAAI,mBAAY,KAAK,UAAU,MAAM,MAAM,CAAC,GAAG,IAAI;AAAA,EAC7D;AAAA,EAEA,SAAS,SAAS,OAAO;AACvB,YAAQ,MAAM,2BAAsB,OAAO;AAC3C,YAAQ,MAAM,sBAAe,KAAK;AAClC,YAAQ,MAAM,oBAAa,MAAM,OAAO,IAAI;AAAA,EAC9C;AAAA,EAEA,MAAM,uBAAuB,SAAS;AACpC,UAAM,UAAU,QAAQ;AAGxB,UAAM,eAAe,QAAQ,IAAI,mBAAmB,MAAM;AAG1D,QAAI,KAAK,iBAAiB,cAAc;AACtC,cAAQ;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAGA,UAAM,YAAY,KAAK,IAAI;AAC3B,UAAM,iBAAiB,QAAQ,IAAI,wBAAwB;AAC3D,UAAM,YAAY,QAAQ,IAAI,0BAA0B;AACxD,UAAM,YAAY,QAAQ,IAAI,yBAAyB;AACvD,UAAM,UAAU,QAAQ,IAAI,iBAAiB;AAE7C,YAAQ,IAAI,uCAAgC;AAAA,MAC1C,WAAW,YAAY,cAAc;AAAA,MACrC;AAAA,MACA;AAAA,MACA,WAAW,YAAY,cAAc;AAAA,MACrC;AAAA,MACA,aAAa,KAAK,gBAAgB,gBAAgB;AAAA,IACpD,CAAC;AAED,QAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS;AACzE,UAAI,KAAK,eAAe;AACtB,gBAAQ;AAAA,UACN;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AAIA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,oBAAoB,OAAO;AAC/B,SAAK,WAAW,0BAA0B;AAAA,MACxC,WAAW,MAAM;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,WAAW,MAAM;AAAA,IACnB,CAAC;AACD,QAAI;AAEF,YAAM,KAAK,kBAAkB,KAAK;AAGlC,UAAI;AACJ,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACH,eAAK,WAAW,oCAAoC;AACpD,mBAAS,MAAM,KAAK,4BAA4B,KAAK;AACrD;AAAA,QAEF,KAAK;AACH,eAAK,WAAW,+BAA+B;AAC/C,mBAAS,MAAM,KAAK,uBAAuB,KAAK;AAChD;AAAA,QAEF,KAAK;AACH,eAAK,WAAW,sCAAsC;AACtD,mBAAS,MAAM,KAAK,4BAA4B,KAAK;AACrD;AAAA,QAEF,KAAK;AACH,eAAK,WAAW,4BAA4B;AAC5C,mBAAS,MAAM,KAAK,oBAAoB,KAAK;AAC7C;AAAA,QAEF;AACE,eAAK,WAAW,wBAAwB;AAAA,YACtC,WAAW,MAAM;AAAA,UACnB,CAAC;AACD,iBAAO,EAAE,QAAQ,aAAa,WAAW,MAAM,WAAW;AAAA,MAC9D;AAEA,WAAK,WAAW,kCAAkC,MAAM;AACxD,aAAO;AAAA,IACT,SAAS,OAAP;AACA,WAAK,SAAS,6BAA6B,KAAK;AAChD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,4BAA4B,OAAO;AACvC,QAAI;AACF,YAAM,eAAe,MAAM;AAC3B,WAAK,WAAW,sCAAsC;AAAA,QACpD,gBAAgB,aAAa;AAAA,MAC/B,CAAC;AAGD,YAAM,mBAAmB,MAAM,KAAK,oBAAoB,aAAa,EAAE;AACvE,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,2BAA2B,aAAa,IAAI;AAAA,MAC9D;AAEA,WAAK,WAAW,2BAA2B,gBAAgB;AAG3D,WAAK,WAAW,sBAAsB;AAAA,QACpC,QAAQ,iBAAiB;AAAA,QACzB,MAAM,iBAAiB;AAAA,MACzB,CAAC;AACD,YAAM,KAAK,YAAY;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAGA,WAAK,WAAW,0BAA0B;AAAA,QACxC,OAAO,aAAa,WAAW;AAAA,MACjC,CAAC;AACD,YAAM,KAAK,kBAAkB,WAAW;AAAA,QACtC,OAAO,aAAa,WAAW;AAAA,QAC/B,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,MAAM,iBAAiB;AAAA,UACvB,OAAO,iBAAiB;AAAA,UACxB,iBAAiB,aAAa,cAAc;AAAA,QAC9C;AAAA,MACF,CAAC;AAGD,WAAK,WAAW,gCAAgC;AAAA,QAC9C,QAAQ,aAAa;AAAA,QACrB,aAAa,aAAa,cAAc;AAAA,MAC1C,CAAC;AACD,YAAM,KAAK;AAAA,QACT,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb;AAAA,UACE,QAAQ,aAAa;AAAA,UACrB,iBAAiB,aAAa,cAAc;AAAA,UAC5C,aAAa,aAAa,cAAc;AAAA,UACxC,WAAW,aAAa;AAAA,QAC1B;AAAA,MACF;AAEA,YAAM,SAAS,EAAE,QAAQ,aAAa,gBAAgB,aAAa,GAAG;AACtE,WAAK,WAAW,uCAAuC,MAAM;AAC7D,aAAO;AAAA,IACT,SAAS,OAAP;AACA,WAAK,SAAS,mCAAmC,KAAK;AACtD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,uBAAuB,OAAO;AAClC,UAAM,UAAU,MAAM;AACtB,UAAM,iBAAiB,QAAQ;AAC/B,SAAK,WAAW,iCAAiC;AAAA,MAC/C,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAED,UAAM,mBAAmB,MAAM,KAAK,oBAAoB,cAAc;AACtE,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,2BAA2B,gBAAgB;AAAA,IAC7D;AAGA,UAAM,KAAK,cAAc;AAAA,MACvB,IAAI,QAAQ;AAAA,MACZ;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,QAAQ,QAAQ,OAAO;AAAA,MACvB,UAAU,QAAQ,OAAO;AAAA,MACzB,QAAQ,QAAQ;AAAA,MAChB,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ;AAAA,IACtB,CAAC;AAGD,UAAM,KAAK,kBAAkB,WAAW;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,QAAQ,QAAQ,OAAO;AAAA,QACvB,UAAU,QAAQ,OAAO;AAAA,QACzB,MAAM,QAAQ;AAAA,QACd,MAAM,iBAAiB;AAAA,MACzB;AAAA,IACF,CAAC;AAED,WAAO,EAAE,QAAQ,aAAa,WAAW,QAAQ,GAAG;AAAA,EACtD;AAAA,EAEA,MAAM,4BAA4B,OAAO;AACvC,UAAM,eAAe,MAAM;AAC3B,SAAK,WAAW,wCAAwC;AAAA,MACtD,gBAAgB,aAAa;AAAA,IAC/B,CAAC;AACD,UAAM,mBAAmB,MAAM,KAAK,oBAAoB,aAAa,EAAE;AACvE,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,2BAA2B,aAAa,IAAI;AAAA,IAC9D;AAGA,UAAM,KAAK,YAAY,YAAY,iBAAiB,QAAQ,MAAM;AAGlE,UAAM,KAAK,kBAAkB,WAAW;AAAA,MACtC,OAAO,aAAa,WAAW;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,kBAAkB,aAAa;AAAA,QAC/B,cAAc,iBAAiB;AAAA,MACjC;AAAA,IACF,CAAC;AAGD,UAAM,KAAK;AAAA,MACT,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb;AAAA,QACE,QAAQ,aAAa;AAAA,QACrB,kBAAkB,aAAa;AAAA,MACjC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,aAAa,gBAAgB,aAAa,GAAG;AAAA,EAChE;AAAA,EAEA,MAAM,oBAAoB,OAAO;AAC/B,UAAM,eAAe,MAAM;AAC3B,UAAM,mBAAmB,MAAM,KAAK,oBAAoB,aAAa,EAAE;AACvE,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,2BAA2B,aAAa,IAAI;AAAA,IAC9D;AAGA,UAAM,KAAK,kBAAkB,WAAW;AAAA,MACtC,OAAO,aAAa,WAAW;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,QAAQ,aAAa,aAAa,oBAAoB;AAAA,QACtD,UAAU,aAAa,aAAa,oBAAoB;AAAA,QACxD,qBAAqB,aAAa,aAAa;AAAA,QAC/C,WAAW,aAAa,aAAa;AAAA,MACvC;AAAA,IACF,CAAC;AAGD,UAAM,KAAK;AAAA,MACT,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb;AAAA,QACE,QAAQ,aAAa;AAAA,QACrB,qBAAqB,aAAa,aAAa;AAAA,QAC/C,mBAAmB,aAAa,aAAa;AAAA,MAC/C;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,UAAU,gBAAgB,aAAa,GAAG;AAAA,EAC7D;AAAA;AAAA,EAGA,MAAM,oBAAoB,gBAAgB;AACxC,SAAK,WAAW,gCAAgC,EAAE,eAAe,CAAC;AAClE,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK,EAAE,QAAQ,gBAAgB,CAAC;AAEzE,eAAW,OAAO,MAAM;AACtB,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AACzD,UAAI,MAAM,mBAAmB,gBAAgB;AAC3C,aAAK,WAAW,2BAA2B;AAAA,UACzC,QAAQ,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,UAC7B,cAAc;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL,GAAG;AAAA,UACH,QAAQ,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAEA,SAAK,WAAW,0BAA0B,EAAE,eAAe,CAAC;AAC5D,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,kBAAkB,OAAO;AAC7B,UAAM,UAAU,iBAAiB,MAAM;AACvC,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB;AAAA,MACA,KAAK,UAAU;AAAA,QACb,GAAG;AAAA,QACH,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,MACrC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,MAAM,yBAAyB,QAAQ,gBAAgB,SAAS;AAC9D,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,KAAK,MAAM;AAEpD,QAAI,QAAQ,KAAK,mBAAmB,gBAAgB;AAClD,YAAM,KAAK,IAAI,SAAS;AAAA,QACtB;AAAA,QACA,KAAK,UAAU;AAAA,UACb,GAAG;AAAA,UACH,GAAG;AAAA,UACH,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,SAAS;AAC3B,UAAM,KAAK,IAAI,SAAS;AAAA,MACtB,WAAW,QAAQ;AAAA,MACnB,KAAK,UAAU;AAAA,QACb,GAAG;AAAA,QACH,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,MACrC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AA7Va;;;ACDN,IAAM,oBAAN,MAAwB;AAAA,EAC7B,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,iBAAiB,IAAI,eAAe,GAAG;AAC5C,SAAK,kBAAkB,IAAI,gBAAgB;AAAA,EAC7C;AAAA,EAEA,MAAM,oBAAoB,SAAS;AACjC,QAAI;AAEF,YAAM,KAAK,eAAe,uBAAuB,OAAO;AAGxD,YAAM,QAAQ,MAAM,QAAQ,KAAK;AACjC,YAAM,SAAS,MAAM,KAAK,eAAe,oBAAoB,KAAK;AAElE,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,MAAM,CAAC;AAAA,QACzD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,6BAA6B,KAAK;AAChD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ,MAAM,UAAU;AAAA,UACxB,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,SAAS;AAC9B,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,eAAe,iBAAiB;AAC1D,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,MAAM,CAAC;AAAA,QACzD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,eAAe,gBAAgB;AACzD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,cAAc,MAAM,CAAC;AAAA,QACzD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,aAAO,IAAI;AAAA,QACT,KAAK,UAAU,KAAK,gBAAgB,YAAY,MAAM,OAAO,CAAC;AAAA,QAC9D;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA1Ea;;;ACAN,SAAS,oBAAoB,KAAK;AACvC,QAAMC,UAAS,EAAO,EAAE,MAAM,gBAAgB,CAAC;AAC/C,QAAM,oBAAoB,IAAI,kBAAkB,GAAG;AAGnD,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAW,CAAC,YACtB,kBAAkB,oBAAoB,OAAO;AAAA,EAC/C;AAGA,EAAAA,QAAO;AAAA,IAAI;AAAA,IAAW,CAAC,YACrB,kBAAkB,iBAAiB,OAAO;AAAA,EAC5C;AAGA,EAAAA,QAAO;AAAA,IAAI;AAAA,IAAW,CAAC,YACrB,kBAAkB,gBAAgB,OAAO;AAAA,EAC3C;AAEA,SAAOA;AACT;AApBgB;;;ACFT,IAAM,wBAAN,MAA4B;AAAA,EACjC,YAAY,KAAK;AACf,SAAK,MAAM;AAAA,EACb;AAAA,EAEA,MAAM,sBAAsB,SAAS;AACnC,QAAI;AACF,YAAM,EAAE,WAAW,eAAe,IAAI,MAAM,QAAQ,KAAK;AAGzD,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,SAAS,KAAK;AAAA,QAC5C,QAAQ;AAAA,MACV,CAAC;AACD,UAAI,mBAAmB;AACvB,UAAI,SAAS;AAEb,iBAAW,OAAO,MAAM;AACtB,cAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,IAAI,MAAM,MAAM;AACzD,YAAI,MAAM,mBAAmB,gBAAgB;AAC3C,6BAAmB;AACnB,mBAAS,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAC9B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,oBAAoB,CAAC,QAAQ;AAChC,cAAM,IAAI,MAAM,2BAA2B,gBAAgB;AAAA,MAC7D;AAGA,YAAM,OAAO,MAAM,KAAK,IAAI,SAAS,IAAI,QAAQ,UAAU,MAAM;AACjE,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,oCAAoC,gBAAgB;AAAA,MACtE;AAGA,UAAI;AACJ,UAAI,cAAc,0BAA0B;AAC1C,uBAAe,KAAK;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AACL,uBAAe,KAAK;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAGA,YAAM,iBAAiB,OAAO,WAAW;AACzC,YAAM,iBAAiB;AAAA,QACrB,oBAAoB;AAAA,QACpB,mBACE;AAAA,QACF,0BAA0B;AAAA,QAC1B,2BAA2B,QAAQ;AAAA,QACnC,4BAA4B,aAAa;AAAA,QACzC,gBAAgB;AAAA,MAClB;AAGA,YAAM,WAAW,MAAM;AAAA,QACrB,IAAI,IAAI,wBAAwB,QAAQ,GAAG;AAAA,QAC3C;AAAA,UACE,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,MAAM,KAAK,UAAU,YAAY;AAAA,QACnC;AAAA,MACF;AAEA,YAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,SAAS,UAAU;AAAA,UACnB,SAAS;AAAA,YACP,WAAW,aAAa;AAAA,YACxB;AAAA,YACA,cAAc;AAAA,YACd,MAAM,KAAK;AAAA,YACX,WAAW,aAAa;AAAA,YACxB,QAAQ,iBAAiB;AAAA,UAC3B;AAAA,UACA;AAAA,QACF,CAAC;AAAA,QACD;AAAA,UACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,6BAA6B,KAAK;AAChD,aAAO,IAAI;AAAA,QACT,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,qBAAqB,gBAAgB,kBAAkB,MAAM;AAC3D,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AACnC,UAAM,YAAY,OAAO,OAAO,WAAW;AAE3C,WAAO;AAAA,MACL,IAAI,MAAM,OAAO,WAAW;AAAA,MAC5B,aAAa;AAAA,MACb,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,QACR,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO,iBAAiB,MAAM,SAAS;AAAA,UACvC,UAAU;AAAA,UACV,SAAS;AAAA,YACP,UAAU,iBAAiB,MAAM,SAAS;AAAA,UAC5C;AAAA,QACF;AAAA,QACA,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,6BACE;AAAA,QACF,iBAAiB;AAAA,UACf,QAAQ,iBAAiB,QAAQ,QAAQ,KAAK,QAAQ,CAAC;AAAA,UACvD,UAAU;AAAA,QACZ;AAAA,QACA,sBAAsB;AAAA,QACtB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM,mDAAmD;AAAA,YACzD,KAAK;AAAA,YACL,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,MAAM,mDAAmD;AAAA,YACzD,KAAK;AAAA,YACL,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,UACE,MAAM,mEAAmE,OAAO,WAAW;AAAA,UAC3F,KAAK;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,MAAM,mEAAmE,OAAO,WAAW;AAAA,UAC3F,KAAK;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,0BAA0B,WAAW,gBAAgB,kBAAkB,MAAM;AAC3E,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAEnC,WAAO;AAAA,MACL,IAAI,MAAM,OAAO,WAAW;AAAA,MAC5B,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,aAAa;AAAA,MACb,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,SAAS,gBAAgB,UAAU,MAAM,GAAG,EAAE,IAAI,EAAE,YAAY;AAAA,MAChE,UAAU;AAAA,QACR,YAAY,iBAAiB;AAAA,QAC7B,UAAU;AAAA,QACV,YAAY;AAAA,UACV,MAAM;AAAA,YACJ,YAAY,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC;AAAA,YACnC,SAAS;AAAA,UACX;AAAA,UACA,eAAe,KAAK;AAAA,UACpB,UAAU,KAAK;AAAA,QACjB;AAAA,QACA,QAAQ;AAAA,QACR,oBAAoB;AAAA,QACpB,IAAI;AAAA,QACJ,SAAS,iBAAiB;AAAA,QAC1B,cAAc;AAAA,UACZ,qBAAqB;AAAA,YACnB,eAAe;AAAA,YACf,OAAO;AAAA,UACT;AAAA,UACA,kBAAkB;AAAA,YAChB;AAAA,cACE,aAAa;AAAA,cACb,UAAU;AAAA,cACV,kBAAkB;AAAA,cAClB,kBAAkB;AAAA,cAClB,gCAAgC;AAAA,YAClC;AAAA,UACF;AAAA,UACA,cAAc;AAAA,YACZ,QAAQ;AAAA,cACN,eAAe;AAAA,cACf,OAAO,iBAAiB,MAAM,SAAS;AAAA,YACzC;AAAA,YACA,MAAM;AAAA,UACR;AAAA,UACA,mBAAmB,IAAI;AAAA,YACrB,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK;AAAA,UACnC,EAAE,YAAY;AAAA,UACd,uBAAuB;AAAA,QACzB;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,UACE,MAAM,mEAAmE,OAAO,WAAW;AAAA,UAC3F,KAAK;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,QACA;AAAA,UACE,MAAM,mEAAmE,OAAO,WAAW;AAAA,UAC3F,KAAK;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA3Oa;;;ACGN,SAAS,wBAAwB,KAAK;AAC3C,QAAMC,UAAS,EAAO,EAAE,MAAM,qBAAqB,CAAC;AACpD,QAAM,aAAa,IAAI,sBAAsB,GAAG;AAGhD,EAAAA,QAAO;AAAA,IAAK;AAAA,IAAoB,CAAC,YAC/B,WAAW,sBAAsB,OAAO;AAAA,EAC1C;AAEA,SAAOA;AACT;AAVgB;;;ACShB,IAAM,cAAc;AAAA,EAClB,+BAA+B;AAAA;AAAA,EAC/B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,0BAA0B;AAC5B;AAGA,SAAS,cAAc,SAAS;AAC9B,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,SAAS;AAAA,EACX,CAAC;AACH;AAJS;AAOT,SAAS,eAAe,UAAU;AAChC,QAAM,aAAa,IAAI,QAAQ,SAAS,OAAO;AAC/C,SAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,eAAW,IAAI,KAAK,KAAK;AAAA,EAC3B,CAAC;AAED,SAAO,IAAI,SAAS,SAAS,MAAM;AAAA,IACjC,QAAQ,SAAS;AAAA,IACjB,YAAY,SAAS;AAAA,IACrB,SAAS;AAAA,EACX,CAAC;AACH;AAXS;AAaT,IAAM,SAAS,EAAO;AAGtB,eAAe,uBAAuB,KAAK;AACzC,QAAM,cAAc,IAAIC,aAAY,GAAG;AACvC,QAAM,YAAY,uBAAuB;AACzC,UAAQ,IAAI,2BAA2B;AACzC;AAJe;AAOf,eAAe,kBAAkB,KAAK;AACpC,MAAI;AACF,YAAQ,IAAI,8CAA8C;AAC1D,UAAM,oBAAoB,IAAI,kBAAkB,GAAG;AACnD,UAAM,eAAe,MAAM,kBAAkB,eAAe;AAC5D,YAAQ,IAAI,mCAAmC,YAAY;AAC3D,UAAM,UAAU,MAAM,kBAAkB,aAAa,IAAI;AACzD,YAAQ,IAAI,6BAA6B,OAAO;AAChD,UAAM,cAAc,MAAM,kBAAkB,eAAe;AAC3D,YAAQ,IAAI,kCAAkC,WAAW;AACzD,WAAO,EAAE,cAAc,SAAS,YAAY;AAAA,EAC9C,SAAS,OAAP;AACA,YAAQ,MAAM,iCAAiC,KAAK;AACpD,UAAM;AAAA,EACR;AACF;AAfe;AAkBf,OAAO,QAAQ,KAAK,aAAa;AAGjC,OAAO,IAAI,aAAa,MAAM;AAC5B,SAAO;AAAA,IACL,IAAI,SAAS,eAAe,eAAe,GAAG;AAAA,MAC5C,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC;AAGD,OAAO,IAAI,wBAAwB,OAAO,SAAS,QAAQ;AACzD,MAAI;AACF,UAAM,qBAAqB,yBAAyB,GAAG;AACvD,UAAM,WAAW,MAAM,mBAAmB,OAAO,OAAO;AACxD,WAAO,eAAe,QAAQ;AAAA,EAChC,SAAS,OAAP;AACA,YAAQ,MAAM,6BAA6B,KAAK;AAChD,WAAO;AAAA,MACL,IAAI;AAAA,QACF,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,OAAO,IAAI,gBAAgB,OAAO,SAAS,QAAQ;AACjD,QAAM,aAAa,iBAAiB,GAAG;AACvC,QAAM,WAAW,MAAM,WAAW,OAAO,OAAO;AAChD,SAAO,eAAe,QAAQ;AAChC,CAAC;AAGD,OAAO,IAAI,gBAAgB,OAAO,SAAS,QAAQ;AACjD,QAAM,cAAc,kBAAkB,GAAG;AACzC,QAAM,WAAW,MAAM,YAAY,OAAO,OAAO;AACjD,SAAO,eAAe,QAAQ;AAChC,CAAC;AAGD,OAAO,IAAI,gBAAgB,OAAO,SAAS,QAAQ;AACjD,QAAM,aAAa,iBAAiB,GAAG;AACvC,QAAM,WAAW,MAAM,WAAW,OAAO,OAAO;AAChD,SAAO,eAAe,QAAQ;AAChC,CAAC;AAGD,OAAO,IAAI,gBAAgB,OAAO,SAAS,QAAQ;AACjD,MAAI;AACF,UAAM,cAAc,kBAAkB,GAAG;AACzC,UAAM,WAAW,MAAM,YAAY,OAAO,OAAO;AACjD,WAAO,eAAe,QAAQ;AAAA,EAChC,SAAS,OAAP;AACA,YAAQ,MAAM,sBAAsB,KAAK;AACzC,WAAO;AAAA,MACL,IAAI;AAAA,QACF,KAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACf,CAAC;AAAA,QACD;AAAA,UACE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,OAAO,IAAI,wBAAwB,OAAO,SAAS,QAAQ;AACzD,QAAM,aAAa,wBAAwB,GAAG;AAC9C,QAAM,WAAW,MAAM,WAAW,OAAO,OAAO;AAChD,SAAO,eAAe,QAAQ;AAChC,CAAC;AAGD,OAAO,IAAI,mBAAmB,OAAO,SAAS,QAAQ;AACpD,QAAM,gBAAgB,oBAAoB,GAAG;AAC7C,QAAM,WAAW,MAAM,cAAc,OAAO,OAAO;AACnD,SAAO,eAAe,QAAQ;AAChC,CAAC;AAGD,OAAO;AAAA,EAAI;AAAA,EAAK,MACd,eAAe,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC,CAAC;AAC3D;AAGA,IAAO,cAAQ;AAAA;AAAA,EAEb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,QAAI;AACF,YAAM,uBAAuB,GAAG;AAGhC,UAAI,QAAQ,WAAW,WAAW;AAChC,eAAO,cAAc,OAAO;AAAA,MAC9B;AAGA,UACE,QAAQ,WAAW,UACnB,QAAQ,IAAI,SAAS,0BAA0B,GAC/C;AACA,cAAM,UAAU,MAAM,kBAAkB,GAAG;AAC3C,eAAO;AAAA,UACL,IAAI;AAAA,YACF,KAAK,UAAU;AAAA,cACb,SAAS;AAAA,cACT,SAAS;AAAA,cACT,GAAG;AAAA,YACL,CAAC;AAAA,YACD;AAAA,cACE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW,MAAM,OAAO,OAAO,SAAS,KAAK,GAAG;AACtD,aAAO,eAAe,QAAQ;AAAA,IAChC,SAAS,OAAP;AACA,cAAQ,MAAM,iBAAiB,KAAK;AACpC,aAAO;AAAA,QACL,IAAI;AAAA,UACF,KAAK,UAAU;AAAA,YACb,SAAS;AAAA,YACT,OAAO,MAAM;AAAA,UACf,CAAC;AAAA,UACD;AAAA,YACE,QAAQ;AAAA,YACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,UAAU,OAAO,KAAK,KAAK;AAC/B,YAAQ,IAAI,mBAAmB,MAAM,YAAW,oBAAI,KAAK,GAAE,YAAY,GAAG;AAC1E,QAAI;AACF,UAAI,UAAU,kBAAkB,GAAG,CAAC;AAAA,IACtC,SAAS,OAAP;AACA,cAAQ,MAAM,wBAAwB,KAAK;AAAA,IAC7C;AAAA,EACF;AACF;;;AClOA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAASC,IAAP;AACD,cAAQ,MAAM,4CAA4CA,EAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACdf,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,MAAI,IAAI,aAAa,gBAAgB;AACpC,UAAM,OAAO,IAAI,aAAa,IAAI,MAAM,KAAK;AAC7C,UAAM,cAAc,SAAS,aAAa,EAAE,KAAK,CAAC;AAElD,WAAO,IAAI,SAAS,qBAAqB;AAAA,EAC1C;AAEA,QAAM,OAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAKlD,MACC,QAAQ,QAAQ,IAAI,SAAS,GAAG,SAAS,cAAc,KACvD,IAAI,aAAa,kBACjB,KAAK,WAAW,KACf;AACD,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC1C;AAEA,SAAO;AACR,GAvB8B;AAyB9B,IAAO,+BAAQ;;;ACnBf,SAAS,YAAYC,IAAmB;AACvC,SAAO;AAAA,IACN,MAAMA,IAAG;AAAA,IACT,SAASA,IAAG,WAAW,OAAOA,EAAC;AAAA,IAC/B,OAAOA,IAAG;AAAA,IACV,OAAOA,IAAG,UAAU,SAAY,SAAY,YAAYA,GAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAASA,IAAP;AACD,UAAM,QAAQ,YAAYA,EAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACxBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAAA,EAAyB;AACpE;AACA,IAAO,sCAAQ;;;ACanB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["e", "a", "r", "l", "router", "TierService", "TierService", "router", "TierService", "router", "emailQueueService", "env", "TierService", "TierService", "router", "TierService", "router", "TierService", "router", "router", "TierService", "e", "e"]}