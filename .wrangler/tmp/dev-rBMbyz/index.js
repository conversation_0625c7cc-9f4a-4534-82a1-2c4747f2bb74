var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) =>
  key in obj
    ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value,
      })
    : (obj[key] = value);
var __name = (target, value) =>
  __defProp(target, "name", { value, configurable: true });
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};

// .wrangler/tmp/bundle-mB1F46/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url =
    request instanceof URL
      ? request
      : new URL(
          (typeof request === "string"
            ? new Request(request, init)
            : request
          ).url
        );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  },
});

// node_modules/itty-router/dist/itty-router.mjs
var e = /* @__PURE__ */ __name(
  ({ base: e2 = "", routes: r = [] } = {}) => ({
    __proto__: new Proxy(
      {},
      {
        get:
          (a, o, t) =>
          (a2, ...p) =>
            r.push([
              o.toUpperCase(),
              RegExp(
                `^${(e2 + a2)
                  .replace(/(\/?)\*/g, "($1.*)?")
                  .replace(/(\/$)|((?<=\/)\/)/, "")
                  .replace(/(:(\w+)\+)/, "(?<$2>.*)")
                  .replace(/:(\w+)(\?)?(\.)?/g, "$2(?<$1>[^/]+)$2$3")
                  .replace(/\.(?=[\w(])/, "\\.")
                  .replace(
                    /\)\.\?\(([^\[]+)\[\^/g,
                    "?)\\.?($1(?<=\\.)[^\\."
                  )}/*$`
              ),
              p,
            ]) && t,
      }
    ),
    routes: r,
    async handle(e3, ...a) {
      let o,
        t,
        p = new URL(e3.url),
        l = (e3.query = {});
      for (let [e4, r2] of p.searchParams)
        l[e4] = void 0 === l[e4] ? r2 : [l[e4], r2].flat();
      for (let [l2, s, c] of r)
        if ((l2 === e3.method || "ALL" === l2) && (t = p.pathname.match(s))) {
          e3.params = t.groups || {};
          for (let r2 of c)
            if (void 0 !== (o = await r2(e3.proxy || e3, ...a))) return o;
        }
    },
  }),
  "e"
);

// src/services/responseService.js
var ResponseService = class {
  formatSuccess(data, message = null) {
    return {
      success: true,
      data,
      message,
      timestamp: /* @__PURE__ */ new Date().toISOString(),
    };
  }
  formatError(message, statusCode = 500) {
    return {
      success: false,
      message,
      statusCode,
      timestamp: /* @__PURE__ */ new Date().toISOString(),
    };
  }
};
__name(ResponseService, "ResponseService");

// src/services/emailService.js
var EmailService = class {
  constructor(env) {
    if (!env.ZEPTO_TOKEN) {
      console.error(
        "\u274C ZEPTO_TOKEN is not configured in environment variables"
      );
      throw new Error("Email service configuration missing");
    }
    if (!env.ZEPTO_FROM_ADDRESS) {
      console.error(
        "\u274C ZEPTO_FROM_ADDRESS is not configured in environment variables"
      );
      throw new Error("Email service configuration missing");
    }
    this.API_URL = "https://api.zeptomail.com/v1.1/email/template";
    this.token = env.ZEPTO_TOKEN;
    this.fromAddress = env.ZEPTO_FROM_ADDRESS.includes("@")
      ? env.ZEPTO_FROM_ADDRESS
      : `noreply@${env.ZEPTO_FROM_ADDRESS}`;
    this.templateKey =
      "2d6f.15d6311547e5377.k1.27e302f0-a62e-11ef-914f-525400fa05f6.19342aa189f";
  }
  async sendApiKeyEmail(email, username, apiKey, password, domain) {
    console.log("\u{1F4E7} Starting to send email to:", email);
    try {
      const requestBody = {
        template_key: this.templateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID",
        },
        to: [
          {
            email_address: {
              address: email,
              name: email.split("@")[0],
            },
          },
        ],
        merge_info: {
          API_KEY: apiKey,
          // Changed to match template variable
          DOMAIN: domain,
          USER_EMAIL: email,
          TEMP_PASSWORD: password,
          YEAR: /* @__PURE__ */ new Date().getFullYear(),
          AWP_APP: "Superuser.ID",
          DOCS_URL: "https://docs.superuser.id",
          API_DOCS: "https://superuser.id/portal",
          EMAIL_SUPPORT: "<EMAIL>",
        },
        track_clicks: false,
        track_opens: false,
      };
      console.log("\u{1F4E4} Sending email with API key:", apiKey);
      const response = await fetch(this.API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: this.token,
        },
        body: JSON.stringify(requestBody),
      });
      const responseData = await response.json();
      console.log(
        "\u{1F4E8} Email API Response:",
        JSON.stringify(responseData, null, 2)
      );
      if (!response.ok) {
        throw new Error(
          `Email API error: ${response.status} - ${JSON.stringify(
            responseData
          )}`
        );
      }
      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("\u274C Error sending email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
};
__name(EmailService, "EmailService");

// src/services/emailQueueService.js
var EmailQueueService = class {
  constructor(env) {
    this.env = env;
    this.emailService = new EmailService(env);
  }
  async addToQueue(emailData) {
    try {
      console.log("\u{1F680} Adding email to queue:", {
        email: emailData.email,
        type: emailData.type,
        domain: emailData.domain,
      });
      const id = crypto.randomUUID();
      const queueItem = {
        id,
        ...emailData,
        status: "pending",
        attempts: 0,
        queuedAt: /* @__PURE__ */ new Date().toISOString(),
        processAfter: /* @__PURE__ */ new Date().toISOString(),
      };
      await this.env.USERS_KV.put(
        `email_queue:${id}`,
        JSON.stringify(queueItem)
      );
      await this.processQueueItem(queueItem);
      return id;
    } catch (error) {
      console.error("\u274C Error adding to queue:", error);
      throw error;
    }
  }
  async processQueueItem(queueItem) {
    console.log(
      `\u{1F4E7} Processing email ${queueItem.id} for ${queueItem.email}`
    );
    try {
      const result = await this.emailService.sendApiKeyEmail(
        queueItem.email,
        queueItem.username,
        queueItem.apiKey,
        queueItem.password,
        queueItem.domain
      );
      if (result.success) {
        console.log(`\u2705 Email sent successfully to ${queueItem.email}`);
        await Promise.all([
          this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),
          this.env.USERS_KV.put(
            `email_sent:${queueItem.id}`,
            JSON.stringify({
              ...queueItem,
              status: "sent",
              sentAt: /* @__PURE__ */ new Date().toISOString(),
              messageId: result.messageId,
            })
          ),
        ]);
        return result;
      } else {
        throw new Error(result.error || "Failed to send email");
      }
    } catch (error) {
      console.error(
        `\u274C Error processing email for ${queueItem.email}:`,
        error
      );
      const attempts = (queueItem.attempts || 0) + 1;
      if (attempts >= 3) {
        await this.moveToFailed(queueItem, error);
      } else {
        await this.scheduleRetry(queueItem, attempts, error);
      }
      throw error;
    }
  }
  async moveToFailed(queueItem, error) {
    await Promise.all([
      this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),
      this.env.USERS_KV.put(
        `email_failed:${queueItem.id}`,
        JSON.stringify({
          ...queueItem,
          status: "failed",
          error: error.message,
          failedAt: /* @__PURE__ */ new Date().toISOString(),
        })
      ),
    ]);
  }
  async scheduleRetry(queueItem, attempts, error) {
    const delayMinutes = Math.pow(2, attempts - 1);
    const processAfter = new Date(
      Date.now() + delayMinutes * 60 * 1e3
    ).toISOString();
    await this.env.USERS_KV.put(
      `email_queue:${queueItem.id}`,
      JSON.stringify({
        ...queueItem,
        attempts,
        processAfter,
        lastError: error.message,
        // Now error is defined
        lastAttempt: /* @__PURE__ */ new Date().toISOString(),
      })
    );
    console.log(
      `\u23F3 Scheduled retry #${attempts} for ${queueItem.email} after ${delayMinutes} minutes`
    );
  }
  async getQueueStatus() {
    try {
      const [pending, sent, failed] = await Promise.all([
        this.env.USERS_KV.list({ prefix: "email_queue:" }),
        this.env.USERS_KV.list({ prefix: "email_sent:" }),
        this.env.USERS_KV.list({ prefix: "email_failed:" }),
      ]);
      const status = {
        pending: pending.keys.length,
        sent: sent.keys.length,
        failed: failed.length,
        details: {
          pending: await this._getQueueItems("email_queue:"),
          sent: await this._getQueueItems("email_sent:"),
          failed: await this._getQueueItems("email_failed:"),
        },
      };
      console.log("\u{1F4CA} Queue Status:", status);
      return status;
    } catch (error) {
      console.error("\u274C Error getting queue status:", error);
      throw error;
    }
  }
  async _getQueueItems(prefix) {
    const { keys } = await this.env.USERS_KV.list({ prefix });
    const items = await Promise.all(
      keys.map(async (key) => {
        const value = await this.env.USERS_KV.get(key.name, "json");
        return { key: key.name, ...value };
      })
    );
    return items;
  }
  async getDebugInfo() {
    const status = await this.getQueueStatus();
    return {
      queueStatus: status,
      timestamp: /* @__PURE__ */ new Date().toISOString(),
      environment: {
        hasZeptoToken: !!this.env.ZEPTO_TOKEN,
        hasFromAddress: !!this.env.ZEPTO_FROM_ADDRESS,
        zeptoToken: {
          exists: !!this.env.ZEPTO_TOKEN,
          length: this.env.ZEPTO_TOKEN?.length || 0,
          preview: this.env.ZEPTO_TOKEN
            ? `${this.env.ZEPTO_TOKEN.substring(0, 10)}...`
            : null,
        },
        fromAddress: this.env.ZEPTO_FROM_ADDRESS || "not configured",
      },
    };
  }
  async getDebugInfo() {
    const status = await this.getQueueStatus();
    return {
      queueStatus: status,
      timestamp: /* @__PURE__ */ new Date().toISOString(),
      environment: {
        hasZeptoKey: !!this.env.ZEPTOMAIL_API_KEY,
        keyLength: this.env.ZEPTOMAIL_API_KEY?.length || 0,
      },
    };
  }
};
__name(EmailQueueService, "EmailQueueService");

// src/services/userService.js
var UserService = class {
  constructor(env) {
    this.env = env;
    this.emailService = new EmailService(env);
    this.emailQueueService = new EmailQueueService(env);
  }
  _generatePassword() {
    try {
      const length = 12;
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const numbers = "0123456789";
      const special = "!@#$%^&*";
      let password = "";
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += special[Math.floor(Math.random() * special.length)];
      const allChars = lowercase + uppercase + numbers + special;
      for (let i = password.length; i < length; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      return password
        .split("")
        .sort(() => 0.5 - Math.random())
        .join("");
    } catch (error) {
      console.error("Error generating password:", error);
      return `Pass${Math.random().toString(36).substring(2, 10)}!`;
    }
  }
  async createUser(userData) {
    try {
      if (!userData.domain || !userData.email) {
        throw new Error("Domain and email are required");
      }
      const normalizedEmail = userData.email.toLowerCase().trim();
      const normalizedDomain = userData.domain.toLowerCase().trim();
      const existingUser = await this.env.USERS_KV.get(
        `email:${normalizedEmail}`,
        "json"
      );
      if (existingUser && existingUser.domain === normalizedDomain) {
        throw new Error("This email and domain is already registered.");
      }
      const existingDomain = await this.env.USERS_KV.get(
        `domain:${normalizedDomain}`,
        "json"
      );
      if (existingDomain) {
        throw new Error(
          "Domain is already registered. Each domain can only have one API key."
        );
      }
      let existingEmail = await this.env.USERS_KV.get(
        `email:${normalizedEmail}`,
        "json"
      );
      const apiKey = crypto.randomUUID();
      let plainPassword;
      let hashedPassword;
      if (existingEmail) {
        const existingCredentials = await this.env.USERS_KV.get(
          `credentials:${existingEmail.id}`,
          "json"
        );
        if (!existingCredentials) {
          console.error(
            "No credentials found for existing email, generating new password"
          );
          plainPassword = this._generatePassword();
          hashedPassword = await this._hashPassword(plainPassword);
        } else {
          plainPassword = await this._getPlainPasswordFromCredentials(
            existingCredentials
          );
          hashedPassword = existingCredentials.hashedPassword;
        }
      } else {
        plainPassword = this._generatePassword();
        hashedPassword = await this._hashPassword(plainPassword);
      }
      const user = {
        id: crypto.randomUUID(),
        email: userData.email,
        domain: userData.domain,
        password: hashedPassword,
        // Store hashed password
        api_key: apiKey,
        createdAt: /* @__PURE__ */ new Date().toISOString(),
        updatedAt: /* @__PURE__ */ new Date().toISOString(),
      };
      const credentials = {
        hashedPassword,
        apiKey,
        userId: user.id,
        email: user.email,
        createdAt: user.createdAt,
      };
      await Promise.all([
        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
        this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
        this.env.USERS_KV.put(`domain:${user.domain}`, JSON.stringify(user)),
        this.env.USERS_KV.put(`api_key:${apiKey}`, JSON.stringify(user)),
        // Store credentials separately
        this.env.USERS_KV.put(
          `credentials:${user.id}`,
          JSON.stringify(credentials)
        ),
      ]);
      try {
        const emailQueueId = await this.emailQueueService.addToQueue({
          email: user.email,
          domain: user.domain,
          password: plainPassword,
          // Send plain password in email
          apiKey,
          type: "welcome",
        });
        console.log("\u{1F4EC} Welcome email queued:", emailQueueId);
      } catch (error) {
        console.error("\u274C Error queueing welcome email:", error);
      }
      const response = {
        ...this._formatUserData(user),
        credentials: {
          password: plainPassword,
          // Include plain password in initial response
          apiKey,
        },
      };
      return response;
    } catch (error) {
      console.error("Error in createUser:", error);
      throw error;
    }
  }
  async _hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hash = await crypto.subtle.digest("SHA-256", data);
    return Array.from(new Uint8Array(hash))
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("");
  }
  // Helper method to get plain password from credentials
  async _getPlainPasswordFromCredentials(credentials) {
    return credentials.plainPassword;
  }
  async getUserDetail(apiKey) {
    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    return this._formatUserData(user);
  }
  async verifyPassword(userId, password) {
    const credentials = await this.env.USERS_KV.get(
      `credentials:${userId}`,
      "json"
    );
    if (!credentials) {
      throw new Error("Credentials not found");
    }
    const hashedInput = await this._hashPassword(password);
    return hashedInput === credentials.hashedPassword;
  }
  async activateUser(license) {
    try {
      const user = await this.env.USERS_KV.get(`api_key:${license}`, "json");
      if (!user) {
        return false;
      }
      const updatedUser = {
        ...user,
        status: "active",
        activatedAt: /* @__PURE__ */ new Date().toISOString(),
      };
      await Promise.all([
        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(updatedUser)),
        this.env.USERS_KV.put(
          `email:${user.email}`,
          JSON.stringify(updatedUser)
        ),
        this.env.USERS_KV.put(
          `api_key:${license}`,
          JSON.stringify(updatedUser)
        ),
        user.domain &&
          this.env.USERS_KV.put(
            `domain:${user.domain}`,
            JSON.stringify(updatedUser)
          ),
      ]);
      return true;
    } catch (error) {
      console.error("Error activating user:", error);
      return false;
    }
  }
  async getLicenseStatus(license) {
    try {
      const user = await this.env.USERS_KV.get(`api_key:${license}`, "json");
      if (!user) {
        return {
          isValid: false,
          message: "License not found",
        };
      }
      return {
        isValid: true,
        status: user.status || "pending",
        activatedAt: user.activatedAt || null,
        domain: user.domain,
        message:
          user.status === "active"
            ? "License is activated"
            : "License is pending activation",
      };
    } catch (error) {
      console.error("Error checking license status:", error);
      return {
        isValid: false,
        message: "Error checking license status",
      };
    }
  }
  _formatUserData(user) {
    const { password, ...userData } = user;
    return userData;
  }
};
__name(UserService, "UserService");

// src/services/apiKeyService.js
var ApiKeyService = class {
  async validateApiKey(request, env) {
    const apiKey = request.headers.get("x-sps-key");
    if (!apiKey) {
      throw new Error("API Key is required in x-sps-key header");
    }
    const user = await env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return user;
  }
};
__name(ApiKeyService, "ApiKeyService");

// src/services/validationService.js
var ValidationService = class {
  validateUserData(userData) {
    const errors = [];
    if (!userData.email) errors.push("Email is required");
    if (!userData.domain) errors.push("Domain is required");
    if (userData.email && !this._isValidEmail(userData.email)) {
      errors.push("Invalid email format");
    }
    if (userData.domain && !this._isValidDomain(userData.domain)) {
      errors.push("Invalid domain format");
    }
    return {
      isValid: errors.length === 0,
      errors,
    };
  }
  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  _isValidDomain(domain) {
    const domainRegex =
      /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }
};
__name(ValidationService, "ValidationService");

// src/controllers/userController.js
var UserController = class {
  constructor(env) {
    this.env = env;
    this.userService = new UserService(env);
    this.apiKeyService = new ApiKeyService();
    this.responseService = new ResponseService();
    this.validationService = new ValidationService();
  }
  async createUser(request) {
    try {
      const userData = await request.json();
      console.log("Received user data:", userData);
      const validation = this.validationService.validateUserData(userData);
      if (!validation.isValid) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(validation.errors.join(", "))
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }
      if (!userData.domain || !userData.email) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Domain and email are required")
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.createUser(userData);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            user,
            "User created successfully. Please check your email for credentials."
          )
        ),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error creating user:", error);
      if (error.message.includes("Domain is already registered")) {
        return new Response(
          JSON.stringify({
            success: false,
            message: error.message,
            timestamp: /* @__PURE__ */ new Date().toISOString(),
          }),
          {
            status: 200,
            // Keep 200 for domain registration error
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          status: 200,
          // Changed from 400/500 to 200
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async validateApiKey(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: "API Key is required in x-sps-key header",
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }
      try {
        await this.apiKeyService.validateApiKey(request, this.env);
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: true,
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      } catch (error) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: error.message,
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify(
          this.responseService.formatError("Server error occurred")
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async getUserDetail(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("API Key is required")
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.getUserDetail(apiKey);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async activate(request) {
    try {
      const data = await request.json();
      const { license } = data;
      if (!license) {
        return new Response(
          JSON.stringify({
            status: false,
            message: "License key is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const activated = await this.userService.activateUser(license);
      return new Response(
        JSON.stringify({
          status: activated,
          message: activated
            ? "Success activate api key"
            : "Failed activate api key",
        }),
        {
          status: activated ? 200 : 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error activating user:", error);
      return new Response(
        JSON.stringify({
          status: false,
          message: "Failed activate api key",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async checkLicenseStatus(request) {
    try {
      const data = await request.json();
      const { license } = data;
      if (!license) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "License key is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const status = await this.userService.getLicenseStatus(license);
      return new Response(
        JSON.stringify({
          success: status.isValid,
          data: status,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error checking license status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to check license status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
};
__name(UserController, "UserController");

// src/controllers/debugController.js
var DebugController = class {
  constructor(env) {
    this.env = env;
  }
  async listAllKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value,
          };
        })
      );
      const groupedData = data.reduce((acc, item) => {
        const prefix = item.key.split(":")[0];
        if (!acc[prefix]) {
          acc[prefix] = [];
        }
        acc[prefix].push(item);
        return acc;
      }, {});
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            summary: {
              totalKeys: list.keys.length,
              prefixes: Object.keys(groupedData),
              countByPrefix: Object.fromEntries(
                Object.entries(groupedData).map(([k, v]) => [k, v.length])
              ),
            },
            grouped: groupedData,
            raw: data,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async deleteKVData(request) {
    try {
      const { key } = await request.json();
      if (!key) {
        throw new Error("Key is required");
      }
      await this.env.USERS_KV.delete(key);
      return new Response(
        JSON.stringify({
          success: true,
          message: `Key ${key} deleted successfully`,
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getUserTierData(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      const tierData = await this.env.USERS_KV.get(
        `${TierService.KEYS.USER_TIER}:${user.id}`,
        "json"
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            user,
            tierData,
          },
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
};
__name(DebugController, "DebugController");

// src/controllers/apiUsageController.js
var ApiUsageController = class {
  constructor(env) {
    this.env = env;
  }
  async trackUsage(request) {
    try {
      const data = await request.json();
      const { apiKey, type, source, timestamp } = data;
      if (!apiKey || !type || !source || !timestamp) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Missing required fields: apiKey, type, source, timestamp",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const validTypes = ["images", "contents"];
      if (!validTypes.includes(type)) {
        return new Response(
          JSON.stringify({
            success: false,
            message: 'Invalid type. Must be either "images" or "contents"',
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const usageId = crypto.randomUUID();
      const usageData = {
        id: usageId,
        apiKey,
        type,
        source,
        timestamp,
        createdAt: /* @__PURE__ */ new Date().toISOString(),
      };
      await this.env.USERS_KV.put(
        `d_api_usage:${usageId}`,
        JSON.stringify(usageData)
      );
      await this.env.USERS_KV.put(
        `d_api_usage:${apiKey}:${usageId}`,
        JSON.stringify(usageData)
      );
      return new Response(
        JSON.stringify({
          success: true,
          message: "API usage tracked successfully",
          data: usageData,
        }),
        {
          status: 201,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getUserUsage(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API Key is required in X-API-KEY header",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const { keys } = await this.env.USERS_KV.list({
        prefix: `d_api_usage:${apiKey}:`,
      });
      const usageData = await Promise.all(
        keys.map(async (key) => {
          const data = await this.env.USERS_KV.get(key.name, "json");
          return data;
        })
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: usageData,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
};
__name(ApiUsageController, "ApiUsageController");

// src/routes/userRoutes.js
function createUserRouter(env) {
  const router2 = e({ base: "/api/users" });
  const userController = new UserController(env);
  const debugController = new DebugController(env);
  const apiUsageController = new ApiUsageController(env);
  router2.post("/validatekey", (request) =>
    userController.validateApiKey(request)
  );
  router2.post("/activate", (request) => userController.activate(request));
  router2.post("/license/status", (request) =>
    userController.checkLicenseStatus(request)
  );
  router2.get("/userdetail", (request) =>
    userController.getUserDetail(request)
  );
  router2.post("/", (request) => userController.createUser(request));
  router2.post("/usage/track", (request) =>
    apiUsageController.trackUsage(request)
  );
  router2.get("/usage", (request) => apiUsageController.getUserUsage(request));
  router2.get("/debug/kv", (request) => debugController.listAllKVData(request));
  router2.post("/debug/kv/delete", (request) =>
    debugController.deleteKVData(request)
  );
  return router2;
}
__name(createUserRouter, "createUserRouter");

// src/services/tierService.js
var _TierService = class {
  constructor(env) {
    this.env = env;
  }
  async getTypeQuotaUsage(userId, type) {
    const usage = await this.env.USERS_KV.get(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}:${type}`,
      "json"
    );
    return usage?.count || 0;
  }
  async incrementTypeQuotaUsage(userId, type) {
    const currentUsage = await this.getTypeQuotaUsage(userId, type);
    const userTier = await this.getUserTier(userId);
    const tierSettings = await this.getTierSettings();
    const quotaKey =
      type === "images"
        ? "imagesQuota"
        : type === "content"
        ? "contentQuota"
        : "titleQuota";
    const maxQuota = tierSettings.config[userTier.tier][quotaKey];
    if (currentUsage >= maxQuota) {
      throw new Error(`${type} quota exceeded for current tier`);
    }
    const newUsage = {
      count: currentUsage + 1,
      lastUpdated: /* @__PURE__ */ new Date().toISOString(),
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}:${type}`,
      JSON.stringify(newUsage)
    );
    return newUsage.count;
  }
  async initializeTierSettings() {
    const defaultConfig = {
      free: {
        name: "Free Tier",
        maxQuota: 1e3,
        imagesQuota: 1e3,
        contentQuota: 1e3,
        titleQuota: 1e3,
        price: 0,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 4.99,
        addon2_price: 9.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
        ],
        addon2_detail: [
          "Premium support",
          "1-on-1 training",
          "Custom integration",
        ],
        features: ["Basic API access", "Community support"],
      },
      medium: {
        name: "Medium Tier",
        maxQuota: 1e4,
        imagesQuota: 1e4,
        contentQuota: 1e4,
        titleQuota: 1e4,
        price: 9.99,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 9.99,
        addon2_price: 19.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing",
        ],
        addon2_detail: [
          "Premium support",
          "Weekly training",
          "Custom integration",
          "API consultation",
        ],
        features: ["Increased quota", "Email support"],
      },
      high: {
        name: "High Tier",
        maxQuota: 1e6,
        imagesQuota: 1e6,
        contentQuota: 1e6,
        titleQuota: 1e6,
        price: 49.99,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 19.99,
        addon2_price: 39.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing",
          "Custom models",
        ],
        addon2_detail: [
          "Premium support",
          "Daily training",
          "Custom integration",
          "Dedicated manager",
          "24/7 phone support",
        ],
        features: ["Maximum quota", "Priority support", "24/7 phone support"],
      },
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify({
        config: defaultConfig,
        updatedAt: /* @__PURE__ */ new Date().toISOString(),
        version: "1.0",
      })
    );
    return await this.getTierSettings();
  }
  async getTierSettings() {
    return await this.env.USERS_KV.get(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      "json"
    );
  }
  async updateTierSettings(tierConfig) {
    const settings = {
      config: tierConfig,
      updatedAt: /* @__PURE__ */ new Date().toISOString(),
      version: "1.0",
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify(settings)
    );
    return settings;
  }
  async getUserTier(userId) {
    try {
      const userTier = await this.env.USERS_KV.get(
        `${_TierService.KEYS.USER_TIER}:${userId}`,
        "json"
      );
      return (
        userTier || {
          tier: _TierService.TIERS.FREE,
          updatedAt: /* @__PURE__ */ new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error("Error getting user tier:", error);
      throw error;
    }
  }
  async setUserTier(userId, newTier, options = {}) {
    if (!Object.values(_TierService.TIERS).includes(newTier)) {
      throw new Error("Invalid tier specified");
    }
    const tierSettings = await this.getTierSettings();
    const newTierConfig = tierSettings.config[newTier];
    if (!newTierConfig) {
      throw new Error(`Configuration not found for tier: ${newTier}`);
    }
    const [imagesUsage, contentUsage] = await Promise.all([
      this.getTypeQuotaUsage(userId, "images"),
      this.getTypeQuotaUsage(userId, "content"),
    ]);
    const startDate = /* @__PURE__ */ new Date();
    const expirationDate = new Date(startDate);
    expirationDate.setDate(startDate.getDate() + newTierConfig.expirationDays);
    const tierData = {
      tier: newTier,
      startDate: startDate.toISOString(),
      expirationDate: expirationDate.toISOString(),
      updatedAt: /* @__PURE__ */ new Date().toISOString(),
      addon1: options.addon1 || false,
      addon2: options.addon2 || false,
      previousUsage: {
        images: imagesUsage || 0,
        content: contentUsage || 0,
      },
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.USER_TIER}:${userId}`,
      JSON.stringify(tierData)
    );
    return {
      ...tierData,
      currentStatus: await this.getUserTierStatus(userId),
    };
  }
  async getUserQuotaUsage(userId) {
    const usage = await this.env.USERS_KV.get(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      "json"
    );
    return usage?.count || 0;
  }
  async incrementQuotaUsage(userId) {
    const currentUsage = await this.getUserQuotaUsage(userId);
    const userTier = await this.getUserTier(userId);
    const tierSettings = await this.getTierSettings();
    if (currentUsage >= tierSettings.config[userTier].maxQuota) {
      throw new Error("Quota exceeded for current tier");
    }
    const newUsage = {
      count: currentUsage + 1,
      lastUpdated: /* @__PURE__ */ new Date().toISOString(),
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      JSON.stringify(newUsage)
    );
    return newUsage.count;
  }
  async resetQuotaUsage(userId) {
    const resetData = {
      count: 0,
      lastReset: /* @__PURE__ */ new Date().toISOString(),
    };
    await Promise.all([
      this.env.USERS_KV.put(
        `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
        JSON.stringify(resetData)
      ),
      this.env.USERS_KV.put(
        `${_TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
        JSON.stringify(resetData)
      ),
    ]);
    return resetData;
  }
  async getUserTierStatus(userId) {
    try {
      const [
        userTierData,
        tierSettings,
        imagesUsage,
        contentUsage,
        titleUsage,
      ] = await Promise.all([
        this.getUserTier(userId),
        this.getTierSettings(),
        this.getTypeQuotaUsage(userId, "images"),
        this.getTypeQuotaUsage(userId, "content"),
        this.getTypeQuotaUsage(userId, "title"),
      ]);
      const currentTier =
        typeof userTierData === "object" ? userTierData.tier : userTierData;
      const tierConfig = tierSettings.config[currentTier];
      const totalUsage =
        (imagesUsage || 0) + (contentUsage || 0) + (titleUsage || 0);
      const remainingImagesQuota = Math.max(
        0,
        tierConfig.imagesQuota - (imagesUsage || 0)
      );
      const remainingContentQuota = Math.max(
        0,
        tierConfig.contentQuota - (contentUsage || 0)
      );
      const remainingTitleQuota = Math.max(
        0,
        tierConfig.titleQuota - (titleUsage || 0)
      );
      const isExpired =
        userTierData.expirationDate &&
        new Date(userTierData.expirationDate) < /* @__PURE__ */ new Date();
      return {
        currentTier,
        tierName: tierConfig.name,
        usage: totalUsage,
        imagesUsage: imagesUsage || 0,
        contentUsage: contentUsage || 0,
        titleUsage: titleUsage || 0,
        maxQuota: tierConfig.maxQuota,
        imagesQuota: tierConfig.imagesQuota,
        contentQuota: tierConfig.contentQuota,
        titleQuota: tierConfig.titleQuota,
        remainingImagesQuota,
        remainingContentQuota,
        remainingTitleQuota,
        price: tierConfig.price,
        features: tierConfig.features,
        quotaPercentage: ((totalUsage / tierConfig.maxQuota) * 100).toFixed(2),
        // New fields
        expirationDays: tierConfig.expirationDays,
        startDate: userTierData.startDate,
        expirationDate: userTierData.expirationDate,
        isExpired,
        addon1: userTierData.addon1 || false,
        addon2: userTierData.addon2 || false,
        addon1_price: tierConfig.addon1_price,
        addon2_price: tierConfig.addon2_price,
        addon1_detail: tierConfig.addon1_detail,
        addon2_detail: tierConfig.addon2_detail,
      };
    } catch (error) {
      console.error("Error getting user tier status:", error);
      throw error;
    }
  }
  async setQuotaToZero(userId) {
    try {
      const usage = {
        count: Number.MAX_SAFE_INTEGER,
        // This will effectively make remaining = 0
        lastUpdated: /* @__PURE__ */ new Date().toISOString(),
      };
      await Promise.all([
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(usage)
        ),
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(usage)
        ),
      ]);
      const tierStatus = await this.getUserTierStatus(userId);
      const isQuotaZero =
        tierStatus.remainingImagesQuota === 0 &&
        tierStatus.remainingContentQuota === 0;
      return {
        success: true,
        isQuotaZero,
        tierStatus: {
          remainingImages: tierStatus.remainingImagesQuota,
          remainingContent: tierStatus.remainingContentQuota,
          totalImages: tierStatus.imagesQuota,
          totalContent: tierStatus.contentQuota,
          currentTier: tierStatus.currentTier,
        },
      };
    } catch (error) {
      console.error("Error setting quota to zero:", error);
      throw error;
    }
  }
};
var TierService2 = _TierService;
__name(TierService2, "TierService");
// KV key prefixes
__publicField(TierService2, "KEYS", {
  SETTINGS: "t_setting",
  USER_TIER: "t_setting:user_tier",
  QUOTA_USAGE: "t_setting:quota_usage",
});
// Tier definitions
__publicField(TierService2, "TIERS", {
  FREE: "free",
  MEDIUM: "medium",
  HIGH: "high",
});
// Default tier configuration
__publicField(TierService2, "DEFAULT_TIER_CONFIG", {
  [_TierService.TIERS.FREE]: {
    name: "Free Tier",
    maxQuota: 1e3,
    imagesQuota: 1e3,
    contentQuota: 1e3,
    titleQuota: 1e3,
    price: 0,
    expirationDays: 30,
    addon1: false,
    addon2: false,
    addon1_price: 4.99,
    addon2_price: 9.99,
    addon1_detail: [
      "Enhanced resolution",
      "Advanced filters",
      "Batch processing",
    ],
    addon2_detail: ["Premium support", "1-on-1 training", "Custom integration"],
    features: ["Basic API access", "Community support"],
  },
  [_TierService.TIERS.MEDIUM]: {
    name: "Medium Tier",
    maxQuota: 1e4,
    imagesQuota: 1e4,
    contentQuota: 1e4,
    titleQuota: 1e4,
    price: 9.99,
    expirationDays: 30,
    addon1: false,
    addon2: false,
    addon1_price: 9.99,
    addon2_price: 19.99,
    addon1_detail: [
      "Enhanced resolution",
      "Advanced filters",
      "Batch processing",
      "Priority processing",
    ],
    addon2_detail: [
      "Premium support",
      "Weekly training",
      "Custom integration",
      "API consultation",
    ],
    features: ["Increased quota", "Email support"],
  },
  [_TierService.TIERS.HIGH]: {
    name: "High Tier",
    maxQuota: 1e6,
    imagesQuota: 1e6,
    contentQuota: 1e6,
    titleQuota: 1e6,
    price: 49.99,
    expirationDays: 30,
    addon1: false,
    addon2: false,
    addon1_price: 19.99,
    addon2_price: 39.99,
    addon1_detail: [
      "Enhanced resolution",
      "Advanced filters",
      "Batch processing",
      "Priority processing",
      "Custom models",
    ],
    addon2_detail: [
      "Premium support",
      "Daily training",
      "Custom integration",
      "Dedicated manager",
      "24/7 phone support",
    ],
    features: ["Maximum quota", "Priority support", "24/7 phone support"],
  },
});

// src/controllers/tierController.js
var TierController = class {
  constructor(env) {
    this.env = env;
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
    this.apiKeyService = new ApiKeyService();
  }
  async _getUserFromApiKey(apiKey) {
    if (!apiKey) {
      throw new Error("API Key is required");
    }
    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return user;
  }
  async getTierSettings(request) {
    try {
      const settings = await this.tierService.getTierSettings();
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(settings)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getUserTierStatus(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const status = await this.tierService.getUserTierStatus(user.id);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(status)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async updateUserTier(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const { tier } = await request.json();
      if (!tier) {
        throw new Error("Tier is required");
      }
      const user = await this._getUserFromApiKey(apiKey);
      const updatedTierData = await this.tierService.setUserTier(user.id, tier);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            {
              tier: updatedTierData.tier,
              updatedAt: updatedTierData.updatedAt,
              currentStatus: updatedTierData.status,
            },
            "User tier updated successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const [usage, tierStatus] = await Promise.all([
        this.tierService.getUserQuotaUsage(user.id),
        this.tierService.getUserTierStatus(user.id),
      ]);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            usage,
            currentTier: tierStatus.currentTier,
            tierName: tierStatus.tierName,
            limit: tierStatus.maxQuota,
            remaining: tierStatus.remainingQuota,
            usagePercentage: tierStatus.quotaPercentage + "%",
            price: tierStatus.price,
            features: tierStatus.features,
          })
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async resetQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const resetData = await this.tierService.resetQuotaUsage(user.id);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            resetData,
            "Quota usage reset successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async setQuotaToZero(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const result = await this.tierService.setQuotaToZero(user.id);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(result)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
};
__name(TierController, "TierController");

// src/routes/tierRoutes.js
function createTierRouter(env) {
  const router2 = e({ base: "/api/tiers" });
  const tierController = new TierController(env);
  router2.get("/settings", (request) =>
    tierController.getTierSettings(request)
  );
  router2.get("/status", (request) =>
    tierController.getUserTierStatus(request)
  );
  router2.put("/upgrade", (request) => tierController.updateUserTier(request));
  router2.get("/quota", (request) => tierController.getQuotaUsage(request));
  router2.post("/quota/reset", (request) =>
    tierController.resetQuotaUsage(request)
  );
  router2.post("/quota/zero", (request) =>
    tierController.setQuotaToZero(request)
  );
  router2.post("/settings/force-update", async (request) => {
    const tierService = new TierService2(env);
    await env.USERS_KV.delete(`${TierService2.KEYS.SETTINGS}:tiers`);
    const settings = await tierService.initializeTierSettings();
    return new Response(JSON.stringify({ success: true, data: settings }), {
      headers: { "Content-Type": "application/json" },
    });
  });
  return router2;
}
__name(createTierRouter, "createTierRouter");

// src/routes/debugRoutes.js
function createDebugRouter(env) {
  const router2 = e({ base: "/api/debug" });
  const emailQueueService = new EmailQueueService(env);
  router2.post("/email-queue/cleanup", async () => {
    try {
      const results = await emailQueueService.cleanupQueue();
      const newStatus = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          message: "Queue cleanup completed",
          results,
          currentStatus: newStatus,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.get("/email-queue", async () => {
    try {
      const status = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          data: status,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.get("/cron-debug", async (request) => {
    const emailQueueService2 = new EmailQueueService(env);
    const debugInfo = await emailQueueService2.getDebugInfo();
    return new Response(
      JSON.stringify({
        success: true,
        data: debugInfo,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  });
  router2.post("/process-queue", async () => {
    try {
      const results = await emailQueueService.processQueue();
      const newStatus = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          message: "Queue processing completed",
          results,
          currentStatus: newStatus,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.delete("/email-queue", async () => {
    try {
      const clearedCount = await emailQueueService.clearQueue();
      return new Response(
        JSON.stringify({
          success: true,
          message: `Cleared ${clearedCount} items from queue`,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.get("/email-queue/status", async (request, env2) => {
    try {
      const emailQueueService2 = new EmailQueueService(env2);
      const status = await emailQueueService2.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          data: status,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting queue status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.get("/email-queue/debug", async (request, env2) => {
    try {
      const emailQueueService2 = new EmailQueueService(env2);
      const debugInfo = await emailQueueService2.getDebugInfo();
      return new Response(
        JSON.stringify({
          success: true,
          data: debugInfo,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: /* @__PURE__ */ new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  return router2;
}
__name(createDebugRouter, "createDebugRouter");

// src/services/paypalService.js
var PayPalService = class {
  constructor(env) {
    this.env = env;
    this.clientId = env.PAYPAL_CLIENT_ID;
    this.clientSecret = env.PAYPAL_CLIENT_SECRET;
    this.baseURL =
      env.PAYPAL_SANDBOX === "true"
        ? "https://api-m.sandbox.paypal.com"
        : "https://api-m.paypal.com";
    this.tierService = new TierService2(env);
  }
  async createProduct(tierName) {
    try {
      const accessToken = await this.getAccessToken();
      const productData = {
        name: `${tierName} Subscription`,
        description: `Access to ${tierName} tier features`,
        type: "SERVICE",
        category: "SOFTWARE",
      };
      const response = await fetch(`${this.baseURL}/v1/catalogs/products`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `prod_${Date.now()}`,
        },
        body: JSON.stringify(productData),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create product");
      }
      return data.id;
    } catch (error) {
      console.error("Error creating PayPal product:", error);
      throw error;
    }
  }
  async createSubscription(
    userId,
    tier,
    addons = { addon1: false, addon2: false }
  ) {
    try {
      console.log(
        "Creating subscription for tier:",
        tier,
        "with addons:",
        addons
      );
      const tierSettings = await this.tierService.getTierSettings();
      console.log("Tier settings:", tierSettings);
      if (!tierSettings?.config?.[tier]) {
        throw new Error(`Invalid tier: ${tier}`);
      }
      const tierConfig = tierSettings.config[tier];
      let basePrice = tierConfig.price;
      let totalPrice = basePrice;
      if (addons.addon1) {
        totalPrice += tierConfig.addon1_price;
      }
      if (addons.addon2) {
        totalPrice += tierConfig.addon2_price;
      }
      const productName = `${tierConfig.name}${
        addons.addon1 ? " + Addon 1" : ""
      }${addons.addon2 ? " + Addon 2" : ""}`;
      const productDescription = this._generateProductDescription(
        tierConfig,
        addons
      );
      const productId = await this.createProduct(
        productName,
        productDescription
      );
      const planData = {
        name: `${productName} Monthly Subscription`,
        product_id: productId,
        billing_cycles: [
          {
            frequency: {
              interval_unit: "MONTH",
              interval_count: 1,
            },
            tenure_type: "REGULAR",
            sequence: 1,
            total_cycles: 0,
            pricing_scheme: {
              fixed_price: {
                value: totalPrice.toFixed(2),
                currency_code: "USD",
              },
            },
          },
        ],
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee_failure_action: "CONTINUE",
          payment_failure_threshold: 3,
        },
      };
      const plan = await this._createPlan(planData);
      const subscription = await this._createSubscriptionWithPlan(
        plan.id,
        userId
      );
      const subscriptionRecord = {
        subscriptionId: subscription.id,
        planId: plan.id,
        productId,
        tier,
        basePrice,
        addons: {
          addon1: addons.addon1
            ? {
                enabled: true,
                price: tierConfig.addon1_price,
                features: tierConfig.addon1_detail,
              }
            : false,
          addon2: addons.addon2
            ? {
                enabled: true,
                price: tierConfig.addon2_price,
                features: tierConfig.addon2_detail,
              }
            : false,
        },
        totalPrice,
        status: subscription.status,
        createdAt: /* @__PURE__ */ new Date().toISOString(),
      };
      await this.env.USERS_KV.put(
        `subscription:${userId}`,
        JSON.stringify(subscriptionRecord)
      );
      return {
        ...subscriptionRecord,
        approvalUrl: subscription.links.find((link) => link.rel === "approve")
          ?.href,
      };
    } catch (error) {
      console.error("Error creating PayPal subscription:", error);
      throw error;
    }
  }
  async updateSubscriptionAddons(subscriptionId, addons) {
    try {
      const accessToken = await this.getAccessToken();
      const subscription = await this.getSubscriptionDetails(subscriptionId);
      const subscriptionData = await this.getSubscriptionData(subscriptionId);
      const tierConfig = await this.tierService.getTierSettings();
      const tierSettings = tierConfig.config[subscriptionData.tier];
      let newPrice = tierSettings.price;
      if (addons.addon1) newPrice += tierSettings.addon1_price;
      if (addons.addon2) newPrice += tierSettings.addon2_price;
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}/revise`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify({
            plan_id: subscription.plan_id,
            shipping_amount: {
              currency_code: "USD",
              value: newPrice.toFixed(2),
            },
          }),
        }
      );
      if (!response.ok) {
        throw new Error("Failed to update subscription");
      }
      await this.updateSubscriptionRecord(subscriptionId, {
        addons,
        totalPrice: newPrice,
      });
      return {
        success: true,
        subscriptionId,
        newPrice,
        addons,
      };
    } catch (error) {
      console.error("Error updating subscription addons:", error);
      throw error;
    }
  }
  async getAccessToken() {
    try {
      const response = await fetch(`${this.baseURL}/v1/oauth2/token`, {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: `Basic ${btoa(
            `${this.clientId}:${this.clientSecret}`
          )}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "grant_type=client_credentials",
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error_description || "Failed to get access token");
      }
      return data.access_token;
    } catch (error) {
      console.error("PayPal access token error:", error);
      throw error;
    }
  }
  async getSubscriptionStatus(subscriptionId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to get subscription status");
      }
      const subscription = await response.json();
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:",
      });
      let localSubscription = null;
      let userId = null;
      for (const key of keys) {
        const record = await this.env.USERS_KV.get(key.name, "json");
        if (record && record.subscriptionId === subscriptionId) {
          localSubscription = record;
          userId = key.name.split(":")[1];
          break;
        }
      }
      return {
        subscriptionId: subscription.id,
        status: subscription.status,
        planId: subscription.plan_id,
        startTime: subscription.start_time,
        nextBillingTime: subscription.billing_info?.next_billing_time,
        lastPaymentTime: subscription.billing_info?.last_payment?.time,
        failedPayments: subscription.billing_info?.failed_payments_count || 0,
        tier: localSubscription?.tier,
        price: localSubscription?.price,
        localStatus: localSubscription?.status,
        createdAt: localSubscription?.createdAt,
        activatedAt: localSubscription?.activatedAt,
        lastUpdated: /* @__PURE__ */ new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error getting subscription status:", error);
      throw error;
    }
  }
  async activateSubscription(subscriptionId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );
      const subscription = await response.json();
      if (!response.ok) {
        throw new Error(
          subscription.message || "Failed to get subscription details"
        );
      }
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:",
      });
      let subscriptionRecord = null;
      let userId = null;
      for (const key of keys) {
        const record = await this.env.USERS_KV.get(key.name, "json");
        if (record && record.subscriptionId === subscriptionId) {
          subscriptionRecord = record;
          userId = key.name.split(":")[1];
          break;
        }
      }
      if (!subscriptionRecord || !userId) {
        throw new Error("Subscription record not found");
      }
      const updatedRecord = {
        ...subscriptionRecord,
        status: subscription.status,
        activatedAt: /* @__PURE__ */ new Date().toISOString(),
        paypalStatus: subscription.status,
        lastUpdated: /* @__PURE__ */ new Date().toISOString(),
      };
      await this.env.USERS_KV.put(
        `subscription:${userId}`,
        JSON.stringify(updatedRecord)
      );
      return {
        success: true,
        subscription: updatedRecord,
      };
    } catch (error) {
      console.error("Error activating subscription:", error);
      throw error;
    }
  }
  _generateProductDescription(tierConfig, addons) {
    let description = `${tierConfig.name} features:
`;
    description += tierConfig.features.join(", ") + "\n";
    if (addons.addon1) {
      description += "\nAddon 1 features:\n";
      description += tierConfig.addon1_detail.join(", ");
    }
    if (addons.addon2) {
      description += "\nAddon 2 features:\n";
      description += tierConfig.addon2_detail.join(", ");
    }
    return description;
  }
};
__name(PayPalService, "PayPalService");

// src/controllers/subscriptionController.js
var SubscriptionController = class {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
  }
  async createSubscription(request) {
    try {
      console.log("Starting subscription creation...");
      const headers = {};
      request.headers.forEach((value, key) => {
        headers[key] = value;
      });
      console.log("Request headers:", headers);
      const apiKey = request.headers.get("x-sps-key");
      console.log("API Key present:", !!apiKey);
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API key is required in x-sps-key header",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      console.log("User found:", !!user);
      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Invalid API key",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      const tierSettings = await this.tierService.getTierSettings();
      console.log("Tier settings:", tierSettings);
      const body = await request.json();
      console.log("Request body:", body);
      const { tier, addons = { addon1: false, addon2: false } } = body;
      if (addons.addon1 !== void 0 && typeof addons.addon1 !== "boolean") {
        throw new Error("addon1 must be a boolean value");
      }
      if (addons.addon2 !== void 0 && typeof addons.addon2 !== "boolean") {
        throw new Error("addon2 must be a boolean value");
      }
      if (!tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Tier is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      if (!tierSettings?.config?.[tier]) {
        return new Response(
          JSON.stringify({
            success: false,
            message: `Invalid tier: ${tier}. Available tiers: ${Object.keys(
              tierSettings?.config || {}
            ).join(", ")}`,
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      console.log("PayPal config:", {
        sandboxMode: this.env.PAYPAL_SANDBOX === "true",
        hasClientId: !!this.env.PAYPAL_CLIENT_ID,
        hasClientSecret: !!this.env.PAYPAL_CLIENT_SECRET,
        appUrl: this.env.APP_URL,
      });
      console.log("Creating PayPal subscription for tier:", tier);
      const subscription = await this.paypalService.createSubscription(
        user.id,
        tier,
        addons
      );
      console.log("Subscription created:", subscription);
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            subscriptionId: subscription.subscriptionId,
            approvalUrl: subscription.approvalUrl,
            tier: subscription.tier,
            basePrice: subscription.basePrice,
            addons: subscription.addons,
            totalPrice: subscription.totalPrice,
            status: subscription.status,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Subscription creation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to create subscription",
          details: error.stack,
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  // Add new endpoint to update addons
  async updateSubscriptionAddons(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const { subscriptionId, addons } = await request.json();
      if (!apiKey) {
        throw new Error("API key is required");
      }
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      if (!user) {
        throw new Error("Invalid API key");
      }
      const result = await this.paypalService.updateSubscriptionAddons(
        subscriptionId,
        addons
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: result,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {}
  }
  async getTestEndpoint() {
    try {
      return new Response(
        JSON.stringify({
          success: true,
          message: "Subscription router is working",
          env: {
            hasPayPalConfig: !!(
              this.env.PAYPAL_CLIENT_ID && this.env.PAYPAL_CLIENT_SECRET
            ),
            sandbox: this.env.PAYPAL_SANDBOX === "true",
            appUrl: this.env.APP_URL,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async handleSubscriptionSuccess(request) {
    try {
      const url = new URL(request.url);
      const subscriptionId = url.searchParams.get("subscription_id");
      if (!subscriptionId) {
        throw new Error("Subscription ID is required");
      }
      console.log("Processing successful subscription:", subscriptionId);
      const result = await this.paypalService.activateSubscription(
        subscriptionId
      );
      const redirectUrl = new URL("/subscription/success", this.env.APP_URL);
      redirectUrl.searchParams.set("status", "success");
      redirectUrl.searchParams.set("subscription_id", subscriptionId);
      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling subscription success:", error);
      const redirectUrl = new URL("/subscription/error", this.env.APP_URL);
      redirectUrl.searchParams.set("error", error.message);
      return Response.redirect(redirectUrl.toString(), 302);
    }
  }
};
__name(SubscriptionController, "SubscriptionController");

// src/routes/subscriptionRoutes.js
function createSubscriptionRouter(env) {
  const router2 = e({ base: "/api/subscriptions" });
  const controller = new SubscriptionController(env);
  router2.get("/test", async () => {
    try {
      return new Response(
        JSON.stringify({
          success: true,
          message: "Subscription router is working",
          environment: {
            hasPayPalConfig: !!(
              env.PAYPAL_CLIENT_ID && env.PAYPAL_CLIENT_SECRET
            ),
            sandbox: env.PAYPAL_SANDBOX === "true",
            appUrl: env.APP_URL,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.post("/", (request) => controller.createSubscription(request));
  router2.get("/success", (request) =>
    controller.handleSubscriptionSuccess(request)
  );
  router2.get("/status/:subscriptionId", async (request) => {
    try {
      const subscriptionId = request.params.subscriptionId;
      const subscription = await controller.paypalService.getSubscriptionStatus(
        subscriptionId
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: subscription,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });
  router2.put("/addons", (request) =>
    controller.updateSubscriptionAddons(request)
  );
  router2.all(
    "*",
    () =>
      new Response(
        JSON.stringify({
          success: false,
          error: "Not Found",
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      )
  );
  return router2;
}
__name(createSubscriptionRouter, "createSubscriptionRouter");

// src/swagger/swagger.js
var swaggerDocument = {
  openapi: "3.0.0",
  info: {
    title: "API Documentation",
    version: "1.0.0",
  },
  paths: {
    "/api/users/": {
      post: {
        summary: "Create a new user",
        description:
          "Create a new user with auto-generated username and password. Credentials will be sent via email.",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "domain"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "User email where credentials will be sent",
                    example: "<EMAIL>",
                  },
                  domain: {
                    type: "string",
                    description: "User domain (must be unique)",
                    example: "example.com",
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "User created successfully with generated credentials",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                        domain: {
                          type: "string",
                          example: "example.com",
                        },
                        api_key: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        credentials: {
                          type: "object",
                          properties: {
                            password: {
                              type: "string",
                              description: "Auto-generated password",
                              example: "aB3$xK9#mP2&",
                            },
                            apiKey: {
                              type: "string",
                              description: "API key for authentication",
                              example: "550e8400-e29b-41d4-a716-446655440000",
                            },
                          },
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                        },
                      },
                    },
                    message: {
                      type: "string",
                      example:
                        "User created successfully. Credentials have been sent to your email.",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Domain already registered or validation error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example:
                        "Domain is already registered. Each domain can only have one API key.",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/validatekey": {
      post: {
        summary: "Validate API Key",
        description: "Validates whether a provided API key is valid and active",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        responses: {
          200: {
            description: "API key validation response",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      description: "Whether the operation was successful",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        isValid: {
                          type: "boolean",
                          description: "Whether the API key is valid",
                          example: true,
                        },
                        message: {
                          type: "string",
                          description:
                            "Additional information about the validation result",
                          example: null,
                        },
                      },
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      description: "Timestamp of the response",
                      example: "2024-12-07T12:00:00.000Z",
                    },
                  },
                },
                examples: {
                  valid: {
                    value: {
                      success: true,
                      data: {
                        isValid: true,
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Valid API Key",
                  },
                  invalid: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "Invalid API Key",
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Invalid API Key",
                  },
                  missing: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "API Key is required in x-sps-key header",
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Missing API Key",
                  },
                },
              },
            },
          },
          500: {
            description: "Server error occurred",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Server error occurred",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/userdetail": {
      get: {
        summary: "Get user details",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        responses: {
          200: {
            description: "User details retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UserResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/quota/zero": {
      post: {
        summary: "Set quota to zero",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Sets both image and content quotas to their maximum values, effectively making remaining quota zero",
        responses: {
          200: {
            description: "Quota set to zero successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        isQuotaZero: {
                          type: "boolean",
                          description: "Confirms if quota is set to zero",
                          example: true,
                        },
                        tierStatus: {
                          type: "object",
                          properties: {
                            remainingImages: {
                              type: "number",
                              description: "Remaining image quota",
                              example: 0,
                            },
                            remainingContent: {
                              type: "number",
                              description: "Remaining content quota",
                              example: 0,
                            },
                            totalImages: {
                              type: "number",
                              description: "Total image quota for tier",
                              example: 1e3,
                            },
                            totalContent: {
                              type: "number",
                              description: "Total content quota for tier",
                              example: 1e3,
                            },
                            currentTier: {
                              type: "string",
                              description: "Current tier name",
                              example: "free",
                            },
                          },
                        },
                      },
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-12-08T12:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Error setting quota to zero",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Error setting quota to zero",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-12-08T12:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/settings": {
      get: {
        summary: "Get tier settings",
        tags: ["Tiers"],
        description:
          "Retrieve all tier configurations including quotas and features",
        responses: {
          200: {
            description: "Tier settings retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        config: {
                          type: "object",
                          properties: {
                            free: { $ref: "#/components/schemas/TierConfig" },
                            medium: { $ref: "#/components/schemas/TierConfig" },
                            high: { $ref: "#/components/schemas/TierConfig" },
                          },
                        },
                        updatedAt: { type: "string", format: "date-time" },
                        version: { type: "string" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/status": {
      get: {
        summary: "Get user tier status",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Get current user tier status including usage and quota information",
        responses: {
          200: {
            description: "User tier status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/TierStatusResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/upgrade": {
      put: {
        summary: "Upgrade user tier",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Upgrade user to a different tier with optional add-ons",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["free", "medium", "high"],
                    description: "The tier to upgrade to",
                  },
                  addon1: {
                    type: "boolean",
                    description: "Enable addon1 features",
                    default: false,
                  },
                  addon2: {
                    type: "boolean",
                    description: "Enable addon2 features",
                    default: false,
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Tier upgraded successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        tier: { type: "string" },
                        startDate: {
                          type: "string",
                          format: "date-time",
                        },
                        expirationDate: {
                          type: "string",
                          format: "date-time",
                        },
                        addon1: { type: "boolean" },
                        addon2: { type: "boolean" },
                        currentStatus: {
                          type: "object",
                          properties: {
                            tierName: { type: "string" },
                            maxQuota: { type: "number" },
                            imagesQuota: { type: "number" },
                            contentQuota: { type: "number" },
                            price: { type: "number" },
                            features: {
                              type: "array",
                              items: { type: "string" },
                            },
                            addon1_price: { type: "number" },
                            addon2_price: { type: "number" },
                            addon1_detail: {
                              type: "array",
                              items: { type: "string" },
                            },
                            addon2_detail: {
                              type: "array",
                              items: { type: "string" },
                            },
                          },
                        },
                      },
                    },
                    message: { type: "string" },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "Invalid tier specified",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/quota": {
      get: {
        summary: "Get quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Get current quota usage information",
        responses: {
          200: {
            description: "Quota usage retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        usage: { type: "number" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/quota/reset": {
      post: {
        summary: "Reset quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Reset the quota usage counter to zero",
        responses: {
          200: {
            description: "Quota reset successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        count: { type: "number" },
                        lastReset: { type: "string", format: "date-time" },
                      },
                    },
                    message: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/activate": {
      post: {
        summary: "Activate user API key",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["license"],
                properties: {
                  license: {
                    type: "string",
                    description: "License key to activate",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "API key activated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "boolean",
                    },
                    message: {
                      type: "string",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/license/status": {
      post: {
        summary: "Check license activation status",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["license"],
                properties: {
                  license: {
                    type: "string",
                    description: "License key to check",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "License status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                    },
                    data: {
                      type: "object",
                      properties: {
                        isValid: {
                          type: "boolean",
                        },
                        status: {
                          type: "string",
                          enum: ["active", "pending"],
                        },
                        activatedAt: {
                          type: "string",
                          format: "date-time",
                          nullable: true,
                        },
                        domain: {
                          type: "string",
                        },
                        message: {
                          type: "string",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/images": {
      post: {
        summary: "Track image generation usage",
        tags: ["Usage"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Increment the image generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the image generation",
                    example: "profile-picture",
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the image was generated",
                    example: "2024-12-08T02:30:00.000Z",
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      size: "1024x1024",
                      model: "stable-diffusion",
                    },
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "profile-picture",
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "product-image",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      size: "1024x1024",
                      model: "stable-diffusion",
                      style: "photorealistic",
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: { type: "object" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/content": {
      post: {
        summary: "Track content generation usage",
        tags: ["Usage"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Increment the content generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the content generation",
                    example: "product-description",
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the content was generated",
                    example: "2024-12-08T02:30:00.000Z",
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      wordCount: 500,
                      language: "en",
                    },
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "product-description",
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "blog-post",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      wordCount: 1500,
                      language: "en",
                      category: "technology",
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: { type: "object" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/stats": {
      get: {
        summary: "Get usage statistics",
        tags: ["Usage"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Get detailed usage statistics for images, content, and title generation",
        responses: {
          200: {
            description: "Usage statistics retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        images: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 50 },
                            remaining: { type: "number", example: 950 },
                            total: { type: "number", example: 1e3 },
                            percentageUsed: { type: "string", example: "5.00" },
                          },
                        },
                        content: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 30 },
                            remaining: { type: "number", example: 970 },
                            total: { type: "number", example: 1e3 },
                            percentageUsed: { type: "string", example: "3.00" },
                          },
                        },
                        title: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 20 },
                            remaining: { type: "number", example: 980 },
                            total: { type: "number", example: 1e3 },
                            percentageUsed: { type: "string", example: "2.00" },
                          },
                        },
                        tier: {
                          type: "object",
                          properties: {
                            name: { type: "string", example: "Medium Tier" },
                            current: { type: "string", example: "medium" },
                            expirationDate: {
                              type: "string",
                              example: "2024-12-31T23:59:59Z",
                            },
                            isExpired: { type: "boolean", example: false },
                            price: { type: "number", example: 9.99 },
                          },
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean", example: false },
                                price: { type: "number", example: 4.99 },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                  example: [
                                    "Enhanced resolution",
                                    "Advanced filters",
                                  ],
                                },
                              },
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean", example: false },
                                price: { type: "number", example: 9.99 },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                  example: [
                                    "Premium support",
                                    "1-on-1 training",
                                  ],
                                },
                              },
                            },
                          },
                        },
                        features: {
                          type: "array",
                          items: { type: "string" },
                          example: ["Basic API access", "Community support"],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/history": {
      get: {
        summary: "Get usage history",
        tags: ["Usage"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Retrieve detailed usage history with filtering and pagination options",
        parameters: [
          {
            name: "startDate",
            in: "query",
            description: "Filter results from this date (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-01",
            },
          },
          {
            name: "endDate",
            in: "query",
            description: "Filter results until this date (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-31",
            },
          },
          {
            name: "type",
            in: "query",
            description: "Filter by usage type",
            schema: {
              type: "string",
              enum: ["images", "content"],
            },
          },
          {
            name: "limit",
            in: "query",
            description: "Number of results per page",
            schema: {
              type: "integer",
              minimum: 1,
              maximum: 100,
              default: 100,
            },
          },
          {
            name: "page",
            in: "query",
            description: "Page number",
            schema: {
              type: "integer",
              minimum: 1,
              default: 1,
            },
          },
        ],
        responses: {
          200: {
            description: "Usage history retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        entries: {
                          type: "array",
                          items: {
                            $ref: "#/components/schemas/UsageHistoryEntry",
                          },
                        },
                        pagination: {
                          type: "object",
                          properties: {
                            total: { type: "integer", example: 50 },
                            totalPages: { type: "integer", example: 5 },
                            currentPage: { type: "integer", example: 1 },
                            limit: { type: "integer", example: 10 },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/daily": {
      get: {
        summary: "Get daily usage aggregates",
        tags: ["Usage"],
        security: [{ ApiKeyAuth: [] }],
        description: "Retrieve daily aggregated usage statistics",
        parameters: [
          {
            name: "startDate",
            in: "query",
            description: "Start date for daily stats (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-01",
            },
          },
          {
            name: "endDate",
            in: "query",
            description: "End date for daily stats (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-31",
            },
          },
        ],
        responses: {
          200: {
            description: "Daily usage statistics retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "array",
                      items: {
                        $ref: "#/components/schemas/DailyUsage",
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions": {
      post: {
        summary: "Create new PayPal subscription",
        description:
          "Creates a new PayPal subscription for the specified tier with automatic price fetching from tier settings",
        tags: ["Subscriptions"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["medium", "high"],
                    description:
                      "The tier level to subscribe to. Price will be automatically fetched from tier settings.",
                  },
                },
              },
              examples: {
                medium: {
                  summary: "Medium tier subscription",
                  value: {
                    tier: "medium",
                  },
                },
                high: {
                  summary: "High tier subscription",
                  value: {
                    tier: "high",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          description: "PayPal subscription identifier",
                          example: "I-BW452GLLEP1G",
                        },
                        approvalUrl: {
                          type: "string",
                          description: "PayPal URL for subscription approval",
                          example:
                            "https://www.sandbox.paypal.com/webapps/billing/subscriptions?token=...",
                        },
                        tier: {
                          type: "string",
                          description: "Selected subscription tier",
                          example: "medium",
                        },
                        price: {
                          type: "number",
                          description: "Monthly subscription price in USD",
                          example: 9.99,
                        },
                        status: {
                          type: "string",
                          description: "Initial subscription status",
                          enum: ["APPROVAL_PENDING", "APPROVED", "ACTIVE"],
                          example: "APPROVAL_PENDING",
                        },
                      },
                    },
                  },
                },
                examples: {
                  mediumTier: {
                    summary: "Medium tier response",
                    value: {
                      success: true,
                      data: {
                        subscriptionId: "I-BW452GLLEP1G",
                        approvalUrl:
                          "https://www.sandbox.paypal.com/webapps/billing/subscriptions?token=EC-5RT15012PY123456",
                        tier: "medium",
                        price: 9.99,
                        status: "APPROVAL_PENDING",
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Error creating subscription",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                    },
                  },
                },
                examples: {
                  invalidTier: {
                    summary: "Invalid tier specified",
                    value: {
                      success: false,
                      message: "Invalid tier specified",
                    },
                  },
                  missingApiKey: {
                    summary: "Missing API key",
                    value: {
                      success: false,
                      message: "API key is required in x-sps-key header",
                    },
                  },
                  paypalError: {
                    summary: "PayPal API error",
                    value: {
                      success: false,
                      message: "Failed to create PayPal subscription",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/success": {
      get: {
        summary: "PayPal subscription success callback",
        description:
          "Endpoint called by PayPal after successful subscription approval",
        tags: ["Subscriptions"],
        parameters: [
          {
            name: "subscription_id",
            in: "query",
            required: true,
            schema: {
              type: "string",
            },
            description: "PayPal subscription identifier",
          },
        ],
        responses: {
          302: {
            description: "Redirects to frontend success page",
            headers: {
              Location: {
                schema: {
                  type: "string",
                  example:
                    "https://your-app.com/subscription/success?subscription_id=I-BW452GLLEP1G",
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/webhook": {
      post: {
        summary: "PayPal webhook endpoint",
        description: "Handles PayPal subscription event notifications",
        tags: ["Subscriptions"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  event_type: {
                    type: "string",
                    description: "PayPal event type",
                    example: "BILLING.SUBSCRIPTION.ACTIVATED",
                  },
                  resource: {
                    type: "object",
                    description: "Event resource details",
                  },
                },
              },
              examples: {
                activated: {
                  summary: "Subscription activated",
                  value: {
                    event_type: "BILLING.SUBSCRIPTION.ACTIVATED",
                    resource: {
                      id: "I-BW452GLLEP1G",
                      status: "ACTIVE",
                    },
                  },
                },
                payment: {
                  summary: "Payment completed",
                  value: {
                    event_type: "PAYMENT.SALE.COMPLETED",
                    resource: {
                      id: "5RT15012PY123456",
                      amount: {
                        total: "9.99",
                        currency: "USD",
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Webhook processed successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    received: {
                      type: "boolean",
                      example: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/status/{subscriptionId}": {
      get: {
        summary: "Get subscription status",
        description:
          "Retrieves the current status and details of a PayPal subscription",
        tags: ["Subscriptions"],
        parameters: [
          {
            name: "subscriptionId",
            in: "path",
            required: true,
            schema: {
              type: "string",
            },
            description: "PayPal subscription identifier",
            example: "I-FBN91UE2890B",
          },
        ],
        responses: {
          200: {
            description: "Subscription status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-FBN91UE2890B",
                        },
                        status: {
                          type: "string",
                          example: "ACTIVE",
                        },
                        planId: {
                          type: "string",
                          example: "P-6L993232BE527384WM5Q53VA",
                        },
                        startTime: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:32:32Z",
                        },
                        nextBillingTime: {
                          type: "string",
                          format: "date-time",
                          example: "2025-01-17T08:00:00Z",
                        },
                        lastPaymentTime: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:33:23Z",
                        },
                        failedPayments: {
                          type: "integer",
                          example: 0,
                        },
                        tier: {
                          type: "string",
                          example: "medium",
                        },
                        price: {
                          type: "number",
                          example: 9.99,
                        },
                        localStatus: {
                          type: "string",
                          example: "ACTIVE",
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:32:35.281Z",
                        },
                        activatedAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T08:24.356Z",
                        },
                        lastUpdated: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T08:27.870Z",
                        },
                      },
                    },
                  },
                },
                example: {
                  success: true,
                  data: {
                    subscriptionId: "I-FBN91UE2890B",
                    status: "ACTIVE",
                    planId: "P-6L993232BE527384WM5Q53VA",
                    startTime: "2024-12-17T07:32:32Z",
                    nextBillingTime: "2025-01-17T08:00:00Z",
                    lastPaymentTime: "2024-12-17T07:33:23Z",
                    failedPayments: 0,
                    tier: "medium",
                    price: 9.99,
                    localStatus: "ACTIVE",
                    createdAt: "2024-12-17T07:32:35.281Z",
                    activatedAt: "2024-12-17T08:24.356Z",
                    lastUpdated: "2024-12-17T08:27.870Z",
                  },
                },
              },
            },
          },
          400: {
            description: "Error retrieving subscription status",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to get subscription status",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/create": {
      post: {
        summary: "Create subscription with optional add-ons",
        tags: ["Subscriptions"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["free", "medium", "high"],
                    description: "Subscription tier level",
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable addon 1 features",
                        default: false,
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable addon 2 features",
                        default: false,
                      },
                    },
                  },
                },
              },
              examples: {
                "Basic Subscription": {
                  value: {
                    tier: "medium",
                    addons: {
                      addon1: false,
                      addon2: false,
                    },
                  },
                },
                "Subscription with Add-ons": {
                  value: {
                    tier: "medium",
                    addons: {
                      addon1: true,
                      addon2: true,
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-BW452GLLEP1G",
                        },
                        approvalUrl: {
                          type: "string",
                          example:
                            "https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=EC-5Y366741JN879735H",
                        },
                        tier: {
                          type: "string",
                          example: "medium",
                        },
                        basePrice: {
                          type: "number",
                          example: 9.99,
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: {
                                  type: "boolean",
                                  example: true,
                                },
                                price: {
                                  type: "number",
                                  example: 9.99,
                                },
                                features: {
                                  type: "array",
                                  items: {
                                    type: "string",
                                  },
                                  example: [
                                    "Enhanced resolution",
                                    "Advanced filters",
                                    "Batch processing",
                                    "Priority processing",
                                  ],
                                },
                              },
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: {
                                  type: "boolean",
                                  example: false,
                                },
                                price: {
                                  type: "number",
                                  example: 19.99,
                                },
                                features: {
                                  type: "array",
                                  items: {
                                    type: "string",
                                  },
                                },
                              },
                            },
                          },
                        },
                        totalPrice: {
                          type: "number",
                          example: 19.98,
                        },
                        status: {
                          type: "string",
                          example: "APPROVAL_PENDING",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Invalid tier specified",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/addons": {
      put: {
        summary: "Update subscription add-ons",
        tags: ["Subscriptions"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["subscriptionId", "addons"],
                properties: {
                  subscriptionId: {
                    type: "string",
                    description: "PayPal subscription ID",
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable/disable addon 1",
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable/disable addon 2",
                      },
                    },
                  },
                },
              },
              example: {
                subscriptionId: "I-BW452GLLEP1G",
                addons: {
                  addon1: true,
                  addon2: false,
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Add-ons updated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-BW452GLLEP1G",
                        },
                        newPrice: {
                          type: "number",
                          example: 19.98,
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "boolean",
                              example: true,
                            },
                            addon2: {
                              type: "boolean",
                              example: false,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Invalid subscription ID",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/title": {
      post: {
        summary: "Track title generation usage",
        tags: ["Usage"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Increment the title generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the title generation",
                    example: "product-title",
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the title was generated",
                    example: "2024-12-08T02:30:00.000Z",
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      language: "en",
                      category: "product",
                      length: "short",
                    },
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "product-title",
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "blog-title",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      language: "en",
                      category: "blog",
                      length: "medium",
                      keywords: ["tech", "ai"],
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: {
                      type: "object",
                      properties: {
                        tracked: { type: "boolean" },
                        currentUsage: { type: "number" },
                        remainingQuota: { type: "number" },
                        timestamp: {
                          type: "string",
                          format: "date-time",
                        },
                      },
                    },
                  },
                },
                example: {
                  success: true,
                  message: "Title generation usage tracked successfully",
                  data: {
                    tracked: true,
                    currentUsage: 51,
                    remainingQuota: 949,
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
              },
            },
          },
          400: {
            description: "Error tracking usage",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: { type: "string" },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
                example: {
                  success: false,
                  message: "Invalid request format or quota exceeded",
                  timestamp: "2024-12-08T02:30:00.000Z",
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    schemas: {
      UserResponse: {
        type: "object",
        properties: {
          id: { type: "string" },
          username: { type: "string" },
          email: { type: "string" },
          api_key: { type: "string" },
        },
      },
      TierConfig: {
        type: "object",
        properties: {
          name: { type: "string" },
          maxQuota: { type: "number" },
          price: { type: "number" },
          features: {
            type: "array",
            items: { type: "string" },
          },
        },
      },
      TierStatusResponse: {
        type: "object",
        properties: {
          success: { type: "boolean" },
          data: {
            type: "object",
            properties: {
              currentTier: { type: "string" },
              tierName: { type: "string" },
              usage: { type: "number" },
              imagesUsage: { type: "number" },
              contentUsage: { type: "number" },
              titleUsage: { type: "number" },
              maxQuota: { type: "number" },
              imagesQuota: { type: "number" },
              contentQuota: { type: "number" },
              titleQuota: { type: "number" },
              remainingImagesQuota: { type: "number" },
              remainingContentQuota: { type: "number" },
              remainingTitleQuota: { type: "number" },
              price: { type: "number" },
              features: {
                type: "array",
                items: { type: "string" },
              },
              quotaPercentage: { type: "string" },
            },
          },
        },
      },
      UsageHistoryEntry: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          type: { type: "string", enum: ["images", "content"] },
          source: { type: "string" },
          timestamp: { type: "string", format: "date-time" },
          metadata: {
            type: "object",
            additionalProperties: true,
            example: {
              size: "1024x1024",
              model: "stable-diffusion",
            },
          },
        },
      },
      DailyUsage: {
        type: "object",
        properties: {
          date: { type: "string", format: "date" },
          images: { type: "integer" },
          content: { type: "integer" },
          title: { type: "integer" },
          details: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "string", format: "uuid" },
                type: { type: "string", enum: ["images", "content", "title"] },
                source: { type: "string" },
                time: { type: "string", format: "date-time" },
              },
            },
          },
        },
        example: {
          date: "2024-12-08",
          images: 15,
          content: 8,
          title: 12,
          details: [
            {
              id: "123e4567-e89b-12d3-a456-************",
              type: "title",
              source: "product-title",
              time: "2024-12-08T02:30:00.000Z",
            },
          ],
        },
      },
      PayPalSubscription: {
        type: "object",
        properties: {
          subscriptionId: {
            type: "string",
            description: "PayPal subscription identifier",
          },
          status: {
            type: "string",
            enum: [
              "APPROVAL_PENDING",
              "APPROVED",
              "ACTIVE",
              "SUSPENDED",
              "CANCELLED",
              "EXPIRED",
            ],
          },
          tier: {
            type: "string",
            enum: ["medium", "high"],
          },
          price: {
            type: "number",
            description: "Monthly subscription price in USD",
          },
          createdAt: {
            type: "string",
            format: "date-time",
          },
          nextBillingDate: {
            type: "string",
            format: "date-time",
          },
        },
      },
      Addons: {
        type: "object",
        properties: {
          addon1: {
            type: "boolean",
            description: "Enhanced features add-on",
          },
          addon2: {
            type: "boolean",
            description: "Premium support add-on",
          },
        },
      },
    },
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "x-sps-key",
      },
    },
  },
};
function getSwaggerHTML(swaggerSpec) {
  return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>API Documentation</title>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui.min.css" />
      </head>
      <body>
          <div id="swagger-ui"></div>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui-bundle.min.js"><\/script>
          <script>
              window.onload = () => {
                  window.ui = SwaggerUIBundle({
                      spec: ${JSON.stringify(swaggerSpec)},
                      dom_id: '#swagger-ui',
                      deepLinking: true,
                      presets: [
                          SwaggerUIBundle.presets.apis,
                          SwaggerUIBundle.SwaggerUIStandalonePreset
                      ],
                  });
              };
          <\/script>
      </body>
      </html>
    `;
}
__name(getSwaggerHTML, "getSwaggerHTML");

// src/services/usageHistoryService.js
var _UsageHistoryService = class {
  constructor(env) {
    this.env = env;
  }
  async trackUsage(userId, data) {
    try {
      const historyId = crypto.randomUUID();
      const timestamp = /* @__PURE__ */ new Date().toISOString();
      const day = timestamp.split("T")[0];
      const historyEntry = {
        id: historyId,
        userId,
        type: data.type,
        // 'images' or 'content'
        source: data.source,
        timestamp,
        metadata: data.metadata || {},
      };
      await this.env.USERS_KV.put(
        `${_UsageHistoryService.KEYS.HISTORY}:${userId}:${historyId}`,
        JSON.stringify(historyEntry)
      );
      const dailyKey = `${_UsageHistoryService.KEYS.DAILY}:${userId}:${day}`;
      const existingDaily = (await this.env.USERS_KV.get(dailyKey, "json")) || {
        date: day,
        images: 0,
        content: 0,
        details: [],
      };
      existingDaily[data.type] += 1;
      existingDaily.details.push({
        id: historyId,
        type: data.type,
        source: data.source,
        time: timestamp,
      });
      await this.env.USERS_KV.put(dailyKey, JSON.stringify(existingDaily));
      return historyEntry;
    } catch (error) {
      console.error("Error tracking usage history:", error);
      throw error;
    }
  }
  async getUserHistory(userId, options = {}) {
    try {
      const {
        startDate,
        endDate = /* @__PURE__ */ new Date().toISOString(),
        type,
        limit = 100,
        page = 1,
      } = options;
      const { keys } = await this.env.USERS_KV.list({
        prefix: `${_UsageHistoryService.KEYS.HISTORY}:${userId}:`,
      });
      const entries = await Promise.all(
        keys.map((key) => this.env.USERS_KV.get(key.name, "json"))
      );
      let filteredEntries = entries
        .filter((entry) => {
          if (!entry) return false;
          const matchesType = !type || entry.type === type;
          const matchesDateRange =
            (!startDate || entry.timestamp >= startDate) &&
            entry.timestamp <= endDate;
          return matchesType && matchesDateRange;
        })
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      const totalEntries = filteredEntries.length;
      const totalPages = Math.ceil(totalEntries / limit);
      const offset = (page - 1) * limit;
      filteredEntries = filteredEntries.slice(offset, offset + limit);
      return {
        entries: filteredEntries,
        pagination: {
          total: totalEntries,
          totalPages,
          currentPage: page,
          limit,
        },
      };
    } catch (error) {
      console.error("Error getting user history:", error);
      throw error;
    }
  }
  async getDailyUsage(
    userId,
    startDate,
    endDate = /* @__PURE__ */ new Date().toISOString()
  ) {
    try {
      const { keys } = await this.env.USERS_KV.list({
        prefix: `${_UsageHistoryService.KEYS.DAILY}:${userId}:`,
      });
      const dailyEntries = await Promise.all(
        keys
          .filter((key) => {
            const date = key.name.split(":")[3];
            return (
              (!startDate || date >= startDate) && date <= endDate.split("T")[0]
            );
          })
          .map((key) => this.env.USERS_KV.get(key.name, "json"))
      );
      return dailyEntries.sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error("Error getting daily usage:", error);
      throw error;
    }
  }
};
var UsageHistoryService = _UsageHistoryService;
__name(UsageHistoryService, "UsageHistoryService");
__publicField(UsageHistoryService, "KEYS", {
  HISTORY: "usage_history",
  DAILY: "daily_usage",
});

// src/controllers/usageController.js
var UsageController = class {
  constructor(env) {
    this.env = env;
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
    this.apiKeyService = new ApiKeyService();
    this.historyService = new UsageHistoryService(env);
  }
  async _validateApiKey(request) {
    const apiKey = request.headers.get("x-sps-key");
    if (!apiKey) {
      throw new Error("API Key is required");
    }
    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return user;
  }
  async trackImageUsage(request) {
    try {
      const user = await this._validateApiKey(request);
      const data = await request.json();
      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }
      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.id,
        "images"
      );
      const tierStatus = await this.tierService.getUserTierStatus(user.id);
      await this.historyService.trackUsage(user.id, {
        type: "images",
        source: data.source,
        timestamp: data.timestamp,
        metadata: data.metadata,
      });
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            type: "images",
            usage: updatedUsage,
            source: data.source,
            timestamp: data.timestamp,
            tierStatus: {
              current: updatedUsage,
              remaining: tierStatus.remainingImagesQuota,
              limit: tierStatus.imagesQuota,
            },
          })
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async trackContentUsage(request) {
    try {
      const user = await this._validateApiKey(request);
      const data = await request.json();
      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }
      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.id,
        "content"
      );
      const tierStatus = await this.tierService.getUserTierStatus(user.id);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            {
              type: "content",
              usage: updatedUsage,
              source: data.source,
              timestamp: data.timestamp,
              tierStatus: {
                current: updatedUsage,
                remaining: tierStatus.remainingContentQuota,
                limit: tierStatus.contentQuota,
              },
            },
            "Content usage tracked successfully"
          )
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getUsageStats(request) {
    try {
      const user = await this._validateApiKey(request);
      const tierStatus = await this.tierService.getUserTierStatus(user.id);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            images: {
              used: tierStatus.imagesUsage || 0,
              remaining: tierStatus.remainingImagesQuota,
              total: tierStatus.imagesQuota,
              percentageUsed: (
                ((tierStatus.imagesUsage || 0) / tierStatus.imagesQuota) *
                100
              ).toFixed(2),
            },
            content: {
              used: tierStatus.contentUsage || 0,
              remaining: tierStatus.remainingContentQuota,
              total: tierStatus.contentQuota,
              percentageUsed: (
                ((tierStatus.contentUsage || 0) / tierStatus.contentQuota) *
                100
              ).toFixed(2),
            },
            title: {
              used: tierStatus.titleUsage || 0,
              remaining: tierStatus.remainingTitleQuota,
              total: tierStatus.titleQuota,
              percentageUsed: (
                ((tierStatus.titleUsage || 0) / tierStatus.titleQuota) *
                100
              ).toFixed(2),
            },
            tier: {
              name: tierStatus.tierName,
              current: tierStatus.currentTier,
              expirationDate: tierStatus.expirationDate,
              isExpired: tierStatus.isExpired,
              price: tierStatus.price,
            },
            addons: {
              addon1: {
                enabled: tierStatus.addon1,
                price: tierStatus.addon1_price,
                features: tierStatus.addon1_detail,
              },
              addon2: {
                enabled: tierStatus.addon2,
                price: tierStatus.addon2_price,
                features: tierStatus.addon2_detail,
              },
            },
            features: tierStatus.features,
          })
        ),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getUsageHistory(request) {
    try {
      const user = await this._validateApiKey(request);
      const url = new URL(request.url);
      const options = {
        startDate: url.searchParams.get("startDate"),
        endDate: url.searchParams.get("endDate"),
        type: url.searchParams.get("type"),
        limit: parseInt(url.searchParams.get("limit") || "100"),
        page: parseInt(url.searchParams.get("page") || "1"),
      };
      const history = await this.historyService.getUserHistory(
        user.id,
        options
      );
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(history)),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getDailyUsage(request) {
    try {
      const user = await this._validateApiKey(request);
      const url = new URL(request.url);
      const startDate = url.searchParams.get("startDate");
      const endDate = url.searchParams.get("endDate");
      const dailyUsage = await this.historyService.getDailyUsage(
        user.id,
        startDate,
        endDate
      );
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(dailyUsage)),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async trackTitleUsage(request) {
    try {
      const user = await this._validateApiKey(request);
      const data = await request.json();
      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }
      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.id,
        "title"
      );
      const tierStatus = await this.tierService.getUserTierStatus(user.id);
      await this.historyService.trackUsage(user.id, {
        type: "title",
        source: data.source,
        timestamp: data.timestamp,
        metadata: data.metadata,
      });
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            tracked: true,
            currentUsage: updatedUsage,
            remainingQuota: tierStatus.remainingTitleQuota,
            timestamp: data.timestamp,
            tierStatus: {
              current: updatedUsage,
              remaining: tierStatus.remainingTitleQuota,
              limit: tierStatus.titleQuota,
            },
          })
        ),
        {
          status: 201,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
};
__name(UsageController, "UsageController");

// src/routes/usageRoutes.js
function createUsageRouter(env) {
  const router2 = e({ base: "/api/usage" });
  const controller = new UsageController(env);
  router2.post("/images", (request) => controller.trackImageUsage(request));
  router2.post("/content", (request) => controller.trackContentUsage(request));
  router2.post("/title", (request) => controller.trackTitleUsage(request));
  router2.get("/stats", (request) => controller.getUsageStats(request));
  router2.get("/history", (request) => controller.getUsageHistory(request));
  router2.get("/daily", (request) => controller.getDailyUsage(request));
  return router2;
}
__name(createUsageRouter, "createUsageRouter");

// src/services/webhookService.js
var WebhookService = class {
  constructor(env) {
    this.env = env;
    this.tierService = new TierService2(env);
    this.emailQueueService = new EmailQueueService(env);
    this.isDevelopment =
      env.NODE_ENV === "development" ||
      env.ENVIRONMENT === "development" ||
      env.PAYPAL_SANDBOX === "true";
  }
  logWebhook(message, data = {}) {
    const timestamp = /* @__PURE__ */ new Date().toISOString();
    console.log("\n\u{1F4EE} WEBHOOK:", message);
    console.log("\u231A", timestamp);
    console.log("\u{1F4C4} Data:", JSON.stringify(data, null, 2), "\n");
  }
  logError(message, error) {
    console.error("\n\u274C WEBHOOK ERROR:", message);
    console.error("\u{1F50D} Details:", error);
    console.error("\u{1F4CD} Stack:", error.stack, "\n");
  }
  async verifyWebhookSignature(request) {
    const headers = request.headers;
    const isSimulation = headers.get("PAYPAL-SIMULATION") === "true";
    if (this.isDevelopment || isSimulation) {
      console.log(
        "\u{1F4DD} Development/Sandbox mode or simulation: Skipping webhook signature verification"
      );
      return true;
    }
    const webhookId = this.env.PAYPAL_WEBHOOK_ID;
    const transmissionId = headers.get("paypal-transmission-id");
    const timestamp = headers.get("paypal-transmission-time");
    const signature = headers.get("paypal-transmission-sig");
    const certUrl = headers.get("paypal-cert-url");
    console.log("\u{1F4E8} Webhook headers received:", {
      webhookId: webhookId ? "(present)" : "(missing)",
      transmissionId,
      timestamp,
      signature: signature ? "(present)" : "(missing)",
      certUrl,
      environment: this.isDevelopment ? "development" : "production",
    });
    if (!webhookId || !transmissionId || !timestamp || !signature || !certUrl) {
      if (this.isDevelopment) {
        console.warn(
          "\u26A0\uFE0F Missing webhook headers in development mode - continuing anyway"
        );
        return true;
      }
      throw new Error("Missing required webhook headers");
    }
    return true;
  }
  async processWebhookEvent(event) {
    this.logWebhook("Received webhook event", {
      eventType: event.event_type,
      eventId: event.id,
      timestamp: event.create_time,
    });
    try {
      await this.storeWebhookEvent(event);
      let result;
      switch (event.event_type) {
        case "BILLING.SUBSCRIPTION.ACTIVATED":
          this.logWebhook("Processing subscription activation");
          result = await this.handleSubscriptionActivated(event);
          break;
        case "PAYMENT.SALE.COMPLETED":
          this.logWebhook("Processing payment completion");
          result = await this.handlePaymentCompleted(event);
          break;
        case "BILLING.SUBSCRIPTION.CANCELLED":
          this.logWebhook("Processing subscription cancellation");
          result = await this.handleSubscriptionCancelled(event);
          break;
        case "BILLING.SUBSCRIPTION.PAYMENT.FAILED":
          this.logWebhook("Processing payment failure");
          result = await this.handlePaymentFailed(event);
          break;
        default:
          this.logWebhook("Unhandled event type", {
            eventType: event.event_type,
          });
          return { status: "unhandled", eventType: event.event_type };
      }
      this.logWebhook("Successfully processed webhook", result);
      return result;
    } catch (error) {
      this.logError("Failed to process webhook", error);
      throw error;
    }
  }
  async handleSubscriptionActivated(event) {
    try {
      const subscription = event.resource;
      this.logWebhook("Processing subscription activation", {
        subscriptionId: subscription.id,
      });
      const subscriptionData = await this.getSubscriptionData(subscription.id);
      if (!subscriptionData) {
        throw new Error(`Subscription not found: ${subscription.id}`);
      }
      this.logWebhook("Found subscription data", subscriptionData);
      this.logWebhook("Updating user tier", {
        userId: subscriptionData.userId,
        tier: subscriptionData.tier,
      });
      await this.tierService.setUserTier(
        subscriptionData.userId,
        subscriptionData.tier
      );
      this.logWebhook("Queueing welcome email", {
        email: subscription.subscriber.email_address,
      });
      await this.emailQueueService.addToQueue({
        email: subscription.subscriber.email_address,
        type: "subscription_activated",
        data: {
          tier: subscriptionData.tier,
          price: subscriptionData.price,
          nextBillingDate: subscription.billing_info?.next_billing_time,
        },
      });
      this.logWebhook("Updating subscription status", {
        status: subscription.status,
        nextBilling: subscription.billing_info?.next_billing_time,
      });
      await this.updateSubscriptionStatus(
        subscriptionData.userId,
        subscription.id,
        {
          status: subscription.status,
          nextBillingTime: subscription.billing_info?.next_billing_time,
          lastPayment: subscription.billing_info?.last_payment,
          startTime: subscription.start_time,
        }
      );
      const result = { status: "activated", subscriptionId: subscription.id };
      this.logWebhook("Successfully activated subscription", result);
      return result;
    } catch (error) {
      this.logError("Failed to activate subscription", error);
      throw error;
    }
  }
  async handlePaymentCompleted(event) {
    const payment = event.resource;
    const subscriptionId = payment.billing_agreement_id;
    this.logWebhook("Processing payment completion", {
      paymentId: payment.id,
      amount: payment.amount,
    });
    const subscriptionData = await this.getSubscriptionData(subscriptionId);
    if (!subscriptionData) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }
    await this.recordPayment({
      id: payment.id,
      subscriptionId,
      userId: subscriptionData.userId,
      amount: payment.amount.total,
      currency: payment.amount.currency,
      status: payment.state,
      createTime: payment.create_time,
      updateTime: payment.update_time,
    });
    await this.emailQueueService.addToQueue({
      email: subscriptionData.email,
      type: "payment_success",
      data: {
        amount: payment.amount.total,
        currency: payment.amount.currency,
        date: payment.create_time,
        tier: subscriptionData.tier,
      },
    });
    return { status: "completed", paymentId: payment.id };
  }
  async handleSubscriptionCancelled(event) {
    const subscription = event.resource;
    this.logWebhook("Processing subscription cancellation", {
      subscriptionId: subscription.id,
    });
    const subscriptionData = await this.getSubscriptionData(subscription.id);
    if (!subscriptionData) {
      throw new Error(`Subscription not found: ${subscription.id}`);
    }
    await this.tierService.setUserTier(subscriptionData.userId, "free");
    await this.emailQueueService.addToQueue({
      email: subscription.subscriber.email_address,
      type: "subscription_cancelled",
      data: {
        cancellationDate: subscription.status_update_time,
        previousTier: subscriptionData.tier,
      },
    });
    await this.updateSubscriptionStatus(
      subscriptionData.userId,
      subscription.id,
      {
        status: subscription.status,
        cancellationTime: subscription.status_update_time,
      }
    );
    return { status: "cancelled", subscriptionId: subscription.id };
  }
  async handlePaymentFailed(event) {
    const subscription = event.resource;
    const subscriptionData = await this.getSubscriptionData(subscription.id);
    if (!subscriptionData) {
      throw new Error(`Subscription not found: ${subscription.id}`);
    }
    await this.emailQueueService.addToQueue({
      email: subscription.subscriber.email_address,
      type: "payment_failed",
      data: {
        amount: subscription.billing_info.outstanding_balance.value,
        currency: subscription.billing_info.outstanding_balance.currency_code,
        failedPaymentsCount: subscription.billing_info.failed_payments_count,
        nextRetry: subscription.billing_info.next_payment_retry_time,
      },
    });
    await this.updateSubscriptionStatus(
      subscriptionData.userId,
      subscription.id,
      {
        status: subscription.status,
        failedPaymentsCount: subscription.billing_info.failed_payments_count,
        lastFailedPayment: subscription.billing_info.last_failed_payment,
      }
    );
    return { status: "failed", subscriptionId: subscription.id };
  }
  // Helper methods
  async getSubscriptionData(subscriptionId) {
    this.logWebhook("Looking up subscription data", { subscriptionId });
    const { keys } = await this.env.USERS_KV.list({ prefix: "subscription:" });
    for (const key of keys) {
      const data = await this.env.USERS_KV.get(key.name, "json");
      if (data?.subscriptionId === subscriptionId) {
        this.logWebhook("Found subscription data", {
          userId: key.name.split(":")[1],
          subscription: data,
        });
        return {
          ...data,
          userId: key.name.split(":")[1],
        };
      }
    }
    this.logWebhook("Subscription not found", { subscriptionId });
    return null;
  }
  async storeWebhookEvent(event) {
    const eventId = `webhook_event:${event.id}`;
    await this.env.USERS_KV.put(
      eventId,
      JSON.stringify({
        ...event,
        receivedAt: /* @__PURE__ */ new Date().toISOString(),
      })
    );
  }
  async updateSubscriptionStatus(userId, subscriptionId, updates) {
    const key = `subscription:${userId}`;
    const data = await this.env.USERS_KV.get(key, "json");
    if (data && data.subscriptionId === subscriptionId) {
      await this.env.USERS_KV.put(
        key,
        JSON.stringify({
          ...data,
          ...updates,
          updatedAt: /* @__PURE__ */ new Date().toISOString(),
        })
      );
    }
  }
  async recordPayment(payment) {
    await this.env.USERS_KV.put(
      `payment:${payment.id}`,
      JSON.stringify({
        ...payment,
        recordedAt: /* @__PURE__ */ new Date().toISOString(),
      })
    );
  }
};
__name(WebhookService, "WebhookService");

// src/controllers/webhookController.js
var WebhookController = class {
  constructor(env) {
    this.env = env;
    this.webhookService = new WebhookService(env);
    this.responseService = new ResponseService();
  }
  async handlePayPalWebhook(request) {
    try {
      await this.webhookService.verifyWebhookSignature(request);
      const event = await request.json();
      const result = await this.webhookService.processWebhookEvent(event);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(result)),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Webhook processing error:", error);
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.status || 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getWebhookStatus(request) {
    try {
      const status = await this.webhookService.getWebhookStatus();
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(status)),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  async getRecentEvents(request) {
    try {
      const events = await this.webhookService.getRecentEvents();
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(events)),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
};
__name(WebhookController, "WebhookController");

// src/routes/webhookRoutes.js
function createWebhookRouter(env) {
  const router2 = e({ base: "/api/webhooks" });
  const webhookController = new WebhookController(env);
  router2.post("/paypal", (request) =>
    webhookController.handlePayPalWebhook(request)
  );
  router2.get("/status", (request) =>
    webhookController.getWebhookStatus(request)
  );
  router2.get("/events", (request) =>
    webhookController.getRecentEvents(request)
  );
  return router2;
}
__name(createWebhookRouter, "createWebhookRouter");

// src/controllers/webhookTestController.js
var WebhookTestController = class {
  constructor(env) {
    this.env = env;
  }
  async simulatePayPalWebhook(request) {
    try {
      const { eventType, subscriptionId } = await request.json();
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:",
      });
      let subscriptionData = null;
      let userId = null;
      for (const key of keys) {
        const data = await this.env.USERS_KV.get(key.name, "json");
        if (data?.subscriptionId === subscriptionId) {
          subscriptionData = data;
          userId = key.name.split(":")[1];
          break;
        }
      }
      if (!subscriptionData || !userId) {
        throw new Error(`Subscription not found: ${subscriptionId}`);
      }
      const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
      if (!user) {
        throw new Error(`User not found for subscription: ${subscriptionId}`);
      }
      let webhookEvent;
      if (eventType === "PAYMENT.SALE.COMPLETED") {
        webhookEvent = this.generatePaymentEvent(
          subscriptionId,
          subscriptionData,
          user
        );
      } else {
        webhookEvent = this.generateSubscriptionEvent(
          eventType,
          subscriptionId,
          subscriptionData,
          user
        );
      }
      const transmissionId = crypto.randomUUID();
      const webhookHeaders = {
        "paypal-auth-algo": "SHA256withRSA",
        "paypal-cert-url":
          "https://api.sandbox.paypal.com/v1/notifications/certs/CERT-360caa42-fca2a594-bc34f77b",
        "paypal-transmission-id": transmissionId,
        "paypal-transmission-sig": `mock_${transmissionId}`,
        "paypal-transmission-time": webhookEvent.create_time,
        "Content-Type": "application/json",
      };
      const response = await fetch(
        new URL("/api/webhooks/paypal", request.url),
        {
          method: "POST",
          headers: webhookHeaders,
          body: JSON.stringify(webhookEvent),
        }
      );
      const result = await response.json();
      return new Response(
        JSON.stringify({
          success: true,
          message: `PayPal ${eventType} webhook simulated`,
          details: {
            webhookId: webhookEvent.id,
            transmissionId,
            subscription: subscriptionId,
            user: user.email,
            timestamp: webhookEvent.create_time,
            amount: subscriptionData.price,
          },
          result,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Webhook simulation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
  generatePaymentEvent(subscriptionId, subscriptionData, user) {
    const now = /* @__PURE__ */ new Date().toISOString();
    const paymentId = `PAY-${crypto.randomUUID()}`;
    return {
      id: `WH-${crypto.randomUUID()}`,
      create_time: now,
      resource_type: "sale",
      event_type: "PAYMENT.SALE.COMPLETED",
      summary: "Payment completed for subscription",
      resource: {
        id: paymentId,
        state: "completed",
        amount: {
          total: subscriptionData.price.toString(),
          currency: "USD",
          details: {
            subtotal: subscriptionData.price.toString(),
          },
        },
        payment_mode: "INSTANT_TRANSFER",
        protection_eligibility: "ELIGIBLE",
        protection_eligibility_type:
          "ITEM_NOT_RECEIVED_ELIGIBLE,UNAUTHORIZED_PAYMENT_ELIGIBLE",
        transaction_fee: {
          value: (subscriptionData.price * 0.029 + 0.3).toFixed(2),
          currency: "USD",
        },
        billing_agreement_id: subscriptionId,
        create_time: now,
        update_time: now,
        links: [
          {
            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}`,
            rel: "self",
            method: "GET",
          },
          {
            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}/refund`,
            rel: "refund",
            method: "POST",
          },
        ],
      },
      links: [
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,
          rel: "self",
          method: "GET",
        },
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,
          rel: "resend",
          method: "POST",
        },
      ],
    };
  }
  generateSubscriptionEvent(eventType, subscriptionId, subscriptionData, user) {
    const now = /* @__PURE__ */ new Date().toISOString();
    return {
      id: `WH-${crypto.randomUUID()}`,
      event_type: eventType,
      event_version: "1.0",
      create_time: now,
      resource_type: "subscription",
      resource_version: "2.0",
      summary: `Subscription ${eventType.split(".").pop().toLowerCase()}`,
      resource: {
        start_time: subscriptionData.createdAt,
        quantity: "1",
        subscriber: {
          name: {
            given_name: user.email.split("@")[0],
            surname: "",
          },
          email_address: user.email,
          payer_id: user.id,
        },
        status: "ACTIVE",
        status_update_time: now,
        id: subscriptionId,
        plan_id: subscriptionData.planId,
        billing_info: {
          outstanding_balance: {
            currency_code: "USD",
            value: "0.00",
          },
          cycle_executions: [
            {
              tenure_type: "REGULAR",
              sequence: 1,
              cycles_completed: 1,
              cycles_remaining: 0,
              current_pricing_scheme_version: 1,
            },
          ],
          last_payment: {
            amount: {
              currency_code: "USD",
              value: subscriptionData.price.toString(),
            },
            time: now,
          },
          next_billing_time: new Date(
            Date.now() + 30 * 24 * 60 * 60 * 1e3
          ).toISOString(),
          failed_payments_count: 0,
        },
      },
      links: [
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,
          rel: "self",
          method: "GET",
        },
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,
          rel: "resend",
          method: "POST",
        },
      ],
    };
  }
};
__name(WebhookTestController, "WebhookTestController");

// src/routes/webhookTestRoutes.js
function createWebhookTestRouter(env) {
  const router2 = e({ base: "/api/test/webhooks" });
  const controller = new WebhookTestController(env);
  router2.post("/simulate/paypal", (request) =>
    controller.simulatePayPalWebhook(request)
  );
  return router2;
}
__name(createWebhookTestRouter, "createWebhookTestRouter");

// src/index.js
var corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  // In production, replace with your frontend domain
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, x-api-key",
  "Access-Control-Max-Age": "86400",
};
function handleOptions(request) {
  return new Response(null, {
    headers: corsHeaders,
  });
}
__name(handleOptions, "handleOptions");
function addCorsHeaders(response) {
  const newHeaders = new Headers(response.headers);
  Object.entries(corsHeaders).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders,
  });
}
__name(addCorsHeaders, "addCorsHeaders");
var router = e();
async function initializeTierSettings(env) {
  const tierService = new TierService2(env);
  await tierService.initializeTierSettings();
  console.log("Tier settings initialized");
}
__name(initializeTierSettings, "initializeTierSettings");
async function processEmailQueue(env) {
  try {
    console.log("Starting scheduled email queue processing...");
    const emailQueueService = new EmailQueueService(env);
    const beforeStatus = await emailQueueService.getQueueStatus();
    console.log("Queue status before processing:", beforeStatus);
    const results = await emailQueueService.processQueue(true);
    console.log("Queue processing results:", results);
    const afterStatus = await emailQueueService.getQueueStatus();
    console.log("Queue status after processing:", afterStatus);
    return { beforeStatus, results, afterStatus };
  } catch (error) {
    console.error("Error processing email queue:", error);
    throw error;
  }
}
__name(processEmailQueue, "processEmailQueue");
router.options("*", handleOptions);
router.get("/api/docs", () => {
  return addCorsHeaders(
    new Response(getSwaggerHTML(swaggerDocument), {
      headers: {
        "content-type": "text/html;charset=UTF-8",
      },
    })
  );
});
router.all("/api/subscriptions/*", async (request, env) => {
  try {
    const subscriptionRouter = createSubscriptionRouter(env);
    const response = await subscriptionRouter.handle(request);
    return addCorsHeaders(response);
  } catch (error) {
    console.error("Subscription route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      )
    );
  }
});
router.all("/api/tiers/*", async (request, env) => {
  const tierRouter = createTierRouter(env);
  const response = await tierRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/usage/*", async (request, env) => {
  const usageRouter = createUsageRouter(env);
  const response = await usageRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/users/*", async (request, env) => {
  const userRouter = createUserRouter(env);
  const response = await userRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/debug/*", async (request, env) => {
  try {
    const debugRouter = createDebugRouter(env);
    const response = await debugRouter.handle(request);
    return addCorsHeaders(response);
  } catch (error) {
    console.error("Debug route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      )
    );
  }
});
router.all("/api/test/webhooks/*", async (request, env) => {
  const testRouter = createWebhookTestRouter(env);
  const response = await testRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/webhooks/*", async (request, env) => {
  const webhookRouter = createWebhookRouter(env);
  const response = await webhookRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("*", () =>
  addCorsHeaders(new Response("Not Found", { status: 404 }))
);
var src_default = {
  // Regular request handler
  async fetch(request, env, ctx) {
    try {
      await initializeTierSettings(env);
      if (request.method === "OPTIONS") {
        return handleOptions(request);
      }
      if (
        request.method === "POST" &&
        request.url.endsWith("/api/debug/process-queue")
      ) {
        const results = await processEmailQueue(env);
        return addCorsHeaders(
          new Response(
            JSON.stringify({
              success: true,
              message: "Queue processed manually",
              ...results,
            }),
            {
              headers: { "Content-Type": "application/json" },
            }
          )
        );
      }
      const response = await router.handle(request, env, ctx);
      return addCorsHeaders(response);
    } catch (error) {
      console.error("Worker error:", error);
      return addCorsHeaders(
        new Response(
          JSON.stringify({
            success: false,
            error: error.message,
          }),
          {
            status: 500,
            headers: { "Content-Type": "application/json" },
          }
        )
      );
    }
  },
  // Scheduled handler for cron
  async scheduled(event, env, ctx) {
    console.log(
      `Cron triggered: ${
        event.cron
      } at ${/* @__PURE__ */ new Date().toISOString()}`
    );
    try {
      ctx.waitUntil(processEmailQueue(env));
    } catch (error) {
      console.error("Scheduled job error:", error);
    }
  },
};

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(
  async (request, env, _ctx, middlewareCtx) => {
    try {
      return await middlewareCtx.next(request, env);
    } finally {
      try {
        if (request.body !== null && !request.bodyUsed) {
          const reader = request.body.getReader();
          while (!(await reader.read()).done) {}
        }
      } catch (e2) {
        console.error("Failed to drain the unused request body.", e2);
      }
    }
  },
  "drainBody"
);
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-scheduled.ts
var scheduled = /* @__PURE__ */ __name(
  async (request, env, _ctx, middlewareCtx) => {
    const url = new URL(request.url);
    if (url.pathname === "/__scheduled") {
      const cron = url.searchParams.get("cron") ?? "";
      await middlewareCtx.dispatch("scheduled", { cron });
      return new Response("Ran scheduled event");
    }
    const resp = await middlewareCtx.next(request, env);
    if (
      request.headers.get("referer")?.endsWith("/__scheduled") &&
      url.pathname === "/favicon.ico" &&
      resp.status === 500
    ) {
      return new Response(null, { status: 404 });
    }
    return resp;
  },
  "scheduled"
);
var middleware_scheduled_default = scheduled;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e2) {
  return {
    name: e2?.name,
    message: e2?.message ?? String(e2),
    stack: e2?.stack,
    cause: e2?.cause === void 0 ? void 0 : reduceError(e2.cause),
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(
  async (request, env, _ctx, middlewareCtx) => {
    try {
      return await middlewareCtx.next(request, env);
    } catch (e2) {
      const error = reduceError(e2);
      return Response.json(error, {
        status: 500,
        headers: { "MF-Experimental-Error-Stack": "true" },
      });
    }
  },
  "jsonError"
);
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-mB1F46/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_scheduled_default,
  middleware_miniflare3_json_error_default,
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    },
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware,
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-mB1F46/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (
    __INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 ||
    __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0
  ) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function (request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function (type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {}
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    },
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (
    __INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 ||
    __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0
  ) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {}
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default,
};
//# sourceMappingURL=index.js.map
