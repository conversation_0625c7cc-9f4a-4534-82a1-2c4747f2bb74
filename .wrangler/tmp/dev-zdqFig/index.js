var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-NTB3EG/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// node_modules/itty-router/dist/itty-router.mjs
var e = /* @__PURE__ */ __name(({ base: e2 = "", routes: r = [] } = {}) => ({ __proto__: new Proxy({}, { get: (a, o, t) => (a2, ...p) => r.push([o.toUpperCase(), RegExp(`^${(e2 + a2).replace(/(\/?)\*/g, "($1.*)?").replace(/(\/$)|((?<=\/)\/)/, "").replace(/(:(\w+)\+)/, "(?<$2>.*)").replace(/:(\w+)(\?)?(\.)?/g, "$2(?<$1>[^/]+)$2$3").replace(/\.(?=[\w(])/, "\\.").replace(/\)\.\?\(([^\[]+)\[\^/g, "?)\\.?($1(?<=\\.)[^\\.")}/*$`), p]) && t }), routes: r, async handle(e3, ...a) {
  let o, t, p = new URL(e3.url), l = e3.query = {};
  for (let [e4, r2] of p.searchParams)
    l[e4] = void 0 === l[e4] ? r2 : [l[e4], r2].flat();
  for (let [l2, s, c] of r)
    if ((l2 === e3.method || "ALL" === l2) && (t = p.pathname.match(s))) {
      e3.params = t.groups || {};
      for (let r2 of c)
        if (void 0 !== (o = await r2(e3.proxy || e3, ...a)))
          return o;
    }
} }), "e");

// src/services/responseService.js
var ResponseService = class {
  formatSuccess(data, message = null) {
    return {
      success: true,
      data,
      message,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  formatError(message, statusCode = 500) {
    return {
      success: false,
      message,
      statusCode,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
};
__name(ResponseService, "ResponseService");

// src/services/userService.js
var UserService = class {
  constructor(env) {
    this.env = env;
  }
  async getUserDetail(apiKey) {
    const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return this._formatUserData(user);
  }
  async createUser(userData) {
    const existingUser = await this.env.USERS_KV.get(`email:${userData.email}`, "json");
    if (existingUser) {
      throw new Error("Email already exists");
    }
    const existingUsername = await this.env.USERS_KV.get(`username:${userData.username}`, "json");
    if (existingUsername) {
      throw new Error("Username already exists");
    }
    const apiKey = crypto.randomUUID();
    const user = {
      id: crypto.randomUUID(),
      username: userData.username,
      email: userData.email,
      password: await this._hashPassword(userData.password),
      api_key: apiKey,
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    await Promise.all([
      this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`username:${user.username}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`api_key:${apiKey}`, JSON.stringify(user))
    ]);
    return this._formatUserData(user);
  }
  async _hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hash = await crypto.subtle.digest("SHA-256", data);
    return Array.from(new Uint8Array(hash)).map((b) => b.toString(16).padStart(2, "0")).join("");
  }
  _formatUserData(user) {
    const { password, ...userData } = user;
    return userData;
  }
};
__name(UserService, "UserService");

// src/services/apiKeyService.js
var ApiKeyService = class {
  async validateApiKey(request, env) {
    const apiKey = request.headers.get("x-api-key");
    if (!apiKey) {
      throw new Error("API Key is required in X-API-KEY header");
    }
    const user = await env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!user) {
      throw new Error("Invalid API Key");
    }
    return user;
  }
};
__name(ApiKeyService, "ApiKeyService");

// src/services/validationService.js
var ValidationService = class {
  validateUserData(userData) {
    const errors = [];
    if (!userData.username)
      errors.push("Username is required");
    if (!userData.email)
      errors.push("Email is required");
    if (!userData.password)
      errors.push("Password is required");
    if (userData.username && !this._isValidUsername(userData.username)) {
      errors.push("Username must be at least 3 characters long and contain only letters, numbers, and underscores");
    }
    if (userData.email && !this._isValidEmail(userData.email)) {
      errors.push("Invalid email format");
    }
    if (userData.password && !this._isValidPassword(userData.password)) {
      errors.push("Password must be at least 6 characters long");
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  _isValidUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]{3,}$/;
    return usernameRegex.test(username);
  }
  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  _isValidPassword(password) {
    return password.length >= 6;
  }
};
__name(ValidationService, "ValidationService");

// src/controllers/userController.js
var UserController = class {
  constructor(env) {
    this.env = env;
    this.userService = new UserService(env);
    this.apiKeyService = new ApiKeyService();
    this.responseService = new ResponseService();
    this.validationService = new ValidationService();
  }
  async validateApiKey(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(this.responseService.formatSuccess({
            isValid: false,
            message: "API Key is required in X-API-KEY header"
          })),
          { headers: { "Content-Type": "application/json" } }
        );
      }
      try {
        await this.apiKeyService.validateApiKey(request, this.env);
        return new Response(
          JSON.stringify(this.responseService.formatSuccess({
            isValid: true
          })),
          { headers: { "Content-Type": "application/json" } }
        );
      } catch (error) {
        return new Response(
          JSON.stringify(this.responseService.formatSuccess({
            isValid: false,
            message: error.message
          })),
          { headers: { "Content-Type": "application/json" } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError("Server error occurred")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async getUserDetail(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(this.responseService.formatError("API Key is required")),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.getUserDetail(apiKey);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async createUser(request) {
    try {
      const userData = await request.json();
      const { username, email, password } = userData;
      const validation = this.validationService.validateUserData(userData);
      if (!validation.isValid) {
        return new Response(
          JSON.stringify(this.responseService.formatError(validation.errors.join(", "))),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.createUser({ username, email, password });
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user, "User created successfully")),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }
};
__name(UserController, "UserController");

// src/controllers/debugController.js
var DebugController = class {
  constructor(env) {
    this.env = env;
  }
  async listAllKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value
          };
        })
      );
      const groupedData = data.reduce((acc, item) => {
        const prefix = item.key.split(":")[0];
        if (!acc[prefix]) {
          acc[prefix] = [];
        }
        acc[prefix].push(item);
        return acc;
      }, {});
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            summary: {
              totalKeys: list.keys.length,
              prefixes: Object.keys(groupedData),
              countByPrefix: Object.fromEntries(
                Object.entries(groupedData).map(([k, v]) => [k, v.length])
              )
            },
            grouped: groupedData,
            raw: data
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async deleteKVData(request) {
    try {
      const { key } = await request.json();
      if (!key) {
        throw new Error("Key is required");
      }
      await this.env.USERS_KV.delete(key);
      return new Response(
        JSON.stringify({
          success: true,
          message: `Key ${key} deleted successfully`
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(DebugController, "DebugController");

// src/routes/userRoutes.js
function createUserRouter(env) {
  const router2 = e({ base: "/api/users" });
  const userController = new UserController(env);
  const debugController = new DebugController(env);
  router2.post("/validatekey", (request) => userController.validateApiKey(request));
  router2.get("/userdetail", (request) => userController.getUserDetail(request));
  router2.post("/", (request) => userController.createUser(request));
  router2.get("/debug/kv", (request) => debugController.listAllKVData(request));
  router2.post("/debug/kv/delete", (request) => debugController.deleteKVData(request));
  return router2;
}
__name(createUserRouter, "createUserRouter");

// src/index.js
var router = e();
router.all("/api/users/*", (request, env) => {
  const userRouter = createUserRouter(env);
  return userRouter.handle(request);
});
router.all("*", () => new Response("Not Found", { status: 404 }));
var src_default = {
  fetch(request, env, ctx) {
    return router.handle(request, env, ctx);
  }
};

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e2) {
      console.error("Failed to drain the unused request body.", e2);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e2) {
  return {
    name: e2?.name,
    message: e2?.message ?? String(e2),
    stack: e2?.stack,
    cause: e2?.cause === void 0 ? void 0 : reduceError(e2.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e2) {
    const error = reduceError(e2);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-NTB3EG/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-NTB3EG/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
