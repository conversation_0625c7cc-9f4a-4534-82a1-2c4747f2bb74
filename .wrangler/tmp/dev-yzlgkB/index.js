var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};

// .wrangler/tmp/bundle-xQ5NsH/checked-fetch.js
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
var urls;
var init_checked_fetch = __esm({
  ".wrangler/tmp/bundle-xQ5NsH/checked-fetch.js"() {
    urls = /* @__PURE__ */ new Set();
    __name(checkURL, "checkURL");
    globalThis.fetch = new Proxy(globalThis.fetch, {
      apply(target, thisArg, argArray) {
        const [request, init] = argArray;
        checkURL(request, init);
        return Reflect.apply(target, thisArg, argArray);
      }
    });
  }
});

// wrangler-modules-watch:wrangler:modules-watch
var init_wrangler_modules_watch = __esm({
  "wrangler-modules-watch:wrangler:modules-watch"() {
    init_checked_fetch();
    init_modules_watch_stub();
  }
});

// node_modules/wrangler/templates/modules-watch-stub.js
var init_modules_watch_stub = __esm({
  "node_modules/wrangler/templates/modules-watch-stub.js"() {
    init_wrangler_modules_watch();
  }
});

// node_modules/qrcode/lib/can-promise.js
var require_can_promise = __commonJS({
  "node_modules/qrcode/lib/can-promise.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    module.exports = function() {
      return typeof Promise === "function" && Promise.prototype && Promise.prototype.then;
    };
  }
});

// node_modules/qrcode/lib/core/utils.js
var require_utils = __commonJS({
  "node_modules/qrcode/lib/core/utils.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var toSJISFunction;
    var CODEWORDS_COUNT = [
      0,
      // Not used
      26,
      44,
      70,
      100,
      134,
      172,
      196,
      242,
      292,
      346,
      404,
      466,
      532,
      581,
      655,
      733,
      815,
      901,
      991,
      1085,
      1156,
      1258,
      1364,
      1474,
      1588,
      1706,
      1828,
      1921,
      2051,
      2185,
      2323,
      2465,
      2611,
      2761,
      2876,
      3034,
      3196,
      3362,
      3532,
      3706
    ];
    exports.getSymbolSize = /* @__PURE__ */ __name(function getSymbolSize(version) {
      if (!version)
        throw new Error('"version" cannot be null or undefined');
      if (version < 1 || version > 40)
        throw new Error('"version" should be in range from 1 to 40');
      return version * 4 + 17;
    }, "getSymbolSize");
    exports.getSymbolTotalCodewords = /* @__PURE__ */ __name(function getSymbolTotalCodewords(version) {
      return CODEWORDS_COUNT[version];
    }, "getSymbolTotalCodewords");
    exports.getBCHDigit = function(data) {
      let digit = 0;
      while (data !== 0) {
        digit++;
        data >>>= 1;
      }
      return digit;
    };
    exports.setToSJISFunction = /* @__PURE__ */ __name(function setToSJISFunction(f) {
      if (typeof f !== "function") {
        throw new Error('"toSJISFunc" is not a valid function.');
      }
      toSJISFunction = f;
    }, "setToSJISFunction");
    exports.isKanjiModeEnabled = function() {
      return typeof toSJISFunction !== "undefined";
    };
    exports.toSJIS = /* @__PURE__ */ __name(function toSJIS(kanji) {
      return toSJISFunction(kanji);
    }, "toSJIS");
  }
});

// node_modules/qrcode/lib/core/error-correction-level.js
var require_error_correction_level = __commonJS({
  "node_modules/qrcode/lib/core/error-correction-level.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    exports.L = { bit: 1 };
    exports.M = { bit: 0 };
    exports.Q = { bit: 3 };
    exports.H = { bit: 2 };
    function fromString(string) {
      if (typeof string !== "string") {
        throw new Error("Param is not a string");
      }
      const lcStr = string.toLowerCase();
      switch (lcStr) {
        case "l":
        case "low":
          return exports.L;
        case "m":
        case "medium":
          return exports.M;
        case "q":
        case "quartile":
          return exports.Q;
        case "h":
        case "high":
          return exports.H;
        default:
          throw new Error("Unknown EC Level: " + string);
      }
    }
    __name(fromString, "fromString");
    exports.isValid = /* @__PURE__ */ __name(function isValid(level) {
      return level && typeof level.bit !== "undefined" && level.bit >= 0 && level.bit < 4;
    }, "isValid");
    exports.from = /* @__PURE__ */ __name(function from(value, defaultValue) {
      if (exports.isValid(value)) {
        return value;
      }
      try {
        return fromString(value);
      } catch (e2) {
        return defaultValue;
      }
    }, "from");
  }
});

// node_modules/qrcode/lib/core/bit-buffer.js
var require_bit_buffer = __commonJS({
  "node_modules/qrcode/lib/core/bit-buffer.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    function BitBuffer() {
      this.buffer = [];
      this.length = 0;
    }
    __name(BitBuffer, "BitBuffer");
    BitBuffer.prototype = {
      get: function(index) {
        const bufIndex = Math.floor(index / 8);
        return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) === 1;
      },
      put: function(num, length) {
        for (let i = 0; i < length; i++) {
          this.putBit((num >>> length - i - 1 & 1) === 1);
        }
      },
      getLengthInBits: function() {
        return this.length;
      },
      putBit: function(bit) {
        const bufIndex = Math.floor(this.length / 8);
        if (this.buffer.length <= bufIndex) {
          this.buffer.push(0);
        }
        if (bit) {
          this.buffer[bufIndex] |= 128 >>> this.length % 8;
        }
        this.length++;
      }
    };
    module.exports = BitBuffer;
  }
});

// node_modules/qrcode/lib/core/bit-matrix.js
var require_bit_matrix = __commonJS({
  "node_modules/qrcode/lib/core/bit-matrix.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    function BitMatrix(size) {
      if (!size || size < 1) {
        throw new Error("BitMatrix size must be defined and greater than 0");
      }
      this.size = size;
      this.data = new Uint8Array(size * size);
      this.reservedBit = new Uint8Array(size * size);
    }
    __name(BitMatrix, "BitMatrix");
    BitMatrix.prototype.set = function(row, col, value, reserved) {
      const index = row * this.size + col;
      this.data[index] = value;
      if (reserved)
        this.reservedBit[index] = true;
    };
    BitMatrix.prototype.get = function(row, col) {
      return this.data[row * this.size + col];
    };
    BitMatrix.prototype.xor = function(row, col, value) {
      this.data[row * this.size + col] ^= value;
    };
    BitMatrix.prototype.isReserved = function(row, col) {
      return this.reservedBit[row * this.size + col];
    };
    module.exports = BitMatrix;
  }
});

// node_modules/qrcode/lib/core/alignment-pattern.js
var require_alignment_pattern = __commonJS({
  "node_modules/qrcode/lib/core/alignment-pattern.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var getSymbolSize = require_utils().getSymbolSize;
    exports.getRowColCoords = /* @__PURE__ */ __name(function getRowColCoords(version) {
      if (version === 1)
        return [];
      const posCount = Math.floor(version / 7) + 2;
      const size = getSymbolSize(version);
      const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2;
      const positions = [size - 7];
      for (let i = 1; i < posCount - 1; i++) {
        positions[i] = positions[i - 1] - intervals;
      }
      positions.push(6);
      return positions.reverse();
    }, "getRowColCoords");
    exports.getPositions = /* @__PURE__ */ __name(function getPositions(version) {
      const coords = [];
      const pos = exports.getRowColCoords(version);
      const posLength = pos.length;
      for (let i = 0; i < posLength; i++) {
        for (let j = 0; j < posLength; j++) {
          if (i === 0 && j === 0 || // top-left
          i === 0 && j === posLength - 1 || // bottom-left
          i === posLength - 1 && j === 0) {
            continue;
          }
          coords.push([pos[i], pos[j]]);
        }
      }
      return coords;
    }, "getPositions");
  }
});

// node_modules/qrcode/lib/core/finder-pattern.js
var require_finder_pattern = __commonJS({
  "node_modules/qrcode/lib/core/finder-pattern.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var getSymbolSize = require_utils().getSymbolSize;
    var FINDER_PATTERN_SIZE = 7;
    exports.getPositions = /* @__PURE__ */ __name(function getPositions(version) {
      const size = getSymbolSize(version);
      return [
        // top-left
        [0, 0],
        // top-right
        [size - FINDER_PATTERN_SIZE, 0],
        // bottom-left
        [0, size - FINDER_PATTERN_SIZE]
      ];
    }, "getPositions");
  }
});

// node_modules/qrcode/lib/core/mask-pattern.js
var require_mask_pattern = __commonJS({
  "node_modules/qrcode/lib/core/mask-pattern.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    exports.Patterns = {
      PATTERN000: 0,
      PATTERN001: 1,
      PATTERN010: 2,
      PATTERN011: 3,
      PATTERN100: 4,
      PATTERN101: 5,
      PATTERN110: 6,
      PATTERN111: 7
    };
    var PenaltyScores = {
      N1: 3,
      N2: 3,
      N3: 40,
      N4: 10
    };
    exports.isValid = /* @__PURE__ */ __name(function isValid(mask) {
      return mask != null && mask !== "" && !isNaN(mask) && mask >= 0 && mask <= 7;
    }, "isValid");
    exports.from = /* @__PURE__ */ __name(function from(value) {
      return exports.isValid(value) ? parseInt(value, 10) : void 0;
    }, "from");
    exports.getPenaltyN1 = /* @__PURE__ */ __name(function getPenaltyN1(data) {
      const size = data.size;
      let points = 0;
      let sameCountCol = 0;
      let sameCountRow = 0;
      let lastCol = null;
      let lastRow = null;
      for (let row = 0; row < size; row++) {
        sameCountCol = sameCountRow = 0;
        lastCol = lastRow = null;
        for (let col = 0; col < size; col++) {
          let module2 = data.get(row, col);
          if (module2 === lastCol) {
            sameCountCol++;
          } else {
            if (sameCountCol >= 5)
              points += PenaltyScores.N1 + (sameCountCol - 5);
            lastCol = module2;
            sameCountCol = 1;
          }
          module2 = data.get(col, row);
          if (module2 === lastRow) {
            sameCountRow++;
          } else {
            if (sameCountRow >= 5)
              points += PenaltyScores.N1 + (sameCountRow - 5);
            lastRow = module2;
            sameCountRow = 1;
          }
        }
        if (sameCountCol >= 5)
          points += PenaltyScores.N1 + (sameCountCol - 5);
        if (sameCountRow >= 5)
          points += PenaltyScores.N1 + (sameCountRow - 5);
      }
      return points;
    }, "getPenaltyN1");
    exports.getPenaltyN2 = /* @__PURE__ */ __name(function getPenaltyN2(data) {
      const size = data.size;
      let points = 0;
      for (let row = 0; row < size - 1; row++) {
        for (let col = 0; col < size - 1; col++) {
          const last = data.get(row, col) + data.get(row, col + 1) + data.get(row + 1, col) + data.get(row + 1, col + 1);
          if (last === 4 || last === 0)
            points++;
        }
      }
      return points * PenaltyScores.N2;
    }, "getPenaltyN2");
    exports.getPenaltyN3 = /* @__PURE__ */ __name(function getPenaltyN3(data) {
      const size = data.size;
      let points = 0;
      let bitsCol = 0;
      let bitsRow = 0;
      for (let row = 0; row < size; row++) {
        bitsCol = bitsRow = 0;
        for (let col = 0; col < size; col++) {
          bitsCol = bitsCol << 1 & 2047 | data.get(row, col);
          if (col >= 10 && (bitsCol === 1488 || bitsCol === 93))
            points++;
          bitsRow = bitsRow << 1 & 2047 | data.get(col, row);
          if (col >= 10 && (bitsRow === 1488 || bitsRow === 93))
            points++;
        }
      }
      return points * PenaltyScores.N3;
    }, "getPenaltyN3");
    exports.getPenaltyN4 = /* @__PURE__ */ __name(function getPenaltyN4(data) {
      let darkCount = 0;
      const modulesCount = data.data.length;
      for (let i = 0; i < modulesCount; i++)
        darkCount += data.data[i];
      const k = Math.abs(Math.ceil(darkCount * 100 / modulesCount / 5) - 10);
      return k * PenaltyScores.N4;
    }, "getPenaltyN4");
    function getMaskAt(maskPattern, i, j) {
      switch (maskPattern) {
        case exports.Patterns.PATTERN000:
          return (i + j) % 2 === 0;
        case exports.Patterns.PATTERN001:
          return i % 2 === 0;
        case exports.Patterns.PATTERN010:
          return j % 3 === 0;
        case exports.Patterns.PATTERN011:
          return (i + j) % 3 === 0;
        case exports.Patterns.PATTERN100:
          return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0;
        case exports.Patterns.PATTERN101:
          return i * j % 2 + i * j % 3 === 0;
        case exports.Patterns.PATTERN110:
          return (i * j % 2 + i * j % 3) % 2 === 0;
        case exports.Patterns.PATTERN111:
          return (i * j % 3 + (i + j) % 2) % 2 === 0;
        default:
          throw new Error("bad maskPattern:" + maskPattern);
      }
    }
    __name(getMaskAt, "getMaskAt");
    exports.applyMask = /* @__PURE__ */ __name(function applyMask(pattern, data) {
      const size = data.size;
      for (let col = 0; col < size; col++) {
        for (let row = 0; row < size; row++) {
          if (data.isReserved(row, col))
            continue;
          data.xor(row, col, getMaskAt(pattern, row, col));
        }
      }
    }, "applyMask");
    exports.getBestMask = /* @__PURE__ */ __name(function getBestMask(data, setupFormatFunc) {
      const numPatterns = Object.keys(exports.Patterns).length;
      let bestPattern = 0;
      let lowerPenalty = Infinity;
      for (let p = 0; p < numPatterns; p++) {
        setupFormatFunc(p);
        exports.applyMask(p, data);
        const penalty = exports.getPenaltyN1(data) + exports.getPenaltyN2(data) + exports.getPenaltyN3(data) + exports.getPenaltyN4(data);
        exports.applyMask(p, data);
        if (penalty < lowerPenalty) {
          lowerPenalty = penalty;
          bestPattern = p;
        }
      }
      return bestPattern;
    }, "getBestMask");
  }
});

// node_modules/qrcode/lib/core/error-correction-code.js
var require_error_correction_code = __commonJS({
  "node_modules/qrcode/lib/core/error-correction-code.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var ECLevel = require_error_correction_level();
    var EC_BLOCKS_TABLE = [
      // L  M  Q  H
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      2,
      2,
      1,
      2,
      2,
      4,
      1,
      2,
      4,
      4,
      2,
      4,
      4,
      4,
      2,
      4,
      6,
      5,
      2,
      4,
      6,
      6,
      2,
      5,
      8,
      8,
      4,
      5,
      8,
      8,
      4,
      5,
      8,
      11,
      4,
      8,
      10,
      11,
      4,
      9,
      12,
      16,
      4,
      9,
      16,
      16,
      6,
      10,
      12,
      18,
      6,
      10,
      17,
      16,
      6,
      11,
      16,
      19,
      6,
      13,
      18,
      21,
      7,
      14,
      21,
      25,
      8,
      16,
      20,
      25,
      8,
      17,
      23,
      25,
      9,
      17,
      23,
      34,
      9,
      18,
      25,
      30,
      10,
      20,
      27,
      32,
      12,
      21,
      29,
      35,
      12,
      23,
      34,
      37,
      12,
      25,
      34,
      40,
      13,
      26,
      35,
      42,
      14,
      28,
      38,
      45,
      15,
      29,
      40,
      48,
      16,
      31,
      43,
      51,
      17,
      33,
      45,
      54,
      18,
      35,
      48,
      57,
      19,
      37,
      51,
      60,
      19,
      38,
      53,
      63,
      20,
      40,
      56,
      66,
      21,
      43,
      59,
      70,
      22,
      45,
      62,
      74,
      24,
      47,
      65,
      77,
      25,
      49,
      68,
      81
    ];
    var EC_CODEWORDS_TABLE = [
      // L  M  Q  H
      7,
      10,
      13,
      17,
      10,
      16,
      22,
      28,
      15,
      26,
      36,
      44,
      20,
      36,
      52,
      64,
      26,
      48,
      72,
      88,
      36,
      64,
      96,
      112,
      40,
      72,
      108,
      130,
      48,
      88,
      132,
      156,
      60,
      110,
      160,
      192,
      72,
      130,
      192,
      224,
      80,
      150,
      224,
      264,
      96,
      176,
      260,
      308,
      104,
      198,
      288,
      352,
      120,
      216,
      320,
      384,
      132,
      240,
      360,
      432,
      144,
      280,
      408,
      480,
      168,
      308,
      448,
      532,
      180,
      338,
      504,
      588,
      196,
      364,
      546,
      650,
      224,
      416,
      600,
      700,
      224,
      442,
      644,
      750,
      252,
      476,
      690,
      816,
      270,
      504,
      750,
      900,
      300,
      560,
      810,
      960,
      312,
      588,
      870,
      1050,
      336,
      644,
      952,
      1110,
      360,
      700,
      1020,
      1200,
      390,
      728,
      1050,
      1260,
      420,
      784,
      1140,
      1350,
      450,
      812,
      1200,
      1440,
      480,
      868,
      1290,
      1530,
      510,
      924,
      1350,
      1620,
      540,
      980,
      1440,
      1710,
      570,
      1036,
      1530,
      1800,
      570,
      1064,
      1590,
      1890,
      600,
      1120,
      1680,
      1980,
      630,
      1204,
      1770,
      2100,
      660,
      1260,
      1860,
      2220,
      720,
      1316,
      1950,
      2310,
      750,
      1372,
      2040,
      2430
    ];
    exports.getBlocksCount = /* @__PURE__ */ __name(function getBlocksCount(version, errorCorrectionLevel) {
      switch (errorCorrectionLevel) {
        case ECLevel.L:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 0];
        case ECLevel.M:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 1];
        case ECLevel.Q:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 2];
        case ECLevel.H:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 3];
        default:
          return void 0;
      }
    }, "getBlocksCount");
    exports.getTotalCodewordsCount = /* @__PURE__ */ __name(function getTotalCodewordsCount(version, errorCorrectionLevel) {
      switch (errorCorrectionLevel) {
        case ECLevel.L:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0];
        case ECLevel.M:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1];
        case ECLevel.Q:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2];
        case ECLevel.H:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3];
        default:
          return void 0;
      }
    }, "getTotalCodewordsCount");
  }
});

// node_modules/qrcode/lib/core/galois-field.js
var require_galois_field = __commonJS({
  "node_modules/qrcode/lib/core/galois-field.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var EXP_TABLE = new Uint8Array(512);
    var LOG_TABLE = new Uint8Array(256);
    (/* @__PURE__ */ __name(function initTables() {
      let x = 1;
      for (let i = 0; i < 255; i++) {
        EXP_TABLE[i] = x;
        LOG_TABLE[x] = i;
        x <<= 1;
        if (x & 256) {
          x ^= 285;
        }
      }
      for (let i = 255; i < 512; i++) {
        EXP_TABLE[i] = EXP_TABLE[i - 255];
      }
    }, "initTables"))();
    exports.log = /* @__PURE__ */ __name(function log(n) {
      if (n < 1)
        throw new Error("log(" + n + ")");
      return LOG_TABLE[n];
    }, "log");
    exports.exp = /* @__PURE__ */ __name(function exp(n) {
      return EXP_TABLE[n];
    }, "exp");
    exports.mul = /* @__PURE__ */ __name(function mul(x, y) {
      if (x === 0 || y === 0)
        return 0;
      return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]];
    }, "mul");
  }
});

// node_modules/qrcode/lib/core/polynomial.js
var require_polynomial = __commonJS({
  "node_modules/qrcode/lib/core/polynomial.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var GF = require_galois_field();
    exports.mul = /* @__PURE__ */ __name(function mul(p1, p2) {
      const coeff = new Uint8Array(p1.length + p2.length - 1);
      for (let i = 0; i < p1.length; i++) {
        for (let j = 0; j < p2.length; j++) {
          coeff[i + j] ^= GF.mul(p1[i], p2[j]);
        }
      }
      return coeff;
    }, "mul");
    exports.mod = /* @__PURE__ */ __name(function mod(divident, divisor) {
      let result = new Uint8Array(divident);
      while (result.length - divisor.length >= 0) {
        const coeff = result[0];
        for (let i = 0; i < divisor.length; i++) {
          result[i] ^= GF.mul(divisor[i], coeff);
        }
        let offset = 0;
        while (offset < result.length && result[offset] === 0)
          offset++;
        result = result.slice(offset);
      }
      return result;
    }, "mod");
    exports.generateECPolynomial = /* @__PURE__ */ __name(function generateECPolynomial(degree) {
      let poly = new Uint8Array([1]);
      for (let i = 0; i < degree; i++) {
        poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]));
      }
      return poly;
    }, "generateECPolynomial");
  }
});

// node_modules/qrcode/lib/core/reed-solomon-encoder.js
var require_reed_solomon_encoder = __commonJS({
  "node_modules/qrcode/lib/core/reed-solomon-encoder.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Polynomial = require_polynomial();
    function ReedSolomonEncoder(degree) {
      this.genPoly = void 0;
      this.degree = degree;
      if (this.degree)
        this.initialize(this.degree);
    }
    __name(ReedSolomonEncoder, "ReedSolomonEncoder");
    ReedSolomonEncoder.prototype.initialize = /* @__PURE__ */ __name(function initialize(degree) {
      this.degree = degree;
      this.genPoly = Polynomial.generateECPolynomial(this.degree);
    }, "initialize");
    ReedSolomonEncoder.prototype.encode = /* @__PURE__ */ __name(function encode(data) {
      if (!this.genPoly) {
        throw new Error("Encoder not initialized");
      }
      const paddedData = new Uint8Array(data.length + this.degree);
      paddedData.set(data);
      const remainder = Polynomial.mod(paddedData, this.genPoly);
      const start = this.degree - remainder.length;
      if (start > 0) {
        const buff = new Uint8Array(this.degree);
        buff.set(remainder, start);
        return buff;
      }
      return remainder;
    }, "encode");
    module.exports = ReedSolomonEncoder;
  }
});

// node_modules/qrcode/lib/core/version-check.js
var require_version_check = __commonJS({
  "node_modules/qrcode/lib/core/version-check.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    exports.isValid = /* @__PURE__ */ __name(function isValid(version) {
      return !isNaN(version) && version >= 1 && version <= 40;
    }, "isValid");
  }
});

// node_modules/qrcode/lib/core/regex.js
var require_regex = __commonJS({
  "node_modules/qrcode/lib/core/regex.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var numeric = "[0-9]+";
    var alphanumeric = "[A-Z $%*+\\-./:]+";
    var kanji = "(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";
    kanji = kanji.replace(/u/g, "\\u");
    var byte = "(?:(?![A-Z0-9 $%*+\\-./:]|" + kanji + ")(?:.|[\r\n]))+";
    exports.KANJI = new RegExp(kanji, "g");
    exports.BYTE_KANJI = new RegExp("[^A-Z0-9 $%*+\\-./:]+", "g");
    exports.BYTE = new RegExp(byte, "g");
    exports.NUMERIC = new RegExp(numeric, "g");
    exports.ALPHANUMERIC = new RegExp(alphanumeric, "g");
    var TEST_KANJI = new RegExp("^" + kanji + "$");
    var TEST_NUMERIC = new RegExp("^" + numeric + "$");
    var TEST_ALPHANUMERIC = new RegExp("^[A-Z0-9 $%*+\\-./:]+$");
    exports.testKanji = /* @__PURE__ */ __name(function testKanji(str) {
      return TEST_KANJI.test(str);
    }, "testKanji");
    exports.testNumeric = /* @__PURE__ */ __name(function testNumeric(str) {
      return TEST_NUMERIC.test(str);
    }, "testNumeric");
    exports.testAlphanumeric = /* @__PURE__ */ __name(function testAlphanumeric(str) {
      return TEST_ALPHANUMERIC.test(str);
    }, "testAlphanumeric");
  }
});

// node_modules/qrcode/lib/core/mode.js
var require_mode = __commonJS({
  "node_modules/qrcode/lib/core/mode.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var VersionCheck = require_version_check();
    var Regex = require_regex();
    exports.NUMERIC = {
      id: "Numeric",
      bit: 1 << 0,
      ccBits: [10, 12, 14]
    };
    exports.ALPHANUMERIC = {
      id: "Alphanumeric",
      bit: 1 << 1,
      ccBits: [9, 11, 13]
    };
    exports.BYTE = {
      id: "Byte",
      bit: 1 << 2,
      ccBits: [8, 16, 16]
    };
    exports.KANJI = {
      id: "Kanji",
      bit: 1 << 3,
      ccBits: [8, 10, 12]
    };
    exports.MIXED = {
      bit: -1
    };
    exports.getCharCountIndicator = /* @__PURE__ */ __name(function getCharCountIndicator(mode, version) {
      if (!mode.ccBits)
        throw new Error("Invalid mode: " + mode);
      if (!VersionCheck.isValid(version)) {
        throw new Error("Invalid version: " + version);
      }
      if (version >= 1 && version < 10)
        return mode.ccBits[0];
      else if (version < 27)
        return mode.ccBits[1];
      return mode.ccBits[2];
    }, "getCharCountIndicator");
    exports.getBestModeForData = /* @__PURE__ */ __name(function getBestModeForData(dataStr) {
      if (Regex.testNumeric(dataStr))
        return exports.NUMERIC;
      else if (Regex.testAlphanumeric(dataStr))
        return exports.ALPHANUMERIC;
      else if (Regex.testKanji(dataStr))
        return exports.KANJI;
      else
        return exports.BYTE;
    }, "getBestModeForData");
    exports.toString = /* @__PURE__ */ __name(function toString(mode) {
      if (mode && mode.id)
        return mode.id;
      throw new Error("Invalid mode");
    }, "toString");
    exports.isValid = /* @__PURE__ */ __name(function isValid(mode) {
      return mode && mode.bit && mode.ccBits;
    }, "isValid");
    function fromString(string) {
      if (typeof string !== "string") {
        throw new Error("Param is not a string");
      }
      const lcStr = string.toLowerCase();
      switch (lcStr) {
        case "numeric":
          return exports.NUMERIC;
        case "alphanumeric":
          return exports.ALPHANUMERIC;
        case "kanji":
          return exports.KANJI;
        case "byte":
          return exports.BYTE;
        default:
          throw new Error("Unknown mode: " + string);
      }
    }
    __name(fromString, "fromString");
    exports.from = /* @__PURE__ */ __name(function from(value, defaultValue) {
      if (exports.isValid(value)) {
        return value;
      }
      try {
        return fromString(value);
      } catch (e2) {
        return defaultValue;
      }
    }, "from");
  }
});

// node_modules/qrcode/lib/core/version.js
var require_version = __commonJS({
  "node_modules/qrcode/lib/core/version.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Utils = require_utils();
    var ECCode = require_error_correction_code();
    var ECLevel = require_error_correction_level();
    var Mode = require_mode();
    var VersionCheck = require_version_check();
    var G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;
    var G18_BCH = Utils.getBCHDigit(G18);
    function getBestVersionForDataLength(mode, length, errorCorrectionLevel) {
      for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {
        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {
          return currentVersion;
        }
      }
      return void 0;
    }
    __name(getBestVersionForDataLength, "getBestVersionForDataLength");
    function getReservedBitsCount(mode, version) {
      return Mode.getCharCountIndicator(mode, version) + 4;
    }
    __name(getReservedBitsCount, "getReservedBitsCount");
    function getTotalBitsFromDataArray(segments, version) {
      let totalBits = 0;
      segments.forEach(function(data) {
        const reservedBits = getReservedBitsCount(data.mode, version);
        totalBits += reservedBits + data.getBitsLength();
      });
      return totalBits;
    }
    __name(getTotalBitsFromDataArray, "getTotalBitsFromDataArray");
    function getBestVersionForMixedData(segments, errorCorrectionLevel) {
      for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {
        const length = getTotalBitsFromDataArray(segments, currentVersion);
        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {
          return currentVersion;
        }
      }
      return void 0;
    }
    __name(getBestVersionForMixedData, "getBestVersionForMixedData");
    exports.from = /* @__PURE__ */ __name(function from(value, defaultValue) {
      if (VersionCheck.isValid(value)) {
        return parseInt(value, 10);
      }
      return defaultValue;
    }, "from");
    exports.getCapacity = /* @__PURE__ */ __name(function getCapacity(version, errorCorrectionLevel, mode) {
      if (!VersionCheck.isValid(version)) {
        throw new Error("Invalid QR Code version");
      }
      if (typeof mode === "undefined")
        mode = Mode.BYTE;
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;
      if (mode === Mode.MIXED)
        return dataTotalCodewordsBits;
      const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version);
      switch (mode) {
        case Mode.NUMERIC:
          return Math.floor(usableBits / 10 * 3);
        case Mode.ALPHANUMERIC:
          return Math.floor(usableBits / 11 * 2);
        case Mode.KANJI:
          return Math.floor(usableBits / 13);
        case Mode.BYTE:
        default:
          return Math.floor(usableBits / 8);
      }
    }, "getCapacity");
    exports.getBestVersionForData = /* @__PURE__ */ __name(function getBestVersionForData(data, errorCorrectionLevel) {
      let seg;
      const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M);
      if (Array.isArray(data)) {
        if (data.length > 1) {
          return getBestVersionForMixedData(data, ecl);
        }
        if (data.length === 0) {
          return 1;
        }
        seg = data[0];
      } else {
        seg = data;
      }
      return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl);
    }, "getBestVersionForData");
    exports.getEncodedBits = /* @__PURE__ */ __name(function getEncodedBits(version) {
      if (!VersionCheck.isValid(version) || version < 7) {
        throw new Error("Invalid QR Code version");
      }
      let d = version << 12;
      while (Utils.getBCHDigit(d) - G18_BCH >= 0) {
        d ^= G18 << Utils.getBCHDigit(d) - G18_BCH;
      }
      return version << 12 | d;
    }, "getEncodedBits");
  }
});

// node_modules/qrcode/lib/core/format-info.js
var require_format_info = __commonJS({
  "node_modules/qrcode/lib/core/format-info.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Utils = require_utils();
    var G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;
    var G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;
    var G15_BCH = Utils.getBCHDigit(G15);
    exports.getEncodedBits = /* @__PURE__ */ __name(function getEncodedBits(errorCorrectionLevel, mask) {
      const data = errorCorrectionLevel.bit << 3 | mask;
      let d = data << 10;
      while (Utils.getBCHDigit(d) - G15_BCH >= 0) {
        d ^= G15 << Utils.getBCHDigit(d) - G15_BCH;
      }
      return (data << 10 | d) ^ G15_MASK;
    }, "getEncodedBits");
  }
});

// node_modules/qrcode/lib/core/numeric-data.js
var require_numeric_data = __commonJS({
  "node_modules/qrcode/lib/core/numeric-data.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Mode = require_mode();
    function NumericData(data) {
      this.mode = Mode.NUMERIC;
      this.data = data.toString();
    }
    __name(NumericData, "NumericData");
    NumericData.getBitsLength = /* @__PURE__ */ __name(function getBitsLength(length) {
      return 10 * Math.floor(length / 3) + (length % 3 ? length % 3 * 3 + 1 : 0);
    }, "getBitsLength");
    NumericData.prototype.getLength = /* @__PURE__ */ __name(function getLength() {
      return this.data.length;
    }, "getLength");
    NumericData.prototype.getBitsLength = /* @__PURE__ */ __name(function getBitsLength() {
      return NumericData.getBitsLength(this.data.length);
    }, "getBitsLength");
    NumericData.prototype.write = /* @__PURE__ */ __name(function write(bitBuffer) {
      let i, group, value;
      for (i = 0; i + 3 <= this.data.length; i += 3) {
        group = this.data.substr(i, 3);
        value = parseInt(group, 10);
        bitBuffer.put(value, 10);
      }
      const remainingNum = this.data.length - i;
      if (remainingNum > 0) {
        group = this.data.substr(i);
        value = parseInt(group, 10);
        bitBuffer.put(value, remainingNum * 3 + 1);
      }
    }, "write");
    module.exports = NumericData;
  }
});

// node_modules/qrcode/lib/core/alphanumeric-data.js
var require_alphanumeric_data = __commonJS({
  "node_modules/qrcode/lib/core/alphanumeric-data.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Mode = require_mode();
    var ALPHA_NUM_CHARS = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
      " ",
      "$",
      "%",
      "*",
      "+",
      "-",
      ".",
      "/",
      ":"
    ];
    function AlphanumericData(data) {
      this.mode = Mode.ALPHANUMERIC;
      this.data = data;
    }
    __name(AlphanumericData, "AlphanumericData");
    AlphanumericData.getBitsLength = /* @__PURE__ */ __name(function getBitsLength(length) {
      return 11 * Math.floor(length / 2) + 6 * (length % 2);
    }, "getBitsLength");
    AlphanumericData.prototype.getLength = /* @__PURE__ */ __name(function getLength() {
      return this.data.length;
    }, "getLength");
    AlphanumericData.prototype.getBitsLength = /* @__PURE__ */ __name(function getBitsLength() {
      return AlphanumericData.getBitsLength(this.data.length);
    }, "getBitsLength");
    AlphanumericData.prototype.write = /* @__PURE__ */ __name(function write(bitBuffer) {
      let i;
      for (i = 0; i + 2 <= this.data.length; i += 2) {
        let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45;
        value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1]);
        bitBuffer.put(value, 11);
      }
      if (this.data.length % 2) {
        bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6);
      }
    }, "write");
    module.exports = AlphanumericData;
  }
});

// node_modules/qrcode/lib/core/byte-data.js
var require_byte_data = __commonJS({
  "node_modules/qrcode/lib/core/byte-data.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Mode = require_mode();
    function ByteData(data) {
      this.mode = Mode.BYTE;
      if (typeof data === "string") {
        this.data = new TextEncoder().encode(data);
      } else {
        this.data = new Uint8Array(data);
      }
    }
    __name(ByteData, "ByteData");
    ByteData.getBitsLength = /* @__PURE__ */ __name(function getBitsLength(length) {
      return length * 8;
    }, "getBitsLength");
    ByteData.prototype.getLength = /* @__PURE__ */ __name(function getLength() {
      return this.data.length;
    }, "getLength");
    ByteData.prototype.getBitsLength = /* @__PURE__ */ __name(function getBitsLength() {
      return ByteData.getBitsLength(this.data.length);
    }, "getBitsLength");
    ByteData.prototype.write = function(bitBuffer) {
      for (let i = 0, l = this.data.length; i < l; i++) {
        bitBuffer.put(this.data[i], 8);
      }
    };
    module.exports = ByteData;
  }
});

// node_modules/qrcode/lib/core/kanji-data.js
var require_kanji_data = __commonJS({
  "node_modules/qrcode/lib/core/kanji-data.js"(exports, module) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Mode = require_mode();
    var Utils = require_utils();
    function KanjiData(data) {
      this.mode = Mode.KANJI;
      this.data = data;
    }
    __name(KanjiData, "KanjiData");
    KanjiData.getBitsLength = /* @__PURE__ */ __name(function getBitsLength(length) {
      return length * 13;
    }, "getBitsLength");
    KanjiData.prototype.getLength = /* @__PURE__ */ __name(function getLength() {
      return this.data.length;
    }, "getLength");
    KanjiData.prototype.getBitsLength = /* @__PURE__ */ __name(function getBitsLength() {
      return KanjiData.getBitsLength(this.data.length);
    }, "getBitsLength");
    KanjiData.prototype.write = function(bitBuffer) {
      let i;
      for (i = 0; i < this.data.length; i++) {
        let value = Utils.toSJIS(this.data[i]);
        if (value >= 33088 && value <= 40956) {
          value -= 33088;
        } else if (value >= 57408 && value <= 60351) {
          value -= 49472;
        } else {
          throw new Error(
            "Invalid SJIS character: " + this.data[i] + "\nMake sure your charset is UTF-8"
          );
        }
        value = (value >>> 8 & 255) * 192 + (value & 255);
        bitBuffer.put(value, 13);
      }
    };
    module.exports = KanjiData;
  }
});

// node_modules/dijkstrajs/dijkstra.js
var require_dijkstra = __commonJS({
  "node_modules/dijkstrajs/dijkstra.js"(exports, module) {
    "use strict";
    init_checked_fetch();
    init_modules_watch_stub();
    var dijkstra = {
      single_source_shortest_paths: function(graph, s, d) {
        var predecessors = {};
        var costs = {};
        costs[s] = 0;
        var open = dijkstra.PriorityQueue.make();
        open.push(s, 0);
        var closest, u, v, cost_of_s_to_u, adjacent_nodes, cost_of_e, cost_of_s_to_u_plus_cost_of_e, cost_of_s_to_v, first_visit;
        while (!open.empty()) {
          closest = open.pop();
          u = closest.value;
          cost_of_s_to_u = closest.cost;
          adjacent_nodes = graph[u] || {};
          for (v in adjacent_nodes) {
            if (adjacent_nodes.hasOwnProperty(v)) {
              cost_of_e = adjacent_nodes[v];
              cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;
              cost_of_s_to_v = costs[v];
              first_visit = typeof costs[v] === "undefined";
              if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {
                costs[v] = cost_of_s_to_u_plus_cost_of_e;
                open.push(v, cost_of_s_to_u_plus_cost_of_e);
                predecessors[v] = u;
              }
            }
          }
        }
        if (typeof d !== "undefined" && typeof costs[d] === "undefined") {
          var msg = ["Could not find a path from ", s, " to ", d, "."].join("");
          throw new Error(msg);
        }
        return predecessors;
      },
      extract_shortest_path_from_predecessor_list: function(predecessors, d) {
        var nodes = [];
        var u = d;
        var predecessor;
        while (u) {
          nodes.push(u);
          predecessor = predecessors[u];
          u = predecessors[u];
        }
        nodes.reverse();
        return nodes;
      },
      find_path: function(graph, s, d) {
        var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);
        return dijkstra.extract_shortest_path_from_predecessor_list(
          predecessors,
          d
        );
      },
      /**
       * A very naive priority queue implementation.
       */
      PriorityQueue: {
        make: function(opts) {
          var T = dijkstra.PriorityQueue, t = {}, key;
          opts = opts || {};
          for (key in T) {
            if (T.hasOwnProperty(key)) {
              t[key] = T[key];
            }
          }
          t.queue = [];
          t.sorter = opts.sorter || T.default_sorter;
          return t;
        },
        default_sorter: function(a, b) {
          return a.cost - b.cost;
        },
        /**
         * Add a new item to the queue and ensure the highest priority element
         * is at the front of the queue.
         */
        push: function(value, cost) {
          var item = { value, cost };
          this.queue.push(item);
          this.queue.sort(this.sorter);
        },
        /**
         * Return the highest priority element in the queue.
         */
        pop: function() {
          return this.queue.shift();
        },
        empty: function() {
          return this.queue.length === 0;
        }
      }
    };
    if (typeof module !== "undefined") {
      module.exports = dijkstra;
    }
  }
});

// node_modules/qrcode/lib/core/segments.js
var require_segments = __commonJS({
  "node_modules/qrcode/lib/core/segments.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Mode = require_mode();
    var NumericData = require_numeric_data();
    var AlphanumericData = require_alphanumeric_data();
    var ByteData = require_byte_data();
    var KanjiData = require_kanji_data();
    var Regex = require_regex();
    var Utils = require_utils();
    var dijkstra = require_dijkstra();
    function getStringByteLength(str) {
      return unescape(encodeURIComponent(str)).length;
    }
    __name(getStringByteLength, "getStringByteLength");
    function getSegments(regex, mode, str) {
      const segments = [];
      let result;
      while ((result = regex.exec(str)) !== null) {
        segments.push({
          data: result[0],
          index: result.index,
          mode,
          length: result[0].length
        });
      }
      return segments;
    }
    __name(getSegments, "getSegments");
    function getSegmentsFromString(dataStr) {
      const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr);
      const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr);
      let byteSegs;
      let kanjiSegs;
      if (Utils.isKanjiModeEnabled()) {
        byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr);
        kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr);
      } else {
        byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr);
        kanjiSegs = [];
      }
      const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs);
      return segs.sort(function(s1, s2) {
        return s1.index - s2.index;
      }).map(function(obj) {
        return {
          data: obj.data,
          mode: obj.mode,
          length: obj.length
        };
      });
    }
    __name(getSegmentsFromString, "getSegmentsFromString");
    function getSegmentBitsLength(length, mode) {
      switch (mode) {
        case Mode.NUMERIC:
          return NumericData.getBitsLength(length);
        case Mode.ALPHANUMERIC:
          return AlphanumericData.getBitsLength(length);
        case Mode.KANJI:
          return KanjiData.getBitsLength(length);
        case Mode.BYTE:
          return ByteData.getBitsLength(length);
      }
    }
    __name(getSegmentBitsLength, "getSegmentBitsLength");
    function mergeSegments(segs) {
      return segs.reduce(function(acc, curr) {
        const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null;
        if (prevSeg && prevSeg.mode === curr.mode) {
          acc[acc.length - 1].data += curr.data;
          return acc;
        }
        acc.push(curr);
        return acc;
      }, []);
    }
    __name(mergeSegments, "mergeSegments");
    function buildNodes(segs) {
      const nodes = [];
      for (let i = 0; i < segs.length; i++) {
        const seg = segs[i];
        switch (seg.mode) {
          case Mode.NUMERIC:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },
              { data: seg.data, mode: Mode.BYTE, length: seg.length }
            ]);
            break;
          case Mode.ALPHANUMERIC:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.BYTE, length: seg.length }
            ]);
            break;
          case Mode.KANJI:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }
            ]);
            break;
          case Mode.BYTE:
            nodes.push([
              { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }
            ]);
        }
      }
      return nodes;
    }
    __name(buildNodes, "buildNodes");
    function buildGraph(nodes, version) {
      const table = {};
      const graph = { start: {} };
      let prevNodeIds = ["start"];
      for (let i = 0; i < nodes.length; i++) {
        const nodeGroup = nodes[i];
        const currentNodeIds = [];
        for (let j = 0; j < nodeGroup.length; j++) {
          const node = nodeGroup[j];
          const key = "" + i + j;
          currentNodeIds.push(key);
          table[key] = { node, lastCount: 0 };
          graph[key] = {};
          for (let n = 0; n < prevNodeIds.length; n++) {
            const prevNodeId = prevNodeIds[n];
            if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {
              graph[prevNodeId][key] = getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) - getSegmentBitsLength(table[prevNodeId].lastCount, node.mode);
              table[prevNodeId].lastCount += node.length;
            } else {
              if (table[prevNodeId])
                table[prevNodeId].lastCount = node.length;
              graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) + 4 + Mode.getCharCountIndicator(node.mode, version);
            }
          }
        }
        prevNodeIds = currentNodeIds;
      }
      for (let n = 0; n < prevNodeIds.length; n++) {
        graph[prevNodeIds[n]].end = 0;
      }
      return { map: graph, table };
    }
    __name(buildGraph, "buildGraph");
    function buildSingleSegment(data, modesHint) {
      let mode;
      const bestMode = Mode.getBestModeForData(data);
      mode = Mode.from(modesHint, bestMode);
      if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {
        throw new Error('"' + data + '" cannot be encoded with mode ' + Mode.toString(mode) + ".\n Suggested mode is: " + Mode.toString(bestMode));
      }
      if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {
        mode = Mode.BYTE;
      }
      switch (mode) {
        case Mode.NUMERIC:
          return new NumericData(data);
        case Mode.ALPHANUMERIC:
          return new AlphanumericData(data);
        case Mode.KANJI:
          return new KanjiData(data);
        case Mode.BYTE:
          return new ByteData(data);
      }
    }
    __name(buildSingleSegment, "buildSingleSegment");
    exports.fromArray = /* @__PURE__ */ __name(function fromArray(array) {
      return array.reduce(function(acc, seg) {
        if (typeof seg === "string") {
          acc.push(buildSingleSegment(seg, null));
        } else if (seg.data) {
          acc.push(buildSingleSegment(seg.data, seg.mode));
        }
        return acc;
      }, []);
    }, "fromArray");
    exports.fromString = /* @__PURE__ */ __name(function fromString(data, version) {
      const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled());
      const nodes = buildNodes(segs);
      const graph = buildGraph(nodes, version);
      const path = dijkstra.find_path(graph.map, "start", "end");
      const optimizedSegs = [];
      for (let i = 1; i < path.length - 1; i++) {
        optimizedSegs.push(graph.table[path[i]].node);
      }
      return exports.fromArray(mergeSegments(optimizedSegs));
    }, "fromString");
    exports.rawSplit = /* @__PURE__ */ __name(function rawSplit(data) {
      return exports.fromArray(
        getSegmentsFromString(data, Utils.isKanjiModeEnabled())
      );
    }, "rawSplit");
  }
});

// node_modules/qrcode/lib/core/qrcode.js
var require_qrcode = __commonJS({
  "node_modules/qrcode/lib/core/qrcode.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Utils = require_utils();
    var ECLevel = require_error_correction_level();
    var BitBuffer = require_bit_buffer();
    var BitMatrix = require_bit_matrix();
    var AlignmentPattern = require_alignment_pattern();
    var FinderPattern = require_finder_pattern();
    var MaskPattern = require_mask_pattern();
    var ECCode = require_error_correction_code();
    var ReedSolomonEncoder = require_reed_solomon_encoder();
    var Version = require_version();
    var FormatInfo = require_format_info();
    var Mode = require_mode();
    var Segments = require_segments();
    function setupFinderPattern(matrix, version) {
      const size = matrix.size;
      const pos = FinderPattern.getPositions(version);
      for (let i = 0; i < pos.length; i++) {
        const row = pos[i][0];
        const col = pos[i][1];
        for (let r = -1; r <= 7; r++) {
          if (row + r <= -1 || size <= row + r)
            continue;
          for (let c = -1; c <= 7; c++) {
            if (col + c <= -1 || size <= col + c)
              continue;
            if (r >= 0 && r <= 6 && (c === 0 || c === 6) || c >= 0 && c <= 6 && (r === 0 || r === 6) || r >= 2 && r <= 4 && c >= 2 && c <= 4) {
              matrix.set(row + r, col + c, true, true);
            } else {
              matrix.set(row + r, col + c, false, true);
            }
          }
        }
      }
    }
    __name(setupFinderPattern, "setupFinderPattern");
    function setupTimingPattern(matrix) {
      const size = matrix.size;
      for (let r = 8; r < size - 8; r++) {
        const value = r % 2 === 0;
        matrix.set(r, 6, value, true);
        matrix.set(6, r, value, true);
      }
    }
    __name(setupTimingPattern, "setupTimingPattern");
    function setupAlignmentPattern(matrix, version) {
      const pos = AlignmentPattern.getPositions(version);
      for (let i = 0; i < pos.length; i++) {
        const row = pos[i][0];
        const col = pos[i][1];
        for (let r = -2; r <= 2; r++) {
          for (let c = -2; c <= 2; c++) {
            if (r === -2 || r === 2 || c === -2 || c === 2 || r === 0 && c === 0) {
              matrix.set(row + r, col + c, true, true);
            } else {
              matrix.set(row + r, col + c, false, true);
            }
          }
        }
      }
    }
    __name(setupAlignmentPattern, "setupAlignmentPattern");
    function setupVersionInfo(matrix, version) {
      const size = matrix.size;
      const bits = Version.getEncodedBits(version);
      let row, col, mod;
      for (let i = 0; i < 18; i++) {
        row = Math.floor(i / 3);
        col = i % 3 + size - 8 - 3;
        mod = (bits >> i & 1) === 1;
        matrix.set(row, col, mod, true);
        matrix.set(col, row, mod, true);
      }
    }
    __name(setupVersionInfo, "setupVersionInfo");
    function setupFormatInfo(matrix, errorCorrectionLevel, maskPattern) {
      const size = matrix.size;
      const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern);
      let i, mod;
      for (i = 0; i < 15; i++) {
        mod = (bits >> i & 1) === 1;
        if (i < 6) {
          matrix.set(i, 8, mod, true);
        } else if (i < 8) {
          matrix.set(i + 1, 8, mod, true);
        } else {
          matrix.set(size - 15 + i, 8, mod, true);
        }
        if (i < 8) {
          matrix.set(8, size - i - 1, mod, true);
        } else if (i < 9) {
          matrix.set(8, 15 - i - 1 + 1, mod, true);
        } else {
          matrix.set(8, 15 - i - 1, mod, true);
        }
      }
      matrix.set(size - 8, 8, 1, true);
    }
    __name(setupFormatInfo, "setupFormatInfo");
    function setupData(matrix, data) {
      const size = matrix.size;
      let inc = -1;
      let row = size - 1;
      let bitIndex = 7;
      let byteIndex = 0;
      for (let col = size - 1; col > 0; col -= 2) {
        if (col === 6)
          col--;
        while (true) {
          for (let c = 0; c < 2; c++) {
            if (!matrix.isReserved(row, col - c)) {
              let dark = false;
              if (byteIndex < data.length) {
                dark = (data[byteIndex] >>> bitIndex & 1) === 1;
              }
              matrix.set(row, col - c, dark);
              bitIndex--;
              if (bitIndex === -1) {
                byteIndex++;
                bitIndex = 7;
              }
            }
          }
          row += inc;
          if (row < 0 || size <= row) {
            row -= inc;
            inc = -inc;
            break;
          }
        }
      }
    }
    __name(setupData, "setupData");
    function createData(version, errorCorrectionLevel, segments) {
      const buffer = new BitBuffer();
      segments.forEach(function(data) {
        buffer.put(data.mode.bit, 4);
        buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version));
        data.write(buffer);
      });
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;
      if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {
        buffer.put(0, 4);
      }
      while (buffer.getLengthInBits() % 8 !== 0) {
        buffer.putBit(0);
      }
      const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8;
      for (let i = 0; i < remainingByte; i++) {
        buffer.put(i % 2 ? 17 : 236, 8);
      }
      return createCodewords(buffer, version, errorCorrectionLevel);
    }
    __name(createData, "createData");
    function createCodewords(bitBuffer, version, errorCorrectionLevel) {
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewords = totalCodewords - ecTotalCodewords;
      const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel);
      const blocksInGroup2 = totalCodewords % ecTotalBlocks;
      const blocksInGroup1 = ecTotalBlocks - blocksInGroup2;
      const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks);
      const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks);
      const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1;
      const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1;
      const rs = new ReedSolomonEncoder(ecCount);
      let offset = 0;
      const dcData = new Array(ecTotalBlocks);
      const ecData = new Array(ecTotalBlocks);
      let maxDataSize = 0;
      const buffer = new Uint8Array(bitBuffer.buffer);
      for (let b = 0; b < ecTotalBlocks; b++) {
        const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2;
        dcData[b] = buffer.slice(offset, offset + dataSize);
        ecData[b] = rs.encode(dcData[b]);
        offset += dataSize;
        maxDataSize = Math.max(maxDataSize, dataSize);
      }
      const data = new Uint8Array(totalCodewords);
      let index = 0;
      let i, r;
      for (i = 0; i < maxDataSize; i++) {
        for (r = 0; r < ecTotalBlocks; r++) {
          if (i < dcData[r].length) {
            data[index++] = dcData[r][i];
          }
        }
      }
      for (i = 0; i < ecCount; i++) {
        for (r = 0; r < ecTotalBlocks; r++) {
          data[index++] = ecData[r][i];
        }
      }
      return data;
    }
    __name(createCodewords, "createCodewords");
    function createSymbol(data, version, errorCorrectionLevel, maskPattern) {
      let segments;
      if (Array.isArray(data)) {
        segments = Segments.fromArray(data);
      } else if (typeof data === "string") {
        let estimatedVersion = version;
        if (!estimatedVersion) {
          const rawSegments = Segments.rawSplit(data);
          estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel);
        }
        segments = Segments.fromString(data, estimatedVersion || 40);
      } else {
        throw new Error("Invalid data");
      }
      const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel);
      if (!bestVersion) {
        throw new Error("The amount of data is too big to be stored in a QR Code");
      }
      if (!version) {
        version = bestVersion;
      } else if (version < bestVersion) {
        throw new Error(
          "\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: " + bestVersion + ".\n"
        );
      }
      const dataBits = createData(version, errorCorrectionLevel, segments);
      const moduleCount = Utils.getSymbolSize(version);
      const modules = new BitMatrix(moduleCount);
      setupFinderPattern(modules, version);
      setupTimingPattern(modules);
      setupAlignmentPattern(modules, version);
      setupFormatInfo(modules, errorCorrectionLevel, 0);
      if (version >= 7) {
        setupVersionInfo(modules, version);
      }
      setupData(modules, dataBits);
      if (isNaN(maskPattern)) {
        maskPattern = MaskPattern.getBestMask(
          modules,
          setupFormatInfo.bind(null, modules, errorCorrectionLevel)
        );
      }
      MaskPattern.applyMask(maskPattern, modules);
      setupFormatInfo(modules, errorCorrectionLevel, maskPattern);
      return {
        modules,
        version,
        errorCorrectionLevel,
        maskPattern,
        segments
      };
    }
    __name(createSymbol, "createSymbol");
    exports.create = /* @__PURE__ */ __name(function create(data, options) {
      if (typeof data === "undefined" || data === "") {
        throw new Error("No input text");
      }
      let errorCorrectionLevel = ECLevel.M;
      let version;
      let mask;
      if (typeof options !== "undefined") {
        errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M);
        version = Version.from(options.version);
        mask = MaskPattern.from(options.maskPattern);
        if (options.toSJISFunc) {
          Utils.setToSJISFunction(options.toSJISFunc);
        }
      }
      return createSymbol(data, version, errorCorrectionLevel, mask);
    }, "create");
  }
});

// node_modules/qrcode/lib/renderer/utils.js
var require_utils2 = __commonJS({
  "node_modules/qrcode/lib/renderer/utils.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    function hex2rgba(hex) {
      if (typeof hex === "number") {
        hex = hex.toString();
      }
      if (typeof hex !== "string") {
        throw new Error("Color should be defined as hex string");
      }
      let hexCode = hex.slice().replace("#", "").split("");
      if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {
        throw new Error("Invalid hex color: " + hex);
      }
      if (hexCode.length === 3 || hexCode.length === 4) {
        hexCode = Array.prototype.concat.apply([], hexCode.map(function(c) {
          return [c, c];
        }));
      }
      if (hexCode.length === 6)
        hexCode.push("F", "F");
      const hexValue = parseInt(hexCode.join(""), 16);
      return {
        r: hexValue >> 24 & 255,
        g: hexValue >> 16 & 255,
        b: hexValue >> 8 & 255,
        a: hexValue & 255,
        hex: "#" + hexCode.slice(0, 6).join("")
      };
    }
    __name(hex2rgba, "hex2rgba");
    exports.getOptions = /* @__PURE__ */ __name(function getOptions(options) {
      if (!options)
        options = {};
      if (!options.color)
        options.color = {};
      const margin = typeof options.margin === "undefined" || options.margin === null || options.margin < 0 ? 4 : options.margin;
      const width = options.width && options.width >= 21 ? options.width : void 0;
      const scale = options.scale || 4;
      return {
        width,
        scale: width ? 4 : scale,
        margin,
        color: {
          dark: hex2rgba(options.color.dark || "#000000ff"),
          light: hex2rgba(options.color.light || "#ffffffff")
        },
        type: options.type,
        rendererOpts: options.rendererOpts || {}
      };
    }, "getOptions");
    exports.getScale = /* @__PURE__ */ __name(function getScale(qrSize, opts) {
      return opts.width && opts.width >= qrSize + opts.margin * 2 ? opts.width / (qrSize + opts.margin * 2) : opts.scale;
    }, "getScale");
    exports.getImageWidth = /* @__PURE__ */ __name(function getImageWidth(qrSize, opts) {
      const scale = exports.getScale(qrSize, opts);
      return Math.floor((qrSize + opts.margin * 2) * scale);
    }, "getImageWidth");
    exports.qrToImageData = /* @__PURE__ */ __name(function qrToImageData(imgData, qr, opts) {
      const size = qr.modules.size;
      const data = qr.modules.data;
      const scale = exports.getScale(size, opts);
      const symbolSize = Math.floor((size + opts.margin * 2) * scale);
      const scaledMargin = opts.margin * scale;
      const palette = [opts.color.light, opts.color.dark];
      for (let i = 0; i < symbolSize; i++) {
        for (let j = 0; j < symbolSize; j++) {
          let posDst = (i * symbolSize + j) * 4;
          let pxColor = opts.color.light;
          if (i >= scaledMargin && j >= scaledMargin && i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {
            const iSrc = Math.floor((i - scaledMargin) / scale);
            const jSrc = Math.floor((j - scaledMargin) / scale);
            pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0];
          }
          imgData[posDst++] = pxColor.r;
          imgData[posDst++] = pxColor.g;
          imgData[posDst++] = pxColor.b;
          imgData[posDst] = pxColor.a;
        }
      }
    }, "qrToImageData");
  }
});

// node_modules/qrcode/lib/renderer/canvas.js
var require_canvas = __commonJS({
  "node_modules/qrcode/lib/renderer/canvas.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Utils = require_utils2();
    function clearCanvas(ctx, canvas, size) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      if (!canvas.style)
        canvas.style = {};
      canvas.height = size;
      canvas.width = size;
      canvas.style.height = size + "px";
      canvas.style.width = size + "px";
    }
    __name(clearCanvas, "clearCanvas");
    function getCanvasElement() {
      try {
        return document.createElement("canvas");
      } catch (e2) {
        throw new Error("You need to specify a canvas element");
      }
    }
    __name(getCanvasElement, "getCanvasElement");
    exports.render = /* @__PURE__ */ __name(function render(qrData, canvas, options) {
      let opts = options;
      let canvasEl = canvas;
      if (typeof opts === "undefined" && (!canvas || !canvas.getContext)) {
        opts = canvas;
        canvas = void 0;
      }
      if (!canvas) {
        canvasEl = getCanvasElement();
      }
      opts = Utils.getOptions(opts);
      const size = Utils.getImageWidth(qrData.modules.size, opts);
      const ctx = canvasEl.getContext("2d");
      const image = ctx.createImageData(size, size);
      Utils.qrToImageData(image.data, qrData, opts);
      clearCanvas(ctx, canvasEl, size);
      ctx.putImageData(image, 0, 0);
      return canvasEl;
    }, "render");
    exports.renderToDataURL = /* @__PURE__ */ __name(function renderToDataURL(qrData, canvas, options) {
      let opts = options;
      if (typeof opts === "undefined" && (!canvas || !canvas.getContext)) {
        opts = canvas;
        canvas = void 0;
      }
      if (!opts)
        opts = {};
      const canvasEl = exports.render(qrData, canvas, opts);
      const type = opts.type || "image/png";
      const rendererOpts = opts.rendererOpts || {};
      return canvasEl.toDataURL(type, rendererOpts.quality);
    }, "renderToDataURL");
  }
});

// node_modules/qrcode/lib/renderer/svg-tag.js
var require_svg_tag = __commonJS({
  "node_modules/qrcode/lib/renderer/svg-tag.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var Utils = require_utils2();
    function getColorAttrib(color, attrib) {
      const alpha = color.a / 255;
      const str = attrib + '="' + color.hex + '"';
      return alpha < 1 ? str + " " + attrib + '-opacity="' + alpha.toFixed(2).slice(1) + '"' : str;
    }
    __name(getColorAttrib, "getColorAttrib");
    function svgCmd(cmd, x, y) {
      let str = cmd + x;
      if (typeof y !== "undefined")
        str += " " + y;
      return str;
    }
    __name(svgCmd, "svgCmd");
    function qrToPath(data, size, margin) {
      let path = "";
      let moveBy = 0;
      let newRow = false;
      let lineLength = 0;
      for (let i = 0; i < data.length; i++) {
        const col = Math.floor(i % size);
        const row = Math.floor(i / size);
        if (!col && !newRow)
          newRow = true;
        if (data[i]) {
          lineLength++;
          if (!(i > 0 && col > 0 && data[i - 1])) {
            path += newRow ? svgCmd("M", col + margin, 0.5 + row + margin) : svgCmd("m", moveBy, 0);
            moveBy = 0;
            newRow = false;
          }
          if (!(col + 1 < size && data[i + 1])) {
            path += svgCmd("h", lineLength);
            lineLength = 0;
          }
        } else {
          moveBy++;
        }
      }
      return path;
    }
    __name(qrToPath, "qrToPath");
    exports.render = /* @__PURE__ */ __name(function render(qrData, options, cb) {
      const opts = Utils.getOptions(options);
      const size = qrData.modules.size;
      const data = qrData.modules.data;
      const qrcodesize = size + opts.margin * 2;
      const bg = !opts.color.light.a ? "" : "<path " + getColorAttrib(opts.color.light, "fill") + ' d="M0 0h' + qrcodesize + "v" + qrcodesize + 'H0z"/>';
      const path = "<path " + getColorAttrib(opts.color.dark, "stroke") + ' d="' + qrToPath(data, size, opts.margin) + '"/>';
      const viewBox = 'viewBox="0 0 ' + qrcodesize + " " + qrcodesize + '"';
      const width = !opts.width ? "" : 'width="' + opts.width + '" height="' + opts.width + '" ';
      const svgTag = '<svg xmlns="http://www.w3.org/2000/svg" ' + width + viewBox + ' shape-rendering="crispEdges">' + bg + path + "</svg>\n";
      if (typeof cb === "function") {
        cb(null, svgTag);
      }
      return svgTag;
    }, "render");
  }
});

// node_modules/qrcode/lib/browser.js
var require_browser = __commonJS({
  "node_modules/qrcode/lib/browser.js"(exports) {
    init_checked_fetch();
    init_modules_watch_stub();
    var canPromise = require_can_promise();
    var QRCode2 = require_qrcode();
    var CanvasRenderer = require_canvas();
    var SvgRenderer = require_svg_tag();
    function renderCanvas(renderFunc, canvas, text, opts, cb) {
      const args = [].slice.call(arguments, 1);
      const argsNum = args.length;
      const isLastArgCb = typeof args[argsNum - 1] === "function";
      if (!isLastArgCb && !canPromise()) {
        throw new Error("Callback required as last argument");
      }
      if (isLastArgCb) {
        if (argsNum < 2) {
          throw new Error("Too few arguments provided");
        }
        if (argsNum === 2) {
          cb = text;
          text = canvas;
          canvas = opts = void 0;
        } else if (argsNum === 3) {
          if (canvas.getContext && typeof cb === "undefined") {
            cb = opts;
            opts = void 0;
          } else {
            cb = opts;
            opts = text;
            text = canvas;
            canvas = void 0;
          }
        }
      } else {
        if (argsNum < 1) {
          throw new Error("Too few arguments provided");
        }
        if (argsNum === 1) {
          text = canvas;
          canvas = opts = void 0;
        } else if (argsNum === 2 && !canvas.getContext) {
          opts = text;
          text = canvas;
          canvas = void 0;
        }
        return new Promise(function(resolve, reject) {
          try {
            const data = QRCode2.create(text, opts);
            resolve(renderFunc(data, canvas, opts));
          } catch (e2) {
            reject(e2);
          }
        });
      }
      try {
        const data = QRCode2.create(text, opts);
        cb(null, renderFunc(data, canvas, opts));
      } catch (e2) {
        cb(e2);
      }
    }
    __name(renderCanvas, "renderCanvas");
    exports.create = QRCode2.create;
    exports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);
    exports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL);
    exports.toString = renderCanvas.bind(null, function(data, _, opts) {
      return SvgRenderer.render(data, opts);
    });
  }
});

// .wrangler/tmp/bundle-xQ5NsH/middleware-loader.entry.ts
init_checked_fetch();
init_modules_watch_stub();

// .wrangler/tmp/bundle-xQ5NsH/middleware-insertion-facade.js
init_checked_fetch();
init_modules_watch_stub();

// src/index.js
init_checked_fetch();
init_modules_watch_stub();

// node_modules/itty-router/dist/itty-router.mjs
init_checked_fetch();
init_modules_watch_stub();
var e = /* @__PURE__ */ __name(({ base: e2 = "", routes: r = [] } = {}) => ({ __proto__: new Proxy({}, { get: (a, o, t) => (a2, ...p) => r.push([o.toUpperCase(), RegExp(`^${(e2 + a2).replace(/(\/?)\*/g, "($1.*)?").replace(/(\/$)|((?<=\/)\/)/, "").replace(/(:(\w+)\+)/, "(?<$2>.*)").replace(/:(\w+)(\?)?(\.)?/g, "$2(?<$1>[^/]+)$2$3").replace(/\.(?=[\w(])/, "\\.").replace(/\)\.\?\(([^\[]+)\[\^/g, "?)\\.?($1(?<=\\.)[^\\.")}/*$`), p]) && t }), routes: r, async handle(e3, ...a) {
  let o, t, p = new URL(e3.url), l = e3.query = {};
  for (let [e4, r2] of p.searchParams)
    l[e4] = void 0 === l[e4] ? r2 : [l[e4], r2].flat();
  for (let [l2, s, c] of r)
    if ((l2 === e3.method || "ALL" === l2) && (t = p.pathname.match(s))) {
      e3.params = t.groups || {};
      for (let r2 of c)
        if (void 0 !== (o = await r2(e3.proxy || e3, ...a)))
          return o;
    }
} }), "e");

// src/routes/userRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/userController.js
init_checked_fetch();
init_modules_watch_stub();

// src/services/responseService.js
init_checked_fetch();
init_modules_watch_stub();
var ResponseService = class {
  formatSuccess(data, message = null) {
    return {
      success: true,
      data,
      message,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  formatError(message, statusCode = 500) {
    return {
      success: false,
      message,
      statusCode,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
};
__name(ResponseService, "ResponseService");

// src/services/userService.js
init_checked_fetch();
init_modules_watch_stub();

// src/services/emailService.js
init_checked_fetch();
init_modules_watch_stub();
var EmailService = class {
  constructor(env) {
    if (!env.ZEPTO_TOKEN) {
      console.error(
        "\u274C ZEPTO_TOKEN is not configured in environment variables"
      );
      throw new Error("Email service configuration missing");
    }
    if (!env.ZEPTO_FROM_ADDRESS) {
      console.error(
        "\u274C ZEPTO_FROM_ADDRESS is not configured in environment variables"
      );
      throw new Error("Email service configuration missing");
    }
    this.API_URL = "https://api.zeptomail.com/v1.1/email/template";
    this.token = env.ZEPTO_TOKEN;
    this.fromAddress = env.ZEPTO_FROM_ADDRESS.includes("@") ? env.ZEPTO_FROM_ADDRESS : `noreply@${env.ZEPTO_FROM_ADDRESS}`;
    this.newUserTemplateKey = "2d6f.15d6311547e5377.k1.27e302f0-a62e-11ef-914f-525400fa05f6.19342aa189f";
    this.existingUserTemplateKey = "2d6f.15d6311547e5377.k1.2b51baa0-d804-11ef-a765-525400033811.1948945034a";
    this.fromAddress = env.ZEPTO_FROM_ADDRESS.includes("@") ? env.ZEPTO_FROM_ADDRESS : `noreply@${env.ZEPTO_FROM_ADDRESS}`;
    this.newUserTemplateKey = "2d6f.15d6311547e5377.k1.27e302f0-a62e-11ef-914f-525400fa05f6.19342aa189f";
    this.existingUserTemplateKey = "2d6f.15d6311547e5377.k1.2b51baa0-d804-11ef-a765-525400033811.1948945034a";
  }
  async sendApiKeyEmail(email, username, apiKey, password, domain) {
    console.log("\u{1F4E7} Starting to send email to:", email);
    console.log("Debug - Password value:", password);
    console.log("Debug - All params:", {
      email,
      username,
      apiKey,
      password: password ? "***" : void 0,
      domain
    });
    if (!password) {
      console.log("\u2139\uFE0F Existing user - sending domain notification email");
    }
    try {
      const requestBody = {
        template_key: password ? this.newUserTemplateKey : this.existingUserTemplateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID"
        },
        to: [
          {
            email_address: {
              address: email,
              name: email.split("@")[0]
            }
          }
        ],
        merge_info: {
          API_KEY: apiKey,
          DOMAIN: domain,
          USER_EMAIL: email,
          ...password ? { TEMP_PASSWORD: password } : { DOMAIN_NOTE: "This is an additional domain for your account" },
          YEAR: (/* @__PURE__ */ new Date()).getFullYear(),
          AWP_APP: "Superuser.ID",
          DOCS_URL: "https://docs.superuser.id",
          API_DOCS: "https://superuser.id/portal",
          EMAIL_SUPPORT: "<EMAIL>"
        },
        track_clicks: false,
        track_opens: false
      };
      console.log("\u{1F4E4} Sending email with API key:", apiKey);
      const response = await fetch(this.API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: this.token
        },
        body: JSON.stringify(requestBody)
      });
      const responseData = await response.json();
      console.log(
        "\u{1F4E8} Email API Response:",
        JSON.stringify(responseData, null, 2)
      );
      if (!response.ok) {
        throw new Error(
          `Email API error: ${response.status} - ${JSON.stringify(
            responseData
          )}`
        );
      }
      return {
        success: true,
        messageId: responseData.message_id
      };
    } catch (error) {
      console.error("\u274C Error sending email:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
};
__name(EmailService, "EmailService");

// src/services/emailQueueService.js
init_checked_fetch();
init_modules_watch_stub();
var EmailQueueService = class {
  constructor(env) {
    this.env = env;
    this.emailService = new EmailService(env);
  }
  async addToQueue(emailData) {
    try {
      console.log("\u{1F680} Adding email to queue:", {
        email: emailData.email,
        type: emailData.type,
        domain: emailData.domain
      });
      const id = crypto.randomUUID();
      const queueItem = {
        id,
        email: emailData.email,
        username: emailData.username,
        domain: emailData.domain,
        password: emailData.password,
        apiKey: emailData.apiKey,
        type: emailData.type,
        status: "pending",
        attempts: 0,
        queuedAt: (/* @__PURE__ */ new Date()).toISOString(),
        processAfter: (/* @__PURE__ */ new Date()).toISOString()
      };
      console.log("Debug - Created queue item with data:", {
        ...queueItem,
        password: queueItem.password ? "***" : void 0
      });
      await this.env.USERS_KV.put(
        `email_queue:${id}`,
        JSON.stringify(queueItem)
      );
      await this.processQueueItem(queueItem);
      return id;
    } catch (error) {
      console.error("\u274C Error adding to queue:", error);
      throw error;
    }
  }
  async processQueueItem(queueItem) {
    console.log(`\u{1F4E7} Processing email ${queueItem.id} for ${queueItem.email}`);
    console.log("Debug - Queue item password:", queueItem.password);
    console.log("Debug - Full queue item:", queueItem);
    try {
      const result = await this.emailService.sendApiKeyEmail(
        queueItem.email,
        queueItem.username,
        queueItem.apiKey,
        queueItem.password,
        queueItem.domain
      );
      if (result.success) {
        console.log(`\u2705 Email sent successfully to ${queueItem.email}`);
        await Promise.all([
          this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),
          this.env.USERS_KV.put(
            `email_sent:${queueItem.id}`,
            JSON.stringify({
              ...queueItem,
              status: "sent",
              sentAt: (/* @__PURE__ */ new Date()).toISOString(),
              messageId: result.messageId
            })
          )
        ]);
        return result;
      } else {
        throw new Error(result.error || "Failed to send email");
      }
    } catch (error) {
      console.error(`\u274C Error processing email for ${queueItem.email}:`, error);
      const attempts = (queueItem.attempts || 0) + 1;
      if (attempts >= 3) {
        await this.moveToFailed(queueItem, error);
      } else {
        await this.scheduleRetry(queueItem, attempts, error);
      }
      throw error;
    }
  }
  async moveToFailed(queueItem, error) {
    await Promise.all([
      this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),
      this.env.USERS_KV.put(
        `email_failed:${queueItem.id}`,
        JSON.stringify({
          ...queueItem,
          status: "failed",
          error: error.message,
          failedAt: (/* @__PURE__ */ new Date()).toISOString()
        })
      )
    ]);
  }
  async scheduleRetry(queueItem, attempts, error) {
    const delayMinutes = Math.pow(2, attempts - 1);
    const processAfter = new Date(
      Date.now() + delayMinutes * 60 * 1e3
    ).toISOString();
    await this.env.USERS_KV.put(
      `email_queue:${queueItem.id}`,
      JSON.stringify({
        ...queueItem,
        attempts,
        processAfter,
        lastError: error.message,
        // Now error is defined
        lastAttempt: (/* @__PURE__ */ new Date()).toISOString()
      })
    );
    console.log(
      `\u23F3 Scheduled retry #${attempts} for ${queueItem.email} after ${delayMinutes} minutes`
    );
  }
  async getQueueStatus() {
    try {
      const [pending, sent, failed] = await Promise.all([
        this.env.USERS_KV.list({ prefix: "email_queue:" }),
        this.env.USERS_KV.list({ prefix: "email_sent:" }),
        this.env.USERS_KV.list({ prefix: "email_failed:" })
      ]);
      const status = {
        pending: pending.keys.length,
        sent: sent.keys.length,
        failed: failed.length,
        details: {
          pending: await this._getQueueItems("email_queue:"),
          sent: await this._getQueueItems("email_sent:"),
          failed: await this._getQueueItems("email_failed:")
        }
      };
      console.log("\u{1F4CA} Queue Status:", status);
      return status;
    } catch (error) {
      console.error("\u274C Error getting queue status:", error);
      throw error;
    }
  }
  async _getQueueItems(prefix) {
    const { keys } = await this.env.USERS_KV.list({ prefix });
    const items = await Promise.all(
      keys.map(async (key) => {
        const value = await this.env.USERS_KV.get(key.name, "json");
        return { key: key.name, ...value };
      })
    );
    return items;
  }
  async getDebugInfo() {
    const status = await this.getQueueStatus();
    return {
      queueStatus: status,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      environment: {
        hasZeptoToken: !!this.env.ZEPTO_TOKEN,
        hasFromAddress: !!this.env.ZEPTO_FROM_ADDRESS,
        zeptoToken: {
          exists: !!this.env.ZEPTO_TOKEN,
          length: this.env.ZEPTO_TOKEN?.length || 0,
          preview: this.env.ZEPTO_TOKEN ? `${this.env.ZEPTO_TOKEN.substring(0, 10)}...` : null
        },
        fromAddress: this.env.ZEPTO_FROM_ADDRESS || "not configured"
      }
    };
  }
};
__name(EmailQueueService, "EmailQueueService");

// src/services/userService.js
var UserService = class {
  constructor(env) {
    this.env = env;
    this.emailService = new EmailService(env);
    this.emailQueueService = new EmailQueueService(env);
  }
  _generatePassword() {
    try {
      const length = 12;
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const numbers = "0123456789";
      const special = "!@#$%^&*";
      let password = "";
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += special[Math.floor(Math.random() * special.length)];
      const allChars = lowercase + uppercase + numbers + special;
      for (let i = password.length; i < length; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      return password.split("").sort(() => 0.5 - Math.random()).join("");
    } catch (error) {
      console.error("Error generating password:", error);
      return `Pass${Math.random().toString(36).substring(2, 10)}!`;
    }
  }
  async createUser(userData) {
    try {
      if (!userData.domain || !userData.email) {
        throw new Error("Domain and email are required");
      }
      const normalizedEmail = userData.email.toLowerCase().trim();
      const normalizedDomain = userData.domain.toLowerCase().trim();
      const existingDomain = await this.env.USERS_KV.get(
        `domain:${normalizedDomain}`,
        "json"
      );
      if (existingDomain) {
        throw new Error(
          "Domain is already registered. Each domain can only have one API key."
        );
      }
      let user = await this.env.USERS_KV.get(
        `email:${normalizedEmail}`,
        "json"
      );
      const apiKey = crypto.randomUUID();
      let plainPassword;
      let hashedPassword;
      if (user) {
        const credentials2 = await this.env.USERS_KV.get(
          `credentials:${user.id}`,
          "json"
        );
        plainPassword = credentials2.plainPassword;
        hashedPassword = credentials2.hashedPassword;
        const newDomain = {
          domain: normalizedDomain,
          api_key: apiKey,
          status: "pending",
          createdAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        if (!user.domains) {
          user.domains = [];
        }
        user.domains.push(newDomain);
        await this.env.USERS_KV.put(`apikey:${apiKey}`, user.id);
      } else {
        plainPassword = this._generatePassword();
        hashedPassword = await this._hashPassword(plainPassword);
        user = {
          id: crypto.randomUUID(),
          email: normalizedEmail,
          password: hashedPassword,
          domains: [
            {
              domain: normalizedDomain,
              api_key: apiKey,
              status: "pending",
              createdAt: (/* @__PURE__ */ new Date()).toISOString()
            }
          ],
          createdAt: (/* @__PURE__ */ new Date()).toISOString(),
          updatedAt: (/* @__PURE__ */ new Date()).toISOString()
        };
        await this.env.USERS_KV.put(`apikey:${apiKey}`, user.id);
      }
      const credentials = {
        hashedPassword,
        plainPassword,
        userId: user.id,
        email: user.email,
        createdAt: user.createdAt
      };
      const tierData = {
        tier: "free",
        email: user.email,
        userId: user.id,
        createdAt: (/* @__PURE__ */ new Date()).toISOString(),
        updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
        additionalQuotas: {
          images: 0,
          content: 0,
          title: 0
        }
      };
      await Promise.all([
        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
        this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
        this.env.USERS_KV.put(
          `domain:${normalizedDomain}`,
          JSON.stringify({
            userId: user.id,
            email: user.email,
            domain: normalizedDomain,
            api_key: apiKey
          })
        ),
        this.env.USERS_KV.put(
          `api_key:${apiKey}`,
          JSON.stringify({
            userId: user.id,
            email: user.email,
            domain: normalizedDomain,
            api_key: apiKey
          })
        ),
        this.env.USERS_KV.put(
          `credentials:${user.id}`,
          JSON.stringify(credentials)
        ),
        this.env.USERS_KV.put(
          `t_setting:email_tier:${user.email}`,
          JSON.stringify(tierData)
        )
      ]);
      try {
        const emailQueueId = await this.emailQueueService.addToQueue({
          email: user.email,
          username: user.email.split("@")[0],
          domain: normalizedDomain,
          password: !user.domains || user.domains.length === 1 ? plainPassword : void 0,
          apiKey,
          type: user.domains && user.domains.length > 1 ? "new_domain" : "welcome"
        });
        console.log("\u{1F4EC} Email queued:", emailQueueId);
      } catch (error) {
        console.error("\u274C Error queueing email:", error);
      }
      return {
        id: user.id,
        email: user.email,
        domain: normalizedDomain,
        api_key: apiKey,
        credentials: {
          apiKey,
          // Only include password for first-time users
          ...(!user.domains || user.domains.length === 1) && {
            password: plainPassword
          }
        },
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };
    } catch (error) {
      console.error("Error in createUser:", error);
      throw error;
    }
  }
  async _hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hash = await crypto.subtle.digest("SHA-256", data);
    return Array.from(new Uint8Array(hash)).map((b) => b.toString(16).padStart(2, "0")).join("");
  }
  // Helper method to get plain password from credentials
  async _getPlainPasswordFromCredentials(credentials) {
    return credentials.plainPassword;
  }
  async getUserDetail(apiKey) {
    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("User not found");
    }
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    const domain = user.domains?.find((d) => d.api_key === apiKey);
    if (!domain) {
      throw new Error("User not found");
    }
    return {
      id: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: domain.api_key,
      status: domain.status,
      createdAt: domain.createdAt,
      updatedAt: user.updatedAt,
      tier: user.tier,
      quota: user.quota
    };
  }
  async verifyPassword(userId, password) {
    const credentials = await this.env.USERS_KV.get(
      `credentials:${userId}`,
      "json"
    );
    if (!credentials) {
      throw new Error("Credentials not found");
    }
    const hashedInput = await this._hashPassword(password);
    return hashedInput === credentials.hashedPassword;
  }
  async activateUser(license, domain) {
    try {
      const apiKeyData = await this.env.USERS_KV.get(
        `api_key:${license}`,
        "json"
      );
      if (!apiKeyData) {
        return false;
      }
      const normalizedDomain = domain.toLowerCase().trim();
      if (apiKeyData.domain !== normalizedDomain) {
        return false;
      }
      const user = await this.env.USERS_KV.get(
        `user:${apiKeyData.userId}`,
        "json"
      );
      if (!user) {
        return false;
      }
      const updatedDomains = user.domains.map((d) => {
        if (d.api_key === license && d.domain === normalizedDomain) {
          return {
            ...d,
            status: "active",
            activatedAt: (/* @__PURE__ */ new Date()).toISOString()
          };
        }
        return d;
      });
      const updatedUser = {
        ...user,
        domains: updatedDomains,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      const updatedApiKeyData = {
        ...apiKeyData,
        status: "active",
        activatedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      await Promise.all([
        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(updatedUser)),
        this.env.USERS_KV.put(
          `email:${user.email}`,
          JSON.stringify(updatedUser)
        ),
        this.env.USERS_KV.put(
          `api_key:${license}`,
          JSON.stringify(updatedApiKeyData)
        ),
        this.env.USERS_KV.put(
          `domain:${normalizedDomain}`,
          JSON.stringify(updatedApiKeyData)
        )
      ]);
      return true;
    } catch (error) {
      console.error("Error activating user:", error);
      return false;
    }
  }
  async getLicenseStatus(license) {
    try {
      const user = await this.env.USERS_KV.get(`api_key:${license}`, "json");
      if (!user) {
        return {
          isValid: false,
          message: "License not found"
        };
      }
      return {
        isValid: true,
        status: user.status || "pending",
        activatedAt: user.activatedAt || null,
        domain: user.domain,
        message: user.status === "active" ? "License is activated" : "License is pending activation"
      };
    } catch (error) {
      console.error("Error checking license status:", error);
      return {
        isValid: false,
        message: "Error checking license status"
      };
    }
  }
  _formatUserData(user) {
    const { password, ...userData } = user;
    return userData;
  }
  async loginUser(email, password) {
    const normalizedEmail = email.toLowerCase().trim();
    const user = await this.env.USERS_KV.get(
      `email:${normalizedEmail}`,
      "json"
    );
    if (!user) {
      throw new Error("Invalid email or password");
    }
    const credentials = await this.env.USERS_KV.get(
      `credentials:${user.id}`,
      "json"
    );
    if (!credentials) {
      throw new Error("Invalid email or password");
    }
    const hashedInput = await this._hashPassword(password);
    if (hashedInput !== credentials.hashedPassword) {
      throw new Error("Invalid email or password");
    }
    const tierInfo = await this.env.USERS_KV.get(
      `tier:${user.id}`,
      "json"
    ) || {
      currentTier: "free",
      tierName: "Free Tier",
      usage: 0,
      imagesUsage: 0,
      contentUsage: 0,
      titleUsage: 0,
      maxQuota: 100,
      imagesQuota: 50,
      contentQuota: 30,
      titleQuota: 20,
      remainingImagesQuota: 50,
      remainingContentQuota: 30,
      remainingTitleQuota: 20,
      price: 0,
      features: ["Basic API Access", "Standard Support"],
      quotaPercentage: "0%"
    };
    if (!tierInfo.remainingImagesQuota) {
      tierInfo.remainingImagesQuota = Math.max(
        0,
        tierInfo.imagesQuota - tierInfo.imagesUsage
      );
    }
    if (!tierInfo.remainingContentQuota) {
      tierInfo.remainingContentQuota = Math.max(
        0,
        tierInfo.contentQuota - tierInfo.contentUsage
      );
    }
    if (!tierInfo.remainingTitleQuota) {
      tierInfo.remainingTitleQuota = Math.max(
        0,
        tierInfo.titleQuota - tierInfo.titleUsage
      );
    }
    const totalUsage = tierInfo.usage || tierInfo.imagesUsage + tierInfo.contentUsage + tierInfo.titleUsage;
    const totalQuota = tierInfo.maxQuota || tierInfo.imagesQuota + tierInfo.contentQuota + tierInfo.titleQuota;
    tierInfo.quotaPercentage = totalQuota > 0 ? `${Math.round(totalUsage / totalQuota * 100)}%` : "0%";
    return {
      ...this._formatUserData(user),
      credentials: {
        password,
        // Include the original password in response
        apiKey: credentials.apiKey
      },
      tier: tierInfo
    };
  }
  async getDomains(email) {
    const normalizedEmail = email.toLowerCase().trim();
    const user = await this.env.USERS_KV.get(
      `email:${normalizedEmail}`,
      "json"
    );
    if (!user) {
      throw new Error("User not found");
    }
    if (user.domains && user.domains.length > 0) {
      await Promise.all(
        user.domains.map(async (domain) => {
          const apiKeyData = await this.env.USERS_KV.get(
            `api_key:${domain.api_key}`,
            "json"
          );
          if (!apiKeyData || !apiKeyData.userId || !apiKeyData.id) {
            const repairData = {
              id: user.id,
              userId: user.id,
              email: user.email,
              domain: domain.domain,
              api_key: domain.api_key,
              status: domain.status || "pending",
              createdAt: domain.createdAt,
              activatedAt: domain.activatedAt,
              password: user.password
            };
            await Promise.all([
              this.env.USERS_KV.put(
                `api_key:${domain.api_key}`,
                JSON.stringify(repairData)
              ),
              this.env.USERS_KV.put(
                `domain:${domain.domain}`,
                JSON.stringify(repairData)
              )
            ]);
            console.log(`Repaired API key data for ${domain.api_key}`);
          }
        })
      );
    }
    return user.domains || [];
  }
  async removeDomain(email, domain) {
    const user = await this.env.USERS_KV.get(`email:${email}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    const domainIndex = user.domains.findIndex((d) => d.domain === domain);
    if (domainIndex === -1) {
      throw new Error("Domain not found for this user");
    }
    const removedDomain = user.domains[domainIndex];
    user.domains.splice(domainIndex, 1);
    user.updatedAt = (/* @__PURE__ */ new Date()).toISOString();
    await Promise.all([
      this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
      this.env.USERS_KV.delete(`domain:${domain}`),
      this.env.USERS_KV.delete(`api_key:${removedDomain.api_key}`)
    ]);
    return true;
  }
};
__name(UserService, "UserService");

// src/services/apiKeyService.js
init_checked_fetch();
init_modules_watch_stub();
var ApiKeyService = class {
  constructor(env) {
    this.env = env;
  }
  async validateApiKey(request) {
    const apiKey = request.headers.get("x-sps-key");
    const requestDomain = request.headers.get("x-sps-domain");
    if (!apiKey) {
      throw new Error("API Key is required in x-sps-key header");
    }
    if (!requestDomain) {
      throw new Error("Domain is required in x-sps-domain header");
    }
    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("Invalid API Key");
    }
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    const domain = user.domains?.find((domain2) => domain2.api_key === apiKey);
    if (!domain) {
      throw new Error("Invalid API Key");
    }
    if (domain.domain !== requestDomain) {
      throw new Error(
        "Failed activate api key, use the correct registered domain !"
      );
    }
    return {
      userId: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: apiKey
    };
  }
};
__name(ApiKeyService, "ApiKeyService");

// src/services/validationService.js
init_checked_fetch();
init_modules_watch_stub();
var ValidationService = class {
  validateUserData(userData) {
    const errors = [];
    if (!userData.email)
      errors.push("Email is required");
    if (!userData.domain)
      errors.push("Domain is required");
    if (userData.email && !this._isValidEmail(userData.email)) {
      errors.push("Invalid email format");
    }
    if (userData.domain && !this._isValidDomain(userData.domain)) {
      errors.push("Invalid domain format");
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  _isValidDomain(domain) {
    const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }
};
__name(ValidationService, "ValidationService");

// src/controllers/userController.js
var UserController = class {
  constructor(env) {
    this.env = env;
    this.userService = new UserService(env);
    this.apiKeyService = new ApiKeyService();
    this.responseService = new ResponseService();
    this.validationService = new ValidationService();
  }
  async createUser(request) {
    try {
      const userData = await request.json();
      console.log("Received user data:", userData);
      const validation = this.validationService.validateUserData(userData);
      if (!validation.isValid) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(validation.errors.join(", "))
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }
      if (!userData.domain || !userData.email) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Domain and email are required")
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.createUser(userData);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            user,
            "User created successfully. Please check your email for credentials."
          )
        ),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error creating user:", error);
      if (error.message.includes("Domain is already registered")) {
        return new Response(
          JSON.stringify({
            success: false,
            message: error.message,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          }),
          {
            status: 200,
            // Keep 200 for domain registration error
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          status: 200,
          // Changed from 400/500 to 200
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async validateApiKey(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: "API Key is required in x-sps-key header"
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }
      try {
        await this.apiKeyService.validateApiKey(request, this.env);
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: true
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      } catch (error) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: error.message
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify(
          this.responseService.formatError("Server error occurred")
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async getUserDetail(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("API Key is required")
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.getUserDetail(apiKey);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async activate(request) {
    try {
      const data = await request.json();
      const { license, domain } = data;
      if (!license || !domain) {
        return new Response(
          JSON.stringify({
            status: false,
            message: "License key and domain are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const activated = await this.userService.activateUser(license, domain);
      if (!activated) {
        return new Response(
          JSON.stringify({
            status: false,
            message: "Failed activate api key"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const apiKeyData = await this.env.USERS_KV.get(
        `api_key:${license}`,
        "json"
      );
      return new Response(
        JSON.stringify({
          status: true,
          message: "Success activate api key",
          domain: apiKeyData.domain,
          email: apiKeyData.email
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error activating user:", error);
      return new Response(
        JSON.stringify({
          status: false,
          message: "Failed activate api key"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async checkLicenseStatus(request) {
    try {
      const data = await request.json();
      const { license } = data;
      if (!license) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "License key is required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const status = await this.userService.getLicenseStatus(license);
      return new Response(
        JSON.stringify({
          success: status.isValid,
          data: status
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error checking license status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to check license status"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async login(request) {
    try {
      const userData = await request.json();
      if (!userData.email || !userData.password) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Email and password are required")
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.loginUser(
        userData.email,
        userData.password
      );
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(user, "Login successful")
        ),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async getDomains(request) {
    try {
      const data = await request.json();
      const { email } = data;
      if (!email) {
        return new Response(
          JSON.stringify(this.responseService.formatError("Email is required")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      const domains = await this.userService.getDomains(email);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(domains)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async removeDomain(request) {
    try {
      const data = await request.json();
      const { email, domain } = data;
      if (!email || !domain) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Email and domain are required")
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      await this.userService.removeDomain(email, domain);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            null,
            "Domain removed successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }
};
__name(UserController, "UserController");

// src/controllers/debugController.js
init_checked_fetch();
init_modules_watch_stub();
var DebugController = class {
  constructor(env) {
    this.env = env;
  }
  async listAllKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          try {
            const value = await this.env.USERS_KV.get(key.name, "json");
            return {
              key: key.name,
              value
            };
          } catch (e2) {
            const textValue = await this.env.USERS_KV.get(key.name, "text");
            return {
              key: key.name,
              value: textValue,
              valueType: "text"
            };
          }
        })
      );
      const groupedData = data.reduce((acc, item) => {
        const prefix = item.key.split(":")[0];
        if (!acc[prefix]) {
          acc[prefix] = [];
        }
        acc[prefix].push(item);
        return acc;
      }, {});
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            summary: {
              totalKeys: list.keys.length,
              prefixes: Object.keys(groupedData),
              countByPrefix: Object.fromEntries(
                Object.entries(groupedData).map(([k, v]) => [k, v.length])
              )
            },
            grouped: groupedData
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getKVEntry(request) {
    try {
      const { key } = request.params;
      if (!key) {
        throw new Error("Key is required");
      }
      const value = await this.env.USERS_KV.get(key, "json");
      if (!value) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Key not found"
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      return new Response(
        JSON.stringify({
          success: true,
          data: value
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async deleteKVEntry(request) {
    try {
      const { key } = request.params;
      if (!key) {
        throw new Error("Key is required");
      }
      await this.env.USERS_KV.delete(key);
      return new Response(
        JSON.stringify({
          success: true,
          message: `Key ${key} deleted successfully`
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async searchKVEntries(request) {
    try {
      const url = new URL(request.url);
      const prefix = url.searchParams.get("prefix");
      const pattern = url.searchParams.get("pattern");
      if (!prefix && !pattern) {
        throw new Error("Either prefix or pattern is required");
      }
      const list = await this.env.USERS_KV.list({ prefix });
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value
          };
        })
      );
      let filteredData = data;
      if (pattern) {
        const regex = new RegExp(pattern, "i");
        filteredData = data.filter(
          (item) => regex.test(item.key) || regex.test(JSON.stringify(item.value))
        );
      }
      return new Response(
        JSON.stringify({
          success: true,
          data: filteredData
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUserTierData(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      const tierData = await this.env.USERS_KV.get(
        `${TierService.KEYS.USER_TIER}:${user.id}`,
        "json"
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            user,
            tierData
          }
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async truncateKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const deletedKeys = [];
      const skippedKeys = [];
      for (const key of list.keys) {
        if (key.name.startsWith("t_setting:tiers")) {
          skippedKeys.push(key.name);
          continue;
        }
        await this.env.USERS_KV.delete(key.name);
        deletedKeys.push(key.name);
      }
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            deletedCount: deletedKeys.length,
            skippedCount: skippedKeys.length,
            deletedKeys,
            skippedKeys
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async checkTruncateStatus(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value: key.name.startsWith("t_setting:tiers") ? "tier settings data..." : value
          };
        })
      );
      const hasTierSettings = data.some(
        (item) => item.key === "t_setting:tiers"
      );
      const hasOtherData = data.some(
        (item) => !item.key.startsWith("t_setting:tiers")
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            isClean: !hasOtherData && hasTierSettings,
            totalKeys: list.keys.length,
            hasTierSettings,
            hasOtherData,
            keys: data.map((item) => item.key)
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(DebugController, "DebugController");

// src/controllers/apiUsageController.js
init_checked_fetch();
init_modules_watch_stub();
var ApiUsageController = class {
  constructor(env) {
    this.env = env;
  }
  async trackUsage(request) {
    try {
      const headerApiKey = request.headers.get("x-sps-key");
      if (!headerApiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: "API Key is required in x-sps-key header"
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }
      const data = await request.json();
      const { apiKey, type, source, timestamp } = data;
      if (!apiKey || !type || !source || !timestamp) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Missing required fields: apiKey, type, source, timestamp"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const validTypes = ["images", "contents"];
      if (!validTypes.includes(type)) {
        return new Response(
          JSON.stringify({
            success: false,
            message: 'Invalid type. Must be either "images" or "contents"'
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const usageId = crypto.randomUUID();
      const usageData = {
        id: usageId,
        apiKey,
        type,
        source,
        timestamp,
        createdAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      await this.env.USERS_KV.put(
        `d_api_usage:${usageId}`,
        JSON.stringify(usageData)
      );
      await this.env.USERS_KV.put(
        `d_api_usage:${apiKey}:${usageId}`,
        JSON.stringify(usageData)
      );
      return new Response(
        JSON.stringify({
          success: true,
          message: "API usage tracked successfully",
          data: usageData
        }),
        {
          status: 201,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUserUsage(request) {
    try {
      const apiKey = request.headers.get("x-api-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API Key is required in x-sps-key header"
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const { keys } = await this.env.USERS_KV.list({
        prefix: `d_api_usage:${apiKey}:`
      });
      const usageData = await Promise.all(
        keys.map(async (key) => {
          const data = await this.env.USERS_KV.get(key.name, "json");
          return data;
        })
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: usageData
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(ApiUsageController, "ApiUsageController");

// src/routes/userRoutes.js
function createUserRouter(env) {
  const router2 = e({ base: "/api/users" });
  const userController = new UserController(env);
  const debugController = new DebugController(env);
  const apiUsageController = new ApiUsageController(env);
  router2.post(
    "/validatekey",
    (request) => userController.validateApiKey(request)
  );
  router2.post("/login", (request) => userController.login(request));
  router2.post("/activate", (request) => userController.activate(request));
  router2.post(
    "/license/status",
    (request) => userController.checkLicenseStatus(request)
  );
  router2.get("/userdetail", (request) => userController.getUserDetail(request));
  router2.post("/", (request) => userController.createUser(request));
  router2.post("/domains", (request) => userController.getDomains(request));
  router2.delete("/domains", (request) => userController.removeDomain(request));
  router2.post(
    "/usage/track",
    (request) => apiUsageController.trackUsage(request)
  );
  router2.get("/usage", (request) => apiUsageController.getUserUsage(request));
  router2.get("/debug/kv", (request) => debugController.listAllKVData(request));
  router2.post(
    "/debug/kv/delete",
    (request) => debugController.deleteKVData(request)
  );
  return router2;
}
__name(createUserRouter, "createUserRouter");

// src/routes/tierRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/tierController.js
init_checked_fetch();
init_modules_watch_stub();

// src/services/tierService.js
init_checked_fetch();
init_modules_watch_stub();
var _TierService = class {
  constructor(env) {
    this.env = env;
  }
  async getEmailFromUserId(userId) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    return user?.email;
  }
  async getUsersByEmail(email) {
    const emailUser = await this.env.USERS_KV.get(`email:${email}`, "json");
    if (!emailUser)
      return [];
    return emailUser.domains.map((d) => ({
      userId: emailUser.id,
      domain: d.domain,
      api_key: d.api_key
    }));
  }
  async getTypeQuotaUsage(email, type) {
    const quotaKey = `${_TierService.KEYS.QUOTA_USAGE}:${email}:${type}`;
    const usage = await this.env.USERS_KV.get(quotaKey, "json");
    return usage ? usage.count : 0;
  }
  async getDomainQuotaUsage(domain, type) {
    const quotaKey = `${_TierService.KEYS.QUOTA_USAGE}:domain:${domain}:${type}`;
    const usage = await this.env.USERS_KV.get(quotaKey, "json");
    return usage ? usage.count : 0;
  }
  async incrementDomainQuotaUsage(domain, type) {
    const quotaKey = `${_TierService.KEYS.QUOTA_USAGE}:domain:${domain}:${type}`;
    const currentUsage = await this.env.USERS_KV.get(quotaKey, "json") || {
      count: 0,
      lastReset: (/* @__PURE__ */ new Date()).toISOString(),
      resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toISOString()
    };
    currentUsage.count += 1;
    await this.env.USERS_KV.put(quotaKey, JSON.stringify(currentUsage));
    return currentUsage;
  }
  async incrementTypeQuotaUsage(userId, type, apiKey) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    const emailTier = await this.getEmailTier(user.email);
    const apiKeyData = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!apiKeyData) {
      throw new Error("API key data not found");
    }
    const domain = user.domains?.find((d) => d.api_key === apiKey);
    if (!domain) {
      throw new Error("Domain not found for user");
    }
    if (emailTier.tier === _TierService.TIERS.FREE) {
      const domainUsage = await this.getDomainQuotaUsage(domain.domain, type);
      const tierConfig = _TierService.DEFAULT_TIER_CONFIG[_TierService.TIERS.FREE];
      const quotaLimit = tierConfig[`${type}Quota`];
      if (domainUsage >= quotaLimit) {
        throw new Error(`Domain quota exceeded for ${type}`);
      }
      return this.incrementDomainQuotaUsage(domain.domain, type);
    } else {
      const quotaKey = `${_TierService.KEYS.QUOTA_USAGE}:${user.email}:${type}`;
      const currentUsage = await this.env.USERS_KV.get(quotaKey, "json") || {
        count: 0,
        lastReset: (/* @__PURE__ */ new Date()).toISOString(),
        resetDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1e3
        ).toISOString()
      };
      const tierConfig = _TierService.DEFAULT_TIER_CONFIG[emailTier.tier];
      const quotaLimit = tierConfig[`${type}Quota`];
      if (currentUsage.count >= quotaLimit) {
        throw new Error(`Email quota exceeded for ${type}`);
      }
      currentUsage.count += 1;
      await this.env.USERS_KV.put(quotaKey, JSON.stringify(currentUsage));
      return currentUsage;
    }
  }
  async getEmailTier(email) {
    const tier = await this.env.USERS_KV.get(
      `${_TierService.KEYS.EMAIL_TIER}:${email}`,
      "json"
    );
    return tier || {
      tier: _TierService.TIERS.FREE,
      addon1: false,
      addon2: false,
      startDate: (/* @__PURE__ */ new Date()).toISOString(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
      additionalQuotas: {
        images: 0,
        content: 0,
        title: 0
      }
    };
  }
  async upgradeEmailTier(email, newTier, options = {}) {
    const currentTier = await this.getEmailTier(email);
    const tierSettings = await this.getTierSettings();
    if (!Object.values(_TierService.TIERS).includes(newTier)) {
      throw new Error("Invalid tier specified");
    }
    if (newTier === _TierService.TIERS.FREE) {
      const currentEmailTier2 = await this.env.USERS_KV.get(
        `${_TierService.KEYS.EMAIL_TIER}:${email}`,
        "json"
      );
      const hasHadPaidTier = currentEmailTier2 && (currentEmailTier2.tier !== _TierService.TIERS.FREE || currentEmailTier2.history && currentEmailTier2.history.some(
        (h) => h.from !== _TierService.TIERS.FREE || h.to !== _TierService.TIERS.FREE
      ));
      if (hasHadPaidTier) {
        throw new Error(
          "Downgrading to free tier is not allowed. Free tier is only for new users."
        );
      }
    }
    const currentTierConfig = tierSettings.config[currentTier.tier];
    const newTierConfig = tierSettings.config[newTier];
    const [imagesUsage, contentUsage, titleUsage] = await Promise.all([
      this.getTypeQuotaUsage(email, "images"),
      this.getTypeQuotaUsage(email, "content"),
      this.getTypeQuotaUsage(email, "title")
    ]);
    const users = await this.getUsersByEmail(email);
    if (!users.length) {
      throw new Error("No users found for this email");
    }
    const currentStatus = await this.getUserTierStatus(
      users[0].userId,
      users[0].api_key
    );
    const currentEmailTier = await this.env.USERS_KV.get(
      `${_TierService.KEYS.EMAIL_TIER}:${email}`,
      "json"
    );
    const previousAdditionalQuotas = currentEmailTier?.additionalQuotas || {
      images: 0,
      content: 0,
      title: 0
    };
    const additionalQuotas = {
      images: previousAdditionalQuotas.images + newTierConfig.imagesQuota,
      content: previousAdditionalQuotas.content + newTierConfig.contentQuota,
      title: previousAdditionalQuotas.title + newTierConfig.titleQuota
    };
    const totalQuotas = {
      images: currentStatus.remainingImagesQuota + newTierConfig.imagesQuota,
      content: currentStatus.remainingContentQuota + newTierConfig.contentQuota,
      title: currentStatus.remainingTitleQuota + newTierConfig.titleQuota
    };
    const updatedTier = {
      tier: newTier,
      addon1: options.addon1 || false,
      addon2: options.addon2 || false,
      startDate: currentEmailTier?.startDate || (/* @__PURE__ */ new Date()).toISOString(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
      previousTier: currentTier.tier,
      additionalQuotas,
      // Store accumulated additional quotas
      history: [
        ...currentTier.history || [],
        {
          from: currentTier.tier,
          to: newTier,
          date: (/* @__PURE__ */ new Date()).toISOString(),
          previousAdditionalQuotas,
          newAdditionalQuotas: additionalQuotas,
          totalQuotas
        }
      ]
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.EMAIL_TIER}:${email}`,
      JSON.stringify(updatedTier)
    );
    await Promise.all(
      users.map(
        (user) => this.env.USERS_KV.put(
          `${_TierService.KEYS.USER_TIER}:${user.userId}`,
          JSON.stringify(updatedTier)
        )
      )
    );
    return {
      ...updatedTier,
      currentStatus: {
        tierName: newTierConfig.name,
        previousTier: currentTier.tier,
        maxQuota: Object.values(totalQuotas).reduce((a, b) => a + b, 0),
        quotas: {
          images: {
            base: newTierConfig.imagesQuota,
            additional: additionalQuotas.images,
            total: totalQuotas.images,
            used: imagesUsage,
            remaining: totalQuotas.images - imagesUsage
          },
          content: {
            base: newTierConfig.contentQuota,
            additional: additionalQuotas.content,
            total: totalQuotas.content,
            used: contentUsage,
            remaining: totalQuotas.content - contentUsage
          },
          title: {
            base: newTierConfig.titleQuota,
            additional: additionalQuotas.title,
            total: totalQuotas.title,
            used: titleUsage,
            remaining: totalQuotas.title - titleUsage
          }
        },
        price: newTierConfig.price,
        features: newTierConfig.features,
        addons: {
          addon1: {
            enabled: options.addon1 || false,
            price: newTierConfig.addon1_price,
            features: newTierConfig.addon1_detail
          },
          addon2: {
            enabled: options.addon2 || false,
            price: newTierConfig.addon2_price,
            features: newTierConfig.addon2_detail
          }
        }
      }
    };
  }
  async initializeTierSettings() {
    const defaultConfig = {
      free: {
        name: "Free Tier",
        maxQuota: 1e3,
        imagesQuota: 1e3,
        contentQuota: 1e3,
        titleQuota: 1e3,
        price: 0,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 4.99,
        addon2_price: 9.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing"
        ],
        addon2_detail: [
          "Premium support",
          "1-on-1 training",
          "Custom integration"
        ],
        features: ["Basic API access", "Community support"]
      },
      medium: {
        name: "Medium Tier",
        maxQuota: 1e4,
        imagesQuota: 1e4,
        contentQuota: 1e4,
        titleQuota: 1e4,
        price: 9.99,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 9.99,
        addon2_price: 19.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing"
        ],
        addon2_detail: [
          "Premium support",
          "Weekly training",
          "Custom integration",
          "API consultation"
        ],
        features: ["Increased quota", "Email support"]
      },
      high: {
        name: "High Tier",
        maxQuota: 1e6,
        imagesQuota: 1e6,
        contentQuota: 1e6,
        titleQuota: 1e6,
        price: 49.99,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 19.99,
        addon2_price: 39.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing",
          "Custom models"
        ],
        addon2_detail: [
          "Premium support",
          "Daily training",
          "Custom integration",
          "Dedicated manager",
          "24/7 phone support"
        ],
        features: ["Maximum quota", "Priority support", "24/7 phone support"]
      }
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify({
        config: defaultConfig,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
        version: "1.0"
      })
    );
    return await this.getTierSettings();
  }
  async getTierSettings() {
    return await this.env.USERS_KV.get(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      "json"
    );
  }
  async updateTierSettings(tierConfig) {
    const settings = {
      config: tierConfig,
      updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
      version: "1.0"
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify(settings)
    );
    return settings;
  }
  async getUserTier(userId) {
    try {
      const userTier = await this.env.USERS_KV.get(
        `${_TierService.KEYS.USER_TIER}:${userId}`,
        "json"
      );
      return userTier || {
        tier: _TierService.TIERS.FREE,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      console.error("Error getting user tier:", error);
      throw error;
    }
  }
  async setUserTier(userId, newTier, options = {}) {
    if (!Object.values(_TierService.TIERS).includes(newTier)) {
      throw new Error("Invalid tier specified");
    }
    const tierSettings = await this.getTierSettings();
    const newTierConfig = tierSettings.config[newTier];
    if (!newTierConfig) {
      throw new Error(`Configuration not found for tier: ${newTier}`);
    }
    const [imagesUsage, contentUsage] = await Promise.all([
      this.getTypeQuotaUsage(userId, "images"),
      this.getTypeQuotaUsage(userId, "content")
    ]);
    const startDate = /* @__PURE__ */ new Date();
    const expirationDate = new Date(startDate);
    expirationDate.setDate(startDate.getDate() + newTierConfig.expirationDays);
    const tierData = {
      tier: newTier,
      startDate: startDate.toISOString(),
      expirationDate: expirationDate.toISOString(),
      updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
      addon1: options.addon1 || false,
      addon2: options.addon2 || false,
      previousUsage: {
        images: imagesUsage || 0,
        content: contentUsage || 0
      }
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.USER_TIER}:${userId}`,
      JSON.stringify(tierData)
    );
    return {
      ...tierData,
      currentStatus: await this.getUserTierStatus(userId, null)
    };
  }
  async getUserQuotaUsage(userId) {
    const usage = await this.env.USERS_KV.get(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      "json"
    );
    return usage?.count || 0;
  }
  async incrementQuotaUsage(userId) {
    const currentUsage = await this.getUserQuotaUsage(userId);
    const userTier = await this.getUserTier(userId);
    const tierSettings = await this.getTierSettings();
    if (currentUsage >= tierSettings.config[userTier].maxQuota) {
      throw new Error("Quota exceeded for current tier");
    }
    const newUsage = {
      count: currentUsage + 1,
      lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
    };
    await this.env.USERS_KV.put(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}`,
      JSON.stringify(newUsage)
    );
    return newUsage.count;
  }
  async resetQuotaUsage(userId) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    const resetData = {
      count: 0,
      lastReset: (/* @__PURE__ */ new Date()).toISOString(),
      resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toISOString()
      // 30 days
    };
    await Promise.all([
      this.env.USERS_KV.put(
        `${_TierService.KEYS.QUOTA_USAGE}:${user.email}:images`,
        JSON.stringify(resetData)
      ),
      this.env.USERS_KV.put(
        `${_TierService.KEYS.QUOTA_USAGE}:${user.email}:content`,
        JSON.stringify(resetData)
      ),
      this.env.USERS_KV.put(
        `${_TierService.KEYS.QUOTA_USAGE}:${user.email}:title`,
        JSON.stringify(resetData)
      )
    ]);
    return {
      email: user.email,
      resetDate: resetData.resetDate,
      quotas: {
        images: 0,
        content: 0,
        title: 0
      }
    };
  }
  async getUserTierStatus(userId, apiKey) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user)
      throw new Error("User not found");
    const [emailTier, tierSettings] = await Promise.all([
      this.getEmailTier(user.email),
      this.getTierSettings()
    ]);
    const tierConfig = tierSettings.config[emailTier.tier];
    let imagesUsage, contentUsage, titleUsage;
    if (emailTier.tier === _TierService.TIERS.FREE) {
      const domain = user.domains?.find((d) => d.api_key === apiKey);
      if (!domain) {
        throw new Error("Domain not found for user");
      }
      [imagesUsage, contentUsage, titleUsage] = await Promise.all([
        this.getDomainQuotaUsage(domain.domain, "images"),
        this.getDomainQuotaUsage(domain.domain, "content"),
        this.getDomainQuotaUsage(domain.domain, "title")
      ]);
    } else {
      [imagesUsage, contentUsage, titleUsage] = await Promise.all([
        this.getTypeQuotaUsage(user.email, "images"),
        this.getTypeQuotaUsage(user.email, "content"),
        this.getTypeQuotaUsage(user.email, "title")
      ]);
    }
    const totalUsage = imagesUsage + contentUsage + titleUsage;
    const maxQuota = tierConfig.maxQuota + (emailTier.additionalQuotas?.images || 0) + (emailTier.additionalQuotas?.content || 0) + (emailTier.additionalQuotas?.title || 0);
    const remainingImagesQuota = tierConfig.imagesQuota + (emailTier.additionalQuotas?.images || 0) - imagesUsage;
    const remainingContentQuota = tierConfig.contentQuota + (emailTier.additionalQuotas?.content || 0) - contentUsage;
    const remainingTitleQuota = tierConfig.titleQuota + (emailTier.additionalQuotas?.title || 0) - titleUsage;
    return {
      currentTier: emailTier.tier,
      tierName: tierConfig.name,
      usage: totalUsage,
      imagesUsage,
      contentUsage,
      titleUsage,
      maxQuota,
      imagesQuota: tierConfig.imagesQuota + (emailTier.additionalQuotas?.images || 0),
      contentQuota: tierConfig.contentQuota + (emailTier.additionalQuotas?.content || 0),
      titleQuota: tierConfig.titleQuota + (emailTier.additionalQuotas?.title || 0),
      remainingImagesQuota,
      remainingContentQuota,
      remainingTitleQuota,
      price: tierConfig.price,
      features: tierConfig.features,
      quotaPercentage: `${Math.round(totalUsage / maxQuota * 100)}%`
    };
  }
  async setQuotaToZero(userId) {
    try {
      const usage = {
        count: Number.MAX_SAFE_INTEGER,
        // This will effectively make remaining = 0
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      };
      await Promise.all([
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(usage)
        ),
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(usage)
        )
      ]);
      const tierStatus = await this.getUserTierStatus(userId, null);
      const isQuotaZero = tierStatus.remainingImagesQuota === 0 && tierStatus.remainingContentQuota === 0;
      return {
        success: true,
        isQuotaZero,
        tierStatus: {
          remainingImages: tierStatus.remainingImagesQuota,
          remainingContent: tierStatus.remainingContentQuota,
          totalImages: tierStatus.imagesQuota,
          totalContent: tierStatus.contentQuota,
          currentTier: tierStatus.currentTier
        }
      };
    } catch (error) {
      console.error("Error setting quota to zero:", error);
      throw error;
    }
  }
  async checkAndResetQuota(userId) {
    const userTier = await this.getUserTier(userId);
    const tierStatus = await this.getUserTierStatus(userId, null);
    const tierSettings = await this.getTierSettings();
    const freeTierConfig = tierSettings.config[_TierService.TIERS.FREE];
    if (userTier.tier !== _TierService.TIERS.FREE) {
      if (tierStatus.remainingQuota <= 0) {
        await this.setUserTier(userId, _TierService.TIERS.FREE);
        const resetData = {
          count: 0,
          lastReset: (/* @__PURE__ */ new Date()).toISOString(),
          resetDate: new Date(
            Date.now() + freeTierConfig.expirationDays * 24 * 60 * 60 * 1e3
          ).toISOString()
        };
        await Promise.all([
          this.env.USERS_KV.put(
            `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
            JSON.stringify(resetData)
          ),
          this.env.USERS_KV.put(
            `${_TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
            JSON.stringify(resetData)
          ),
          this.env.USERS_KV.put(
            `${_TierService.KEYS.QUOTA_USAGE}:${userId}:title`,
            JSON.stringify(resetData)
          )
        ]);
        return {
          downgraded: true,
          newTier: _TierService.TIERS.FREE,
          quotaReset: true,
          nextResetDate: resetData.resetDate
        };
      }
      return {
        downgraded: false,
        currentTier: userTier.tier,
        remainingQuota: tierStatus.remainingQuota
      };
    }
    const quotaUsage = await this.env.USERS_KV.get(
      `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
      "json"
    );
    if (!quotaUsage || !quotaUsage.resetDate) {
      const resetData = {
        count: 0,
        lastReset: (/* @__PURE__ */ new Date()).toISOString(),
        resetDate: new Date(
          Date.now() + freeTierConfig.expirationDays * 24 * 60 * 60 * 1e3
        ).toISOString()
      };
      await Promise.all([
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:title`,
          JSON.stringify(resetData)
        )
      ]);
      return {
        quotaInitialized: true,
        nextResetDate: resetData.resetDate
      };
    }
    if (/* @__PURE__ */ new Date() >= new Date(quotaUsage.resetDate)) {
      const resetData = {
        count: 0,
        lastReset: (/* @__PURE__ */ new Date()).toISOString(),
        resetDate: new Date(
          Date.now() + freeTierConfig.expirationDays * 24 * 60 * 60 * 1e3
        ).toISOString()
      };
      await Promise.all([
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${_TierService.KEYS.QUOTA_USAGE}:${userId}:title`,
          JSON.stringify(resetData)
        )
      ]);
      return {
        quotaReset: true,
        nextResetDate: resetData.resetDate
      };
    }
    return {
      quotaReset: false,
      nextResetDate: quotaUsage.resetDate
    };
  }
};
var TierService2 = _TierService;
__name(TierService2, "TierService");
// KV key prefixes
__publicField(TierService2, "KEYS", {
  SETTINGS: "t_setting",
  USER_TIER: "t_setting:user_tier",
  QUOTA_USAGE: "t_setting:quota_usage",
  EMAIL_TIER: "t_setting:email_tier"
});
// Tier definitions
__publicField(TierService2, "TIERS", {
  FREE: "free",
  MEDIUM: "medium",
  HIGH: "high"
});
// Default tier configuration
__publicField(TierService2, "DEFAULT_TIER_CONFIG", {
  [_TierService.TIERS.FREE]: {
    name: "Free Tier",
    maxQuota: 1e3,
    imagesQuota: 1e3,
    contentQuota: 1e3,
    titleQuota: 1e3,
    price: 0,
    expirationDays: 30,
    addon1: false,
    addon2: false,
    addon1_price: 4.99,
    addon2_price: 9.99,
    addon1_detail: [
      "Enhanced resolution",
      "Advanced filters",
      "Batch processing"
    ],
    addon2_detail: [
      "Premium support",
      "1-on-1 training",
      "Custom integration"
    ],
    features: ["Basic API access", "Community support"]
  },
  [_TierService.TIERS.MEDIUM]: {
    name: "Medium Tier",
    maxQuota: 1e4,
    imagesQuota: 1e4,
    contentQuota: 1e4,
    titleQuota: 1e4,
    price: 9.99,
    expirationDays: 30,
    addon1: false,
    addon2: false,
    addon1_price: 9.99,
    addon2_price: 19.99,
    addon1_detail: [
      "Enhanced resolution",
      "Advanced filters",
      "Batch processing",
      "Priority processing"
    ],
    addon2_detail: [
      "Premium support",
      "Weekly training",
      "Custom integration",
      "API consultation"
    ],
    features: ["Increased quota", "Email support"]
  },
  [_TierService.TIERS.HIGH]: {
    name: "High Tier",
    maxQuota: 1e6,
    imagesQuota: 1e6,
    contentQuota: 1e6,
    titleQuota: 1e6,
    price: 49.99,
    expirationDays: 30,
    addon1: false,
    addon2: false,
    addon1_price: 19.99,
    addon2_price: 39.99,
    addon1_detail: [
      "Enhanced resolution",
      "Advanced filters",
      "Batch processing",
      "Priority processing",
      "Custom models"
    ],
    addon2_detail: [
      "Premium support",
      "Daily training",
      "Custom integration",
      "Dedicated manager",
      "24/7 phone support"
    ],
    features: ["Maximum quota", "Priority support", "24/7 phone support"]
  }
});

// src/controllers/tierController.js
var TierController = class {
  constructor(env) {
    this.env = env;
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
    this.apiKeyService = new ApiKeyService();
  }
  async _getUserFromApiKey(apiKey) {
    if (!apiKey) {
      throw new Error("API Key is required");
    }
    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("Invalid API Key");
    }
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    return user;
  }
  async getTierSettings(request) {
    try {
      const settings = await this.tierService.getTierSettings();
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(settings)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUserTierStatus(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const status = await this.tierService.getUserTierStatus(user.id, apiKey);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(status)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async upgradeTier(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API key is required in x-sps-key header"
          }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      const data = await request.json();
      const { tier, addon1, addon2 } = data;
      if (!tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Tier is required"
          }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }
      const apiKeyData = await this._getUserFromApiKey(apiKey);
      const result = await this.tierService.upgradeEmailTier(
        apiKeyData.email,
        tier,
        {
          addon1,
          addon2
        }
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            email: apiKeyData.email,
            tier: result.tier,
            previousTier: result.previousTier,
            updatedAt: result.updatedAt,
            quotas: result.currentStatus.quotas,
            addons: result.currentStatus.addons,
            features: result.currentStatus.features,
            price: result.currentStatus.price
          },
          message: `Successfully upgraded from ${result.previousTier} to ${tier} tier`
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error upgrading tier:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Error upgrading tier"
        }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }
  async getQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const tierStatus = await this.tierService.getUserTierStatus(user.id);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            email: user.email,
            currentTier: tierStatus.currentTier,
            tierName: tierStatus.tierName,
            usage: tierStatus.usage,
            quotas: tierStatus.quotas,
            quotaPercentage: tierStatus.quotaPercentage,
            price: tierStatus.price,
            features: tierStatus.features,
            addons: tierStatus.addons
          })
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async resetQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const resetData = await this.tierService.resetQuotaUsage(user.id);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            resetData,
            "Quota usage reset successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async setQuotaToZero(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const result = await this.tierService.setQuotaToZero(user.id);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(result)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async checkQuotaCron(request) {
    try {
      const userList = await this.env.USERS_KV.list({ prefix: "user:" });
      const processedEmails = /* @__PURE__ */ new Set();
      const results = [];
      for (const key of userList.keys) {
        const user = await this.env.USERS_KV.get(key.name, "json");
        if (user && user.email && !processedEmails.has(user.email)) {
          processedEmails.add(user.email);
          const emailUser = await this.env.USERS_KV.get(
            `email:${user.email}`,
            "json"
          );
          const emailTier = await this.tierService.getEmailTier(user.email);
          const tierStatus = await this.tierService.getUserTierStatus(user.id);
          const domains = emailUser?.domains || [];
          if (emailTier.tier !== TierService2.TIERS.FREE) {
            if (tierStatus.remainingImagesQuota <= 0 && tierStatus.remainingContentQuota <= 0) {
              const result = await this.tierService.upgradeEmailTier(
                user.email,
                TierService2.TIERS.FREE
              );
              results.push({
                email: user.email,
                action: "downgraded",
                fromTier: emailTier.tier,
                toTier: TierService2.TIERS.FREE,
                domains: domains.map((d) => d.domain)
              });
            }
          } else {
            const quotaUsage = await this.env.USERS_KV.get(
              `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:images`,
              "json"
            );
            if (!quotaUsage || !quotaUsage.resetDate) {
              const resetData = {
                count: 0,
                lastReset: (/* @__PURE__ */ new Date()).toISOString(),
                resetDate: new Date(
                  Date.now() + 30 * 24 * 60 * 60 * 1e3
                ).toISOString()
                // 30 days
              };
              await Promise.all([
                this.env.USERS_KV.put(
                  `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:images`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:content`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:title`,
                  JSON.stringify(resetData)
                )
              ]);
              results.push({
                email: user.email,
                action: "initialized",
                nextResetDate: resetData.resetDate,
                domains: domains.map((d) => d.domain)
              });
            } else if (/* @__PURE__ */ new Date() >= new Date(quotaUsage.resetDate)) {
              const resetData = {
                count: 0,
                lastReset: (/* @__PURE__ */ new Date()).toISOString(),
                resetDate: new Date(
                  Date.now() + 30 * 24 * 60 * 60 * 1e3
                ).toISOString()
                // 30 days
              };
              await Promise.all([
                this.env.USERS_KV.put(
                  `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:images`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:content`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService2.KEYS.QUOTA_USAGE}:${user.email}:title`,
                  JSON.stringify(resetData)
                )
              ]);
              results.push({
                email: user.email,
                action: "reset",
                nextResetDate: resetData.resetDate,
                domains: domains.map((d) => d.domain)
              });
            }
          }
        }
      }
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            { processed: results.length, results },
            "Quota check completed"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error in quota check cron:", error);
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(TierController, "TierController");

// src/routes/tierRoutes.js
function createTierRouter(env) {
  const router2 = e({ base: "/api/tiers" });
  const tierController = new TierController(env);
  router2.get("/settings", (request) => tierController.getTierSettings(request));
  router2.get("/status", (request) => tierController.getUserTierStatus(request));
  router2.post("/upgrade", (request) => tierController.upgradeTier(request));
  router2.get("/quota", (request) => tierController.getQuotaUsage(request));
  router2.post(
    "/quota/reset",
    (request) => tierController.resetQuotaUsage(request)
  );
  router2.post(
    "/quota/zero",
    (request) => tierController.setQuotaToZero(request)
  );
  router2.post(
    "/internal/cron/check-quota",
    (request) => tierController.checkQuotaCron(request)
  );
  router2.post("/settings/force-update", async (request) => {
    const tierService = new TierService2(env);
    await env.USERS_KV.delete(`${TierService2.KEYS.SETTINGS}:tiers`);
    const settings = await tierService.initializeTierSettings();
    return new Response(JSON.stringify({ success: true, data: settings }), {
      headers: { "Content-Type": "application/json" }
    });
  });
  return router2;
}
__name(createTierRouter, "createTierRouter");

// src/routes/debugRoutes.js
init_checked_fetch();
init_modules_watch_stub();
function createDebugRouter(env) {
  const router2 = e({ base: "/api/debug" });
  const emailQueueService = new EmailQueueService(env);
  const controller = new DebugController(env);
  router2.post("/email-queue/cleanup", async () => {
    try {
      const results = await emailQueueService.cleanupQueue();
      const newStatus = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          message: "Queue cleanup completed",
          results,
          currentStatus: newStatus,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  router2.get("/email-queue", async () => {
    try {
      const status = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          data: status,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  router2.get("/cron-debug", async (request) => {
    const emailQueueService2 = new EmailQueueService(env);
    const debugInfo = await emailQueueService2.getDebugInfo();
    return new Response(
      JSON.stringify({
        success: true,
        data: debugInfo
      }),
      {
        headers: { "Content-Type": "application/json" }
      }
    );
  });
  router2.post("/process-queue", async () => {
    try {
      const results = await emailQueueService.processQueue();
      const newStatus = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          message: "Queue processing completed",
          results,
          currentStatus: newStatus,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  router2.delete("/email-queue", async () => {
    try {
      const clearedCount = await emailQueueService.clearQueue();
      return new Response(
        JSON.stringify({
          success: true,
          message: `Cleared ${clearedCount} items from queue`,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  router2.get("/email-queue/status", async (request, env2) => {
    try {
      const emailQueueService2 = new EmailQueueService(env2);
      const status = await emailQueueService2.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          data: status,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error getting queue status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  router2.get("/email-queue/debug", async (request, env2) => {
    try {
      const emailQueueService2 = new EmailQueueService(env2);
      const debugInfo = await emailQueueService2.getDebugInfo();
      return new Response(
        JSON.stringify({
          success: true,
          data: debugInfo,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  router2.get("/kv/list", (request) => controller.listAllKVData(request));
  router2.get("/kv/:key", (request) => controller.getKVEntry(request));
  router2.delete("/kv/:key", (request) => controller.deleteKVEntry(request));
  router2.get("/kv/search", (request) => controller.searchKVEntries(request));
  router2.post("/kv/truncate", (request) => controller.truncateKVData(request));
  router2.get(
    "/kv/truncate/status",
    (request) => controller.checkTruncateStatus(request)
  );
  return router2;
}
__name(createDebugRouter, "createDebugRouter");

// src/routes/subscriptionRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/subscriptionController.js
init_checked_fetch();
init_modules_watch_stub();

// src/services/paypalService.js
init_checked_fetch();
init_modules_watch_stub();
var PayPalService = class {
  constructor(env) {
    this.env = env;
    this.clientId = env.PAYPAL_CLIENT_ID;
    this.clientSecret = env.PAYPAL_CLIENT_SECRET;
    this.baseURL = env.PAYPAL_SANDBOX === "true" ? "https://api-m.sandbox.paypal.com" : "https://api-m.paypal.com";
    this.tierService = new TierService2(env);
  }
  async createProduct(tierName) {
    try {
      const accessToken = await this.getAccessToken();
      const productData = {
        name: `${tierName} Subscription`,
        description: `Access to ${tierName} tier features`,
        type: "SERVICE",
        category: "SOFTWARE"
      };
      const response = await fetch(`${this.baseURL}/v1/catalogs/products`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `prod_${Date.now()}`
        },
        body: JSON.stringify(productData)
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create product");
      }
      return data.id;
    } catch (error) {
      console.error("Error creating PayPal product:", error);
      throw error;
    }
  }
  async createSubscription(userId, tier, addons = { addon1: false, addon2: false }) {
    try {
      console.log(
        "Creating subscription for tier:",
        tier,
        "with addons:",
        addons
      );
      const tierSettings = await this.tierService.getTierSettings();
      console.log("Tier settings:", tierSettings);
      if (!tierSettings?.config?.[tier]) {
        throw new Error(`Invalid tier: ${tier}`);
      }
      const tierConfig = tierSettings.config[tier];
      let basePrice = tierConfig.price;
      let totalPrice = basePrice;
      if (addons.addon1) {
        totalPrice += tierConfig.addon1_price;
      }
      if (addons.addon2) {
        totalPrice += tierConfig.addon2_price;
      }
      const productName = `${tierConfig.name}${addons.addon1 ? " + Addon 1" : ""}${addons.addon2 ? " + Addon 2" : ""}`;
      const productDescription = this._generateProductDescription(
        tierConfig,
        addons
      );
      const productId = await this.createProduct(
        productName,
        productDescription
      );
      const planData = {
        name: `${productName} Monthly Subscription`,
        product_id: productId,
        billing_cycles: [
          {
            frequency: {
              interval_unit: "MONTH",
              interval_count: 1
            },
            tenure_type: "REGULAR",
            sequence: 1,
            total_cycles: 0,
            pricing_scheme: {
              fixed_price: {
                value: totalPrice.toFixed(2),
                currency_code: "USD"
              }
            }
          }
        ],
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee_failure_action: "CONTINUE",
          payment_failure_threshold: 3
        }
      };
      const plan = await this._createPlan(planData);
      const subscription = await this._createSubscriptionWithPlan(
        plan.id,
        userId
      );
      const subscriptionRecord = {
        subscriptionId: subscription.id,
        planId: plan.id,
        productId,
        userId,
        tier,
        basePrice,
        addons: {
          addon1: addons.addon1 ? {
            enabled: true,
            price: tierConfig.addon1_price,
            features: tierConfig.addon1_detail
          } : false,
          addon2: addons.addon2 ? {
            enabled: true,
            price: tierConfig.addon2_price,
            features: tierConfig.addon2_detail
          } : false
        },
        totalPrice,
        status: subscription.status,
        createdAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      const approvalUrl = new URL(
        subscription.links.find((link) => link.rel === "approve")?.href
      );
      const baToken = approvalUrl.searchParams.get("ba_token");
      if (baToken) {
        await this.env.USERS_KV.put(
          `ba_token:${baToken}`,
          JSON.stringify({
            subscriptionId: subscription.id,
            userId,
            tier,
            addons,
            createdAt: (/* @__PURE__ */ new Date()).toISOString()
          }),
          { expirationTtl: 3600 }
          // Expire after 1 hour
        );
      }
      await this.env.USERS_KV.put(
        `subscription:${userId}`,
        JSON.stringify(subscriptionRecord)
      );
      return {
        ...subscriptionRecord,
        approvalUrl: subscription.links.find((link) => link.rel === "approve")?.href
      };
    } catch (error) {
      console.error("Error creating PayPal subscription:", error);
      throw error;
    }
  }
  async updateSubscriptionAddons(subscriptionId, addons) {
    try {
      const accessToken = await this.getAccessToken();
      const subscription = await this.getSubscriptionDetails(subscriptionId);
      const subscriptionData = await this.getSubscriptionData(subscriptionId);
      const tierConfig = await this.tierService.getTierSettings();
      const tierSettings = tierConfig.config[subscriptionData.tier];
      let newPrice = tierSettings.price;
      if (addons.addon1)
        newPrice += tierSettings.addon1_price;
      if (addons.addon2)
        newPrice += tierSettings.addon2_price;
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}/revise`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`
          },
          body: JSON.stringify({
            plan_id: subscription.plan_id,
            shipping_amount: {
              currency_code: "USD",
              value: newPrice.toFixed(2)
            }
          })
        }
      );
      if (!response.ok) {
        throw new Error("Failed to update subscription");
      }
      await this.updateSubscriptionRecord(subscriptionId, {
        addons,
        totalPrice: newPrice
      });
      return {
        success: true,
        subscriptionId,
        newPrice,
        addons
      };
    } catch (error) {
      console.error("Error updating subscription addons:", error);
      throw error;
    }
  }
  async getAccessToken() {
    try {
      const response = await fetch(`${this.baseURL}/v1/oauth2/token`, {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: `Basic ${btoa(
            `${this.clientId}:${this.clientSecret}`
          )}`,
          "Content-Type": "application/x-www-form-urlencoded"
        },
        body: "grant_type=client_credentials"
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error_description || "Failed to get access token");
      }
      return data.access_token;
    } catch (error) {
      console.error("PayPal access token error:", error);
      throw error;
    }
  }
  async getSubscriptionStatus(subscriptionId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to get subscription status");
      }
      const subscription = await response.json();
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:"
      });
      let localSubscription = null;
      let userId = null;
      for (const key of keys) {
        const record = await this.env.USERS_KV.get(key.name, "json");
        if (record && record.subscriptionId === subscriptionId) {
          localSubscription = record;
          userId = key.name.split(":")[1];
          break;
        }
      }
      return {
        subscriptionId: subscription.id,
        status: subscription.status,
        planId: subscription.plan_id,
        startTime: subscription.start_time,
        nextBillingTime: subscription.billing_info?.next_billing_time,
        lastPaymentTime: subscription.billing_info?.last_payment?.time,
        failedPayments: subscription.billing_info?.failed_payments_count || 0,
        tier: localSubscription?.tier,
        price: localSubscription?.price,
        localStatus: localSubscription?.status,
        createdAt: localSubscription?.createdAt,
        activatedAt: localSubscription?.activatedAt,
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      console.error("Error getting subscription status:", error);
      throw error;
    }
  }
  async verifyWebhookSignature(headers, body) {
    try {
      const accessToken = await this.getAccessToken();
      const verificationData = {
        auth_algo: headers.get("paypal-auth-algo"),
        cert_url: headers.get("paypal-cert-url"),
        transmission_id: headers.get("paypal-transmission-id"),
        transmission_sig: headers.get("paypal-transmission-sig"),
        transmission_time: headers.get("paypal-transmission-time"),
        webhook_id: this.env.PAYPAL_WEBHOOK_ID,
        webhook_event: body
      };
      const response = await fetch(
        `${this.baseURL}/v1/notifications/verify-webhook-signature`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`
          },
          body: JSON.stringify(verificationData)
        }
      );
      const data = await response.json();
      return data.verification_status === "SUCCESS";
    } catch (error) {
      console.error("Error verifying webhook signature:", error);
      return false;
    }
  }
  async activateSubscription(subscriptionId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      );
      if (!response.ok) {
        throw new Error("Failed to get subscription details from PayPal");
      }
      const subscription = await response.json();
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found in KV store");
      }
      const updatedSubscriptionData = {
        ...subscriptionData,
        status: subscription.status,
        startTime: subscription.start_time,
        nextBillingTime: subscription.billing_info?.next_billing_time,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      await this.env.USERS_KV.put(
        `subscription:${subscriptionId}`,
        JSON.stringify(updatedSubscriptionData)
      );
      return updatedSubscriptionData;
    } catch (error) {
      console.error("Error activating subscription:", error);
      throw error;
    }
  }
  async _createPlan(planData) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(`${this.baseURL}/v1/billing/plans`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `plan_${Date.now()}`
        },
        body: JSON.stringify(planData)
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create billing plan");
      }
      return data;
    } catch (error) {
      console.error("Error creating PayPal billing plan:", error);
      throw error;
    }
  }
  _generateProductDescription(tierConfig, addons) {
    let description = `${tierConfig.name} features:
`;
    description += tierConfig.features.join(", ") + "\n";
    if (addons.addon1) {
      description += "\nAddon 1 features:\n";
      description += tierConfig.addon1_detail.join(", ");
    }
    if (addons.addon2) {
      description += "\nAddon 2 features:\n";
      description += tierConfig.addon2_detail.join(", ");
    }
    return description;
  }
  async _createSubscriptionWithPlan(planId, userId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(`${this.baseURL}/v1/billing/subscriptions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `sub_${Date.now()}`
        },
        body: JSON.stringify({
          plan_id: planId,
          subscriber: {
            payer_id: userId
          },
          application_context: {
            return_url: `${this.env.APP_URL}/api/subscriptions/success`,
            cancel_url: `${this.env.APP_URL}/api/subscriptions/cancel`
          }
        })
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create subscription");
      }
      return data;
    } catch (error) {
      console.error("Error creating PayPal subscription:", error);
      throw error;
    }
  }
};
__name(PayPalService, "PayPalService");

// src/controllers/subscriptionController.js
var SubscriptionController = class {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
  }
  async createSubscription(request) {
    try {
      console.log("Starting subscription creation...");
      const headers = {};
      request.headers.forEach((value, key) => {
        headers[key] = value;
      });
      console.log("Request headers:", headers);
      const apiKey = request.headers.get("x-sps-key");
      console.log("API Key present:", !!apiKey);
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API key is required in x-sps-key header"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      console.log("User found:", !!user);
      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Invalid API key"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const tierSettings = await this.tierService.getTierSettings();
      console.log("Tier settings:", tierSettings);
      const body = await request.json();
      console.log("Request body:", body);
      const { tier, addons = { addon1: false, addon2: false } } = body;
      if (addons.addon1 !== void 0 && typeof addons.addon1 !== "boolean") {
        throw new Error("addon1 must be a boolean value");
      }
      if (addons.addon2 !== void 0 && typeof addons.addon2 !== "boolean") {
        throw new Error("addon2 must be a boolean value");
      }
      if (!tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Tier is required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      if (!tierSettings?.config?.[tier]) {
        return new Response(
          JSON.stringify({
            success: false,
            message: `Invalid tier: ${tier}. Available tiers: ${Object.keys(
              tierSettings?.config || {}
            ).join(", ")}`
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      console.log("PayPal config:", {
        sandboxMode: this.env.PAYPAL_SANDBOX === "true",
        hasClientId: !!this.env.PAYPAL_CLIENT_ID,
        hasClientSecret: !!this.env.PAYPAL_CLIENT_SECRET,
        appUrl: this.env.APP_URL
      });
      console.log("Creating PayPal subscription for tier:", tier);
      const subscription = await this.paypalService.createSubscription(
        user.id,
        tier,
        addons
      );
      console.log("Subscription created:", subscription);
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            subscriptionId: subscription.subscriptionId,
            approvalUrl: subscription.approvalUrl,
            tier: subscription.tier,
            basePrice: subscription.basePrice,
            addons: subscription.addons,
            totalPrice: subscription.totalPrice,
            status: subscription.status
          }
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Subscription creation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to create subscription",
          details: error.stack
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  // Add new endpoint to update addons
  async updateSubscriptionAddons(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const { subscriptionId, addons } = await request.json();
      if (!apiKey) {
        throw new Error("API key is required");
      }
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      if (!user) {
        throw new Error("Invalid API key");
      }
      const result = await this.paypalService.updateSubscriptionAddons(
        subscriptionId,
        addons
      );
      return new Response(
        JSON.stringify({
          success: true,
          data: result
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
    }
  }
  async getTestEndpoint() {
    try {
      return new Response(
        JSON.stringify({
          success: true,
          message: "Subscription router is working",
          env: {
            hasPayPalConfig: !!(this.env.PAYPAL_CLIENT_ID && this.env.PAYPAL_CLIENT_SECRET),
            sandbox: this.env.PAYPAL_SANDBOX === "true",
            appUrl: this.env.APP_URL
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async handleSubscriptionSuccess(subscriptionId) {
    try {
      console.log(
        "Controller handling subscription success for ID:",
        subscriptionId
      );
      if (!subscriptionId) {
        throw new Error("Subscription ID is required");
      }
      console.log("Calling PayPal service to activate subscription");
      const result = await this.paypalService.activateSubscription(
        subscriptionId
      );
      console.log("PayPal service activation result:", result);
      return result;
    } catch (error) {
      console.error(
        "Error handling subscription success in controller:",
        error
      );
      throw error;
    }
  }
};
__name(SubscriptionController, "SubscriptionController");

// src/routes/subscriptionRoutes.js
function createSubscriptionRouter(env) {
  const router2 = e({ base: "/api/subscriptions" });
  const controller = new SubscriptionController(env);
  const tierService = new TierService2(env);
  router2.post("/", (request) => controller.createSubscription(request));
  router2.get("/success", async (request) => {
    try {
      const url = new URL(request.url);
      const baToken = url.searchParams.get("ba_token");
      console.log("PayPal success callback URL:", request.url);
      console.log("BA Token:", baToken);
      if (!baToken) {
        throw new Error("Missing billing agreement token");
      }
      const subscriptionData = await env.USERS_KV.get(
        `ba_token:${baToken}`,
        "json"
      );
      console.log("Found subscription data for token:", subscriptionData);
      if (!subscriptionData) {
        throw new Error("No subscription found for this token");
      }
      await tierService.updateUserTier(subscriptionData.userId, {
        tier: subscriptionData.tier,
        subscriptionId: subscriptionData.subscriptionId,
        addons: subscriptionData.addons || { addon1: false, addon2: false },
        status: "active",
        startDate: (/* @__PURE__ */ new Date()).toISOString()
      });
      await env.USERS_KV.delete(`ba_token:${baToken}`);
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "success");
      redirectUrl.searchParams.set(
        "subscription_id",
        subscriptionData.subscriptionId
      );
      return Response.redirect(redirectUrl.toString(), 302);
    } catch (error) {
      console.error("Error handling subscription success:", error);
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/dashboard", frontendUrl);
      redirectUrl.searchParams.set("subscription_status", "error");
      redirectUrl.searchParams.set("error", error.message);
      return Response.redirect(redirectUrl.toString(), 302);
    }
  });
  router2.get("/cancel", (request) => {
    const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = new URL("/dashboard", frontendUrl);
    redirectUrl.searchParams.set("subscription_status", "cancelled");
    return Response.redirect(redirectUrl.toString(), 302);
  });
  router2.patch(
    "/:subscriptionId/addons",
    (request) => controller.updateSubscriptionAddons(request)
  );
  router2.get(
    "/:subscriptionId/status",
    (request) => controller.getSubscriptionStatus(request)
  );
  router2.all(
    "*",
    () => new Response(
      JSON.stringify({
        success: false,
        error: "Not Found"
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" }
      }
    )
  );
  return router2;
}
__name(createSubscriptionRouter, "createSubscriptionRouter");

// src/swagger/swagger.js
init_checked_fetch();
init_modules_watch_stub();
var swaggerDocument = {
  openapi: "3.0.0",
  info: {
    title: "API Documentation",
    version: "1.0.0"
  },
  paths: {
    "/api/users/": {
      post: {
        summary: "Registrasi Email dan Domain",
        description: "<b>Registrasi email dan domain</b>. <br><br> Password akan dikirimkan melalu email, dan Password hanya digenerate sekali saat email belum pernah terdaftar. Apabila email sudah pernah terdaftar, maka yang dikirimkan selanjutnya adalah ApiKey.  <br> <br> Domain yang ditambahkan akan di-check apakah domain tersebut sudah terdaftar di API Key yang sudah ada. Apabila domain tersebut sudah terdaftar, maka akan dikirimkan pesan error. <br> <br> Jika domain belum terdaftar, maka akan dikirimkan pesan sukses dan ApiKey.",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "domain"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "User email where credentials will be sent",
                    example: "<EMAIL>"
                  },
                  domain: {
                    type: "string",
                    description: "User domain (must be unique)",
                    example: "example.com"
                  }
                }
              }
            }
          }
        },
        responses: {
          201: {
            description: "User created successfully with generated credentials",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000"
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>"
                        },
                        domain: {
                          type: "string",
                          example: "example.com"
                        },
                        api_key: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000"
                        },
                        credentials: {
                          type: "object",
                          properties: {
                            password: {
                              type: "string",
                              description: "Auto-generated password",
                              example: "aB3$xK9#mP2&"
                            },
                            apiKey: {
                              type: "string",
                              description: "API key for authentication",
                              example: "550e8400-e29b-41d4-a716-446655440000"
                            }
                          }
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time"
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time"
                        }
                      }
                    },
                    message: {
                      type: "string",
                      example: "User created successfully. Please check your email for credentials."
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                },
                examples: {
                  success: {
                    value: {
                      success: true,
                      data: {
                        id: "550e8400-e29b-41d4-a716-446655440000",
                        email: "<EMAIL>",
                        domain: "example.com",
                        api_key: "550e8400-e29b-41d4-a716-446655440000",
                        credentials: {
                          password: "aB3$xK9#mP2&",
                          apiKey: "550e8400-e29b-41d4-a716-446655440000"
                        },
                        createdAt: "2024-01-17T07:44:18.662Z",
                        updatedAt: "2024-01-17T07:44:18.662Z"
                      },
                      message: "User created successfully. Please check your email for credentials.",
                      timestamp: "2024-01-17T07:44:19.010Z"
                    },
                    summary: "Successful user creation"
                  },
                  error: {
                    value: {
                      success: false,
                      message: "Domain is already registered. Each domain can only have one API key.",
                      timestamp: "2024-01-17T07:44:19.010Z"
                    },
                    summary: "Domain already registered"
                  }
                }
              }
            }
          },
          400: {
            description: "Domain already registered or validation error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "Domain is already registered. Each domain can only have one API key."
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/users/validatekey": {
      post: {
        summary: "Validasi API Key",
        description: "<b>Validasi API Key</b>. <br><br> Apabila API Key valid dan domain sesuai, maka akan dikirimkan pesan sukses. <br> <br> Apabila API Key tidak valid atau domain tidak sesuai, maka akan dikirimkan pesan error.",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: []
          }
        ],
        responses: {
          200: {
            description: "API key validation response",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      description: "Whether the operation was successful",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        isValid: {
                          type: "boolean",
                          description: "Whether the API key and domain are valid",
                          example: true
                        },
                        message: {
                          type: "string",
                          description: "Additional information about the validation result",
                          example: null
                        }
                      }
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      description: "Timestamp of the response",
                      example: "2024-12-07T12:00:00.000Z"
                    }
                  }
                },
                examples: {
                  valid: {
                    value: {
                      success: true,
                      data: {
                        isValid: true
                      },
                      timestamp: "2024-12-07T12:00:00.000Z"
                    },
                    summary: "Valid API Key and Domain"
                  },
                  invalid: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "Invalid API Key"
                      },
                      timestamp: "2024-12-07T12:00:00.000Z"
                    },
                    summary: "Invalid API Key"
                  },
                  missing_key: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "API Key is required in x-sps-key header"
                      },
                      timestamp: "2024-12-07T12:00:00.000Z"
                    },
                    summary: "Missing API Key"
                  },
                  missing_domain: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "Domain is required in x-sps-domain header"
                      },
                      timestamp: "2024-12-07T12:00:00.000Z"
                    },
                    summary: "Missing Domain"
                  },
                  domain_mismatch: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "Domain does not match the registered domain for this API key"
                      },
                      timestamp: "2024-12-07T12:00:00.000Z"
                    },
                    summary: "Domain Mismatch"
                  }
                }
              }
            }
          },
          500: {
            description: "Server error occurred",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Server error occurred"
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/users/userdetail": {
      get: {
        summary: "Detail User by API Key",
        description: "<b>Detail User by API Key</b>. <br><br> Menampilkan data detail user berdasarkan API Key.",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: []
          }
        ],
        responses: {
          200: {
            description: "User details retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UserResponse"
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/quota/zero": {
      post: {
        summary: "Set quota to zero",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Sets Title, Image, Content Kuota ke 0",
        responses: {
          200: {
            description: "Quota set to zero successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        isQuotaZero: {
                          type: "boolean",
                          description: "Confirms if quota is set to zero",
                          example: true
                        },
                        tierStatus: {
                          type: "object",
                          properties: {
                            remainingImages: {
                              type: "number",
                              description: "Remaining image quota",
                              example: 0
                            },
                            remainingContent: {
                              type: "number",
                              description: "Remaining content quota",
                              example: 0
                            },
                            totalImages: {
                              type: "number",
                              description: "Total image quota for tier",
                              example: 1e3
                            },
                            totalContent: {
                              type: "number",
                              description: "Total content quota for tier",
                              example: 1e3
                            },
                            currentTier: {
                              type: "string",
                              description: "Current tier name",
                              example: "free"
                            }
                          }
                        }
                      }
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-12-08T12:00:00.000Z"
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Error setting quota to zero",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Error setting quota to zero"
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-12-08T12:00:00.000Z"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/settings": {
      get: {
        summary: "Get tier settings",
        tags: ["Tiers"],
        description: "Retrieve all tier configurations including quotas and features",
        responses: {
          200: {
            description: "Tier settings retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        config: {
                          type: "object",
                          properties: {
                            free: { $ref: "#/components/schemas/TierConfig" },
                            medium: { $ref: "#/components/schemas/TierConfig" },
                            high: { $ref: "#/components/schemas/TierConfig" }
                          }
                        },
                        updatedAt: { type: "string", format: "date-time" },
                        version: { type: "string" }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/status": {
      get: {
        summary: "Get user tier status",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Get current user tier status including usage and quota information",
        responses: {
          200: {
            description: "User tier status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/TierStatusResponse"
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/upgrade": {
      post: {
        summary: "Upgrade user tier",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Upgrade user to a different tier with optional add-ons",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["free", "medium", "high"],
                    description: "The tier to upgrade to"
                  },
                  addon1: {
                    type: "boolean",
                    description: "Enable addon1 features",
                    default: false
                  },
                  addon2: {
                    type: "boolean",
                    description: "Enable addon2 features",
                    default: false
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Tier upgraded successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        email: { type: "string" },
                        tier: { type: "string" },
                        previousTier: { type: "string" },
                        updatedAt: { type: "string", format: "date-time" },
                        quotas: {
                          type: "object",
                          properties: {
                            images: {
                              type: "object",
                              properties: {
                                base: { type: "number" },
                                additional: { type: "number" },
                                total: { type: "number" },
                                used: { type: "number" },
                                remaining: { type: "number" }
                              }
                            },
                            content: {
                              type: "object",
                              properties: {
                                base: { type: "number" },
                                additional: { type: "number" },
                                total: { type: "number" },
                                used: { type: "number" },
                                remaining: { type: "number" }
                              }
                            },
                            title: {
                              type: "object",
                              properties: {
                                base: { type: "number" },
                                additional: { type: "number" },
                                total: { type: "number" },
                                used: { type: "number" },
                                remaining: { type: "number" }
                              }
                            }
                          }
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean" },
                                price: { type: "number" },
                                features: {
                                  type: "array",
                                  items: { type: "string" }
                                }
                              }
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean" },
                                price: { type: "number" },
                                features: {
                                  type: "array",
                                  items: { type: "string" }
                                }
                              }
                            }
                          }
                        },
                        features: {
                          type: "array",
                          items: { type: "string" }
                        },
                        price: { type: "number" }
                      }
                    },
                    message: { type: "string" }
                  }
                }
              }
            }
          },
          400: {
            description: "Bad request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/quota": {
      get: {
        summary: "Get quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Get current quota usage information",
        responses: {
          200: {
            description: "Quota usage retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        usage: { type: "number" }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tiers/quota/reset": {
      post: {
        summary: "Reset quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Reset the quota usage counter to zero",
        responses: {
          200: {
            description: "Quota reset successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        count: { type: "number" },
                        lastReset: { type: "string", format: "date-time" }
                      }
                    },
                    message: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/users/activate": {
      post: {
        tags: ["Users"],
        summary: "Aktivasi Domain by API Key",
        description: "Aktivasi License API Key untuk domain yang terdaftar.",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["license", "domain"],
                properties: {
                  license: {
                    type: "string",
                    example: "550e8400-e29b-41d4-a716-446655440000"
                  },
                  domain: {
                    type: "string",
                    example: "example.com"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Success",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "boolean",
                      example: true
                    },
                    message: {
                      type: "string",
                      example: "Success activate api key"
                    },
                    domain: {
                      type: "string",
                      example: "example.com"
                    },
                    email: {
                      type: "string",
                      example: "<EMAIL>"
                    }
                  }
                },
                examples: {
                  success: {
                    value: {
                      status: true,
                      message: "Success activate api key",
                      domain: "example.com",
                      email: "<EMAIL>"
                    },
                    summary: "Successful activation"
                  },
                  failed: {
                    value: {
                      status: false,
                      message: "Failed activate api key"
                    },
                    summary: "Failed activation"
                  }
                }
              }
            }
          },
          400: {
            description: "Invalid request or activation failed",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "License key and domain are required"
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "Failed activate api key"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/users/license/status": {
      post: {
        summary: "Check Status License API Key",
        description: "<b>Check Status License API Key</b>. <br><br> Melakukan Pengecekan Apakah API Key sudah aktif atau belum.",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["license"],
                properties: {
                  license: {
                    type: "string",
                    description: "License key to check"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "License status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean"
                    },
                    data: {
                      type: "object",
                      properties: {
                        isValid: {
                          type: "boolean"
                        },
                        status: {
                          type: "string",
                          enum: ["active", "pending"]
                        },
                        activatedAt: {
                          type: "string",
                          format: "date-time",
                          nullable: true
                        },
                        domain: {
                          type: "string"
                        },
                        message: {
                          type: "string"
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/usage/images": {
      post: {
        summary: "Track image usage",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: []
          }
        ],
        description: "Increment the image generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the image generation",
                    example: "profile-picture"
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the image was generated",
                    example: "2024-12-08T02:30:00.000Z"
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      size: "1024x1024",
                      model: "stable-diffusion"
                    }
                  }
                }
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "profile-picture",
                    timestamp: "2024-12-08T02:30:00.000Z"
                  }
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "product-image",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      size: "1024x1024",
                      model: "stable-diffusion",
                      style: "photorealistic"
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: { type: "object" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/usage/content": {
      post: {
        summary: "Track content usage",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: []
          }
        ],
        description: "Increment the content generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the content generation",
                    example: "product-description"
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the content was generated",
                    example: "2024-12-08T02:30:00.000Z"
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      wordCount: 500,
                      language: "en"
                    }
                  }
                }
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "product-description",
                    timestamp: "2024-12-08T02:30:00.000Z"
                  }
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "blog-post",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      wordCount: 1500,
                      language: "en",
                      category: "technology"
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: { type: "object" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/usage/stats": {
      get: {
        summary: "Get usage statistics",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: []
          }
        ],
        description: "Get detailed usage statistics for images, content, and title generation",
        responses: {
          200: {
            description: "Usage statistics retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        images: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 50 },
                            remaining: { type: "number", example: 950 },
                            total: { type: "number", example: 1e3 },
                            percentageUsed: { type: "string", example: "5.00" }
                          }
                        },
                        content: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 30 },
                            remaining: { type: "number", example: 970 },
                            total: { type: "number", example: 1e3 },
                            percentageUsed: { type: "string", example: "3.00" }
                          }
                        },
                        title: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 20 },
                            remaining: { type: "number", example: 980 },
                            total: { type: "number", example: 1e3 },
                            percentageUsed: { type: "string", example: "2.00" }
                          }
                        },
                        tier: {
                          type: "object",
                          properties: {
                            name: { type: "string", example: "Medium Tier" },
                            current: { type: "string", example: "medium" },
                            expirationDate: {
                              type: "string",
                              example: "2024-12-31T23:59:59Z"
                            },
                            isExpired: { type: "boolean", example: false },
                            price: { type: "number", example: 9.99 }
                          }
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean", example: false },
                                price: { type: "number", example: 4.99 },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                  example: [
                                    "Enhanced resolution",
                                    "Advanced filters"
                                  ]
                                }
                              }
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean", example: false },
                                price: { type: "number", example: 9.99 },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                  example: [
                                    "Premium support",
                                    "1-on-1 training"
                                  ]
                                }
                              }
                            }
                          }
                        },
                        features: {
                          type: "array",
                          items: { type: "string" },
                          example: ["Basic API access", "Community support"]
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/usage/history": {
      get: {
        summary: "Get usage history",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: []
          }
        ],
        description: "Retrieve detailed usage history with filtering and pagination options",
        parameters: [
          {
            name: "startDate",
            in: "query",
            description: "Filter results from this date (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-01"
            }
          },
          {
            name: "endDate",
            in: "query",
            description: "Filter results until this date (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-31"
            }
          },
          {
            name: "type",
            in: "query",
            description: "Filter by usage type",
            schema: {
              type: "string",
              enum: ["images", "content"]
            }
          },
          {
            name: "limit",
            in: "query",
            description: "Number of results per page",
            schema: {
              type: "integer",
              minimum: 1,
              maximum: 100,
              default: 100
            }
          },
          {
            name: "page",
            in: "query",
            description: "Page number",
            schema: {
              type: "integer",
              minimum: 1,
              default: 1
            }
          }
        ],
        responses: {
          200: {
            description: "Usage history retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        entries: {
                          type: "array",
                          items: {
                            $ref: "#/components/schemas/UsageHistoryEntry"
                          }
                        },
                        pagination: {
                          type: "object",
                          properties: {
                            total: { type: "integer", example: 50 },
                            totalPages: { type: "integer", example: 5 },
                            currentPage: { type: "integer", example: 1 },
                            limit: { type: "integer", example: 10 }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/usage/daily": {
      get: {
        summary: "Get daily usage aggregates",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: []
          }
        ],
        description: "Retrieve daily aggregated usage statistics",
        parameters: [
          {
            name: "startDate",
            in: "query",
            description: "Start date for daily stats (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-01"
            }
          },
          {
            name: "endDate",
            in: "query",
            description: "End date for daily stats (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-31"
            }
          }
        ],
        responses: {
          200: {
            description: "Daily usage statistics retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "array",
                      items: {
                        $ref: "#/components/schemas/DailyUsage"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/subscriptions": {
      post: {
        summary: "Create new PayPal subscription",
        description: "Creates a new PayPal subscription for the specified tier with automatic price fetching from tier settings",
        tags: ["Subscriptions"],
        security: [
          {
            ApiKeyAuth: []
          }
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["medium", "high"],
                    description: "The tier level to subscribe to. Price will be automatically fetched from tier settings."
                  }
                }
              },
              examples: {
                medium: {
                  summary: "Medium tier subscription",
                  value: {
                    tier: "medium"
                  }
                },
                high: {
                  summary: "High tier subscription",
                  value: {
                    tier: "high"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          description: "PayPal subscription identifier",
                          example: "I-BW452GLLEP1G"
                        },
                        approvalUrl: {
                          type: "string",
                          description: "PayPal URL for subscription approval",
                          example: "https://www.sandbox.paypal.com/webapps/billing/subscriptions?token=..."
                        },
                        tier: {
                          type: "string",
                          description: "Selected subscription tier",
                          example: "medium"
                        },
                        price: {
                          type: "number",
                          description: "Monthly subscription price in USD",
                          example: 9.99
                        },
                        status: {
                          type: "string",
                          description: "Initial subscription status",
                          enum: ["APPROVAL_PENDING", "APPROVED", "ACTIVE"],
                          example: "APPROVAL_PENDING"
                        }
                      }
                    }
                  }
                },
                examples: {
                  mediumTier: {
                    summary: "Medium tier response",
                    value: {
                      success: true,
                      data: {
                        subscriptionId: "I-BW452GLLEP1G",
                        approvalUrl: "https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=EC-5RT15012PY123456",
                        tier: "medium",
                        price: 9.99,
                        status: "APPROVAL_PENDING"
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Error creating subscription",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string"
                    }
                  }
                },
                examples: {
                  invalidTier: {
                    summary: "Invalid tier specified",
                    value: {
                      success: false,
                      message: "Invalid tier specified"
                    }
                  },
                  missingApiKey: {
                    summary: "Missing API key",
                    value: {
                      success: false,
                      message: "API key is required in x-sps-key header"
                    }
                  },
                  paypalError: {
                    summary: "PayPal API error",
                    value: {
                      success: false,
                      message: "Failed to create PayPal subscription"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/subscriptions/success": {
      get: {
        summary: "PayPal subscription success callback",
        description: "Endpoint called by PayPal after successful subscription approval",
        tags: ["Subscriptions"],
        parameters: [
          {
            name: "subscription_id",
            in: "query",
            required: true,
            schema: {
              type: "string"
            },
            description: "PayPal subscription identifier"
          }
        ],
        responses: {
          302: {
            description: "Redirects to frontend success page",
            headers: {
              Location: {
                schema: {
                  type: "string",
                  example: "https://your-app.com/subscription/success?subscription_id=I-BW452GLLEP1G"
                }
              }
            }
          }
        }
      }
    },
    "/api/subscriptions/webhook": {
      post: {
        summary: "PayPal webhook endpoint",
        description: "Handles PayPal subscription event notifications",
        tags: ["Subscriptions"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  event_type: {
                    type: "string",
                    description: "PayPal event type",
                    example: "BILLING.SUBSCRIPTION.ACTIVATED"
                  },
                  resource: {
                    type: "object",
                    description: "Event resource details"
                  }
                }
              },
              examples: {
                activated: {
                  summary: "Subscription activated",
                  value: {
                    event_type: "BILLING.SUBSCRIPTION.ACTIVATED",
                    resource: {
                      id: "I-BW452GLLEP1G",
                      status: "ACTIVE"
                    }
                  }
                },
                payment: {
                  summary: "Payment completed",
                  value: {
                    event_type: "PAYMENT.SALE.COMPLETED",
                    resource: {
                      id: "5RT15012PY123456",
                      amount: {
                        total: "9.99",
                        currency: "USD"
                      }
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Webhook processed successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    received: {
                      type: "boolean",
                      example: true
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/subscriptions/status/{subscriptionId}": {
      get: {
        summary: "Get subscription status",
        description: "Retrieves the current status and details of a PayPal subscription",
        tags: ["Subscriptions"],
        parameters: [
          {
            name: "subscriptionId",
            in: "path",
            required: true,
            schema: {
              type: "string"
            },
            description: "PayPal subscription identifier",
            example: "I-FBN91UE2890B"
          }
        ],
        responses: {
          200: {
            description: "Subscription status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-FBN91UE2890B"
                        },
                        status: {
                          type: "string",
                          example: "ACTIVE"
                        },
                        planId: {
                          type: "string",
                          example: "P-6L993232BE527384WM5Q53VA"
                        },
                        startTime: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:32:32Z"
                        },
                        nextBillingTime: {
                          type: "string",
                          format: "date-time",
                          example: "2025-01-17T08:00:00Z"
                        },
                        lastPaymentTime: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:33:23Z"
                        },
                        failedPayments: {
                          type: "integer",
                          example: 0
                        },
                        tier: {
                          type: "string",
                          example: "medium"
                        },
                        price: {
                          type: "number",
                          example: 9.99
                        },
                        localStatus: {
                          type: "string",
                          example: "ACTIVE"
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:32:35.281Z"
                        },
                        activatedAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T08:24.356Z"
                        },
                        lastUpdated: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T08:27.870Z"
                        }
                      }
                    }
                  }
                },
                example: {
                  success: true,
                  data: {
                    subscriptionId: "I-FBN91UE2890B",
                    status: "ACTIVE",
                    planId: "P-6L993232BE527384WM5Q53VA",
                    startTime: "2024-12-17T07:32:32Z",
                    nextBillingTime: "2025-01-17T08:00:00Z",
                    lastPaymentTime: "2024-12-17T07:33:23Z",
                    failedPayments: 0,
                    tier: "medium",
                    price: 9.99,
                    localStatus: "ACTIVE",
                    createdAt: "2024-12-17T07:32:35.281Z",
                    activatedAt: "2024-12-17T08:24.356Z",
                    lastUpdated: "2024-12-17T08:27.870Z"
                  }
                }
              }
            }
          },
          400: {
            description: "Error retrieving subscription status",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "Failed to get subscription status"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/subscriptions/create": {
      post: {
        summary: "Create subscription with optional add-ons",
        tags: ["Subscriptions"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["free", "medium", "high"],
                    description: "Subscription tier level"
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable addon 1 features",
                        default: false
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable addon 2 features",
                        default: false
                      }
                    }
                  }
                }
              },
              examples: {
                "Basic Subscription": {
                  value: {
                    tier: "medium",
                    addons: {
                      addon1: false,
                      addon2: false
                    }
                  }
                },
                "Subscription with Add-ons": {
                  value: {
                    tier: "medium",
                    addons: {
                      addon1: true,
                      addon2: true
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-BW452GLLEP1G"
                        },
                        approvalUrl: {
                          type: "string",
                          example: "https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=EC-5Y366741JN879735H"
                        },
                        tier: {
                          type: "string",
                          example: "medium"
                        },
                        basePrice: {
                          type: "number",
                          example: 9.99
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: {
                                  type: "boolean",
                                  example: true
                                },
                                price: {
                                  type: "number",
                                  example: 9.99
                                },
                                features: {
                                  type: "array",
                                  items: {
                                    type: "string"
                                  },
                                  example: [
                                    "Enhanced resolution",
                                    "Advanced filters",
                                    "Batch processing",
                                    "Priority processing"
                                  ]
                                }
                              }
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: {
                                  type: "boolean",
                                  example: false
                                },
                                price: {
                                  type: "number",
                                  example: 19.99
                                },
                                features: {
                                  type: "array",
                                  items: {
                                    type: "string"
                                  }
                                }
                              }
                            }
                          }
                        },
                        totalPrice: {
                          type: "number",
                          example: 19.98
                        },
                        status: {
                          type: "string",
                          example: "APPROVAL_PENDING"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Invalid tier specified"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/subscriptions/addons": {
      put: {
        summary: "Update subscription add-ons",
        tags: ["Subscriptions"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["subscriptionId", "addons"],
                properties: {
                  subscriptionId: {
                    type: "string",
                    description: "PayPal subscription ID"
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable/disable addon 1"
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable/disable addon 2"
                      }
                    }
                  }
                }
              },
              example: {
                subscriptionId: "I-BW452GLLEP1G",
                addons: {
                  addon1: true,
                  addon2: false
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Add-ons updated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-BW452GLLEP1G"
                        },
                        newPrice: {
                          type: "number",
                          example: 19.98
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "boolean",
                              example: true
                            },
                            addon2: {
                              type: "boolean",
                              example: false
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Invalid subscription ID"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/usage/title": {
      post: {
        summary: "Track title usage",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: []
          }
        ],
        description: "Increment the title generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the title generation",
                    example: "product-title"
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the title was generated",
                    example: "2024-12-08T02:30:00.000Z"
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      language: "en",
                      category: "product",
                      length: "short"
                    }
                  }
                }
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "product-title",
                    timestamp: "2024-12-08T02:30:00.000Z"
                  }
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "blog-title",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      language: "en",
                      category: "blog",
                      length: "medium",
                      keywords: ["tech", "ai"]
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: {
                      type: "object",
                      properties: {
                        tracked: { type: "boolean" },
                        currentUsage: { type: "number" },
                        remainingQuota: { type: "number" },
                        timestamp: {
                          type: "string",
                          format: "date-time"
                        }
                      }
                    }
                  }
                },
                example: {
                  success: true,
                  message: "Title generation usage tracked successfully",
                  data: {
                    tracked: true,
                    currentUsage: 51,
                    remainingQuota: 949,
                    timestamp: "2024-12-08T02:30:00.000Z"
                  }
                }
              }
            }
          },
          400: {
            description: "Error tracking usage",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: { type: "string" },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                },
                example: {
                  success: false,
                  message: "Invalid request format or quota exceeded",
                  timestamp: "2024-12-08T02:30:00.000Z"
                }
              }
            }
          }
        }
      }
    },
    "/api/users/login": {
      post: {
        summary: "User Login",
        description: "Authenticate user with email and password",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "password"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "User's email address",
                    example: "<EMAIL>"
                  },
                  password: {
                    type: "string",
                    description: "User's password",
                    example: "yourPassword123"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Login response",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000"
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>"
                        },
                        domain: {
                          type: "string",
                          example: "example.com"
                        },
                        api_key: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000"
                        },
                        credentials: {
                          type: "object",
                          properties: {
                            password: {
                              type: "string",
                              description: "User's password",
                              example: "yourPassword123"
                            },
                            apiKey: {
                              type: "string",
                              description: "API key for authentication",
                              example: "550e8400-e29b-41d4-a716-446655440000"
                            }
                          }
                        },
                        tier: {
                          type: "object",
                          properties: {
                            currentTier: {
                              type: "string",
                              example: "free"
                            },
                            tierName: {
                              type: "string",
                              example: "Free Tier"
                            },
                            usage: {
                              type: "integer",
                              example: 0
                            },
                            imagesUsage: {
                              type: "integer",
                              example: 0
                            },
                            contentUsage: {
                              type: "integer",
                              example: 0
                            },
                            titleUsage: {
                              type: "integer",
                              example: 0
                            },
                            maxQuota: {
                              type: "integer",
                              example: 100
                            },
                            imagesQuota: {
                              type: "integer",
                              example: 50
                            },
                            contentQuota: {
                              type: "integer",
                              example: 30
                            },
                            titleQuota: {
                              type: "integer",
                              example: 20
                            },
                            remainingImagesQuota: {
                              type: "integer",
                              example: 50
                            },
                            remainingContentQuota: {
                              type: "integer",
                              example: 30
                            },
                            remainingTitleQuota: {
                              type: "integer",
                              example: 20
                            },
                            price: {
                              type: "number",
                              example: 0
                            },
                            features: {
                              type: "array",
                              items: {
                                type: "string"
                              },
                              example: ["Basic API Access", "Standard Support"]
                            },
                            quotaPercentage: {
                              type: "string",
                              example: "0%"
                            }
                          }
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time"
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time"
                        }
                      }
                    },
                    message: {
                      type: "string"
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                },
                examples: {
                  success: {
                    value: {
                      success: true,
                      data: {
                        id: "550e8400-e29b-41d4-a716-446655440000",
                        email: "<EMAIL>",
                        domain: "example.com",
                        api_key: "550e8400-e29b-41d4-a716-446655440000",
                        credentials: {
                          password: "yourPassword123",
                          apiKey: "550e8400-e29b-41d4-a716-446655440000"
                        },
                        tier: {
                          currentTier: "free",
                          tierName: "Free Tier",
                          usage: 0,
                          imagesUsage: 0,
                          contentUsage: 0,
                          titleUsage: 0,
                          maxQuota: 100,
                          imagesQuota: 50,
                          contentQuota: 30,
                          titleQuota: 20,
                          remainingImagesQuota: 50,
                          remainingContentQuota: 30,
                          remainingTitleQuota: 20,
                          price: 0,
                          features: ["Basic API Access", "Standard Support"],
                          quotaPercentage: "0%"
                        },
                        createdAt: "2024-01-17T07:44:18.662Z",
                        updatedAt: "2024-01-17T07:44:18.662Z"
                      },
                      message: "Login successful",
                      timestamp: "2024-01-17T07:44:19.010Z"
                    },
                    summary: "Successful login"
                  },
                  failed: {
                    value: {
                      success: false,
                      message: "Invalid email or password",
                      timestamp: "2024-01-17T07:44:19.010Z"
                    },
                    summary: "Failed login"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/users/domains": {
      post: {
        summary: "Get domains for an email",
        description: "Retrieve all domains associated with an email address",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address to get domains for",
                    example: "<EMAIL>"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "List of domains for the email",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          domain: {
                            type: "string",
                            example: "example.com"
                          },
                          api_key: {
                            type: "string",
                            example: "550e8400-e29b-41d4-a716-446655440000"
                          },
                          status: {
                            type: "string",
                            example: "active"
                          },
                          createdAt: {
                            type: "string",
                            format: "date-time"
                          }
                        }
                      }
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                },
                examples: {
                  success: {
                    value: {
                      success: true,
                      data: [
                        {
                          domain: "example1.com",
                          api_key: "550e8400-e29b-41d4-a716-446655440000",
                          status: "active",
                          createdAt: "2024-01-17T07:44:18.662Z"
                        },
                        {
                          domain: "example2.com",
                          api_key: "660e8400-e29b-41d4-a716-446655440000",
                          status: "active",
                          createdAt: "2024-01-18T07:44:18.662Z"
                        }
                      ],
                      timestamp: "2024-01-17T07:44:19.010Z"
                    },
                    summary: "Successful domains retrieval"
                  }
                }
              }
            }
          },
          400: {
            description: "Email not found or invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Email is required"
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                }
              }
            }
          }
        }
      },
      delete: {
        summary: "Remove a domain from an email",
        description: "Remove a domain and its associated API key from an email address",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "domain"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address to remove domain from",
                    example: "<EMAIL>"
                  },
                  domain: {
                    type: "string",
                    description: "Domain to remove",
                    example: "example.com"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Domain successfully removed",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    message: {
                      type: "string",
                      example: "Domain removed successfully"
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Email or domain not found, or invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Email and domain are required"
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/test/webhooks/simulate/paypal/activate": {
      post: {
        tags: ["Test Webhooks"],
        summary: "Simulate PayPal subscription activation webhook",
        description: "For development only: Simulates a PayPal webhook notification for subscription activation",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["subscriptionId", "userId", "tier"],
                properties: {
                  subscriptionId: {
                    type: "string",
                    description: "PayPal subscription ID",
                    example: "I-GC8W90MU28NW"
                  },
                  userId: {
                    type: "string",
                    description: "User's email address",
                    example: "<EMAIL>"
                  },
                  tier: {
                    type: "string",
                    description: "Subscription tier (free, medium, high)",
                    enum: ["free", "medium", "high"],
                    example: "medium"
                  }
                }
              },
              example: {
                subscriptionId: "I-GC8W90MU28NW",
                userId: "<EMAIL>",
                tier: "medium"
              }
            }
          }
        },
        responses: {
          200: {
            description: "Subscription activated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    message: {
                      type: "string",
                      example: "Subscription activated successfully"
                    },
                    data: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          example: "<EMAIL>"
                        },
                        subscriptionId: {
                          type: "string",
                          example: "I-GC8W90MU28NW"
                        },
                        tier: {
                          type: "string",
                          example: "medium"
                        },
                        timestamp: {
                          type: "string",
                          format: "date-time",
                          example: "2024-01-20T06:30:00.000Z"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Bad request - missing required fields",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "subscriptionId, userId, and tier are required"
                    }
                  }
                }
              }
            }
          },
          404: {
            description: "User not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "User not found"
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Internal server error"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/debug/kv/list": {
      get: {
        tags: ["Debug"],
        summary: "List all KV entries",
        description: "Lists all KV entries grouped by their key patterns",
        security: [{ ApiKeyAuth: [] }],
        responses: {
          200: {
            description: "Successfully retrieved KV data",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        summary: {
                          type: "object",
                          properties: {
                            totalKeys: {
                              type: "integer",
                              example: 42
                            },
                            prefixes: {
                              type: "array",
                              items: {
                                type: "string"
                              },
                              example: [
                                "email",
                                "api_key",
                                "domain",
                                "user",
                                "subscription"
                              ]
                            },
                            countByPrefix: {
                              type: "object",
                              example: {
                                email: 10,
                                api_key: 15,
                                domain: 15,
                                user: 10,
                                subscription: 5
                              }
                            }
                          }
                        },
                        grouped: {
                          type: "object",
                          properties: {
                            email: {
                              type: "array",
                              items: {
                                type: "object",
                                properties: {
                                  key: { type: "string" },
                                  value: { type: "object" }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          401: {
            description: "Unauthorized",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Unauthorized" }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/debug/kv/{key}": {
      get: {
        tags: ["Debug"],
        summary: "Get KV entry by key",
        description: "Retrieves a specific KV entry by its key",
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "key",
            in: "path",
            required: true,
            schema: {
              type: "string"
            },
            description: "The full key to retrieve (e.g., 'email:<EMAIL>')"
          }
        ],
        responses: {
          200: {
            description: "Successfully retrieved KV entry",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: { type: "object" }
                  }
                }
              }
            }
          },
          404: {
            description: "Key not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Key not found" }
                  }
                }
              }
            }
          }
        }
      },
      delete: {
        tags: ["Debug"],
        summary: "Delete KV entry",
        description: "Deletes a specific KV entry",
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "key",
            in: "path",
            required: true,
            schema: {
              type: "string"
            },
            description: "The full key to delete"
          }
        ],
        responses: {
          200: {
            description: "Successfully deleted KV entry",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    message: {
                      type: "string",
                      example: "Key deleted successfully"
                    }
                  }
                }
              }
            }
          },
          404: {
            description: "Key not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Key not found" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/debug/kv/search": {
      get: {
        tags: ["Debug"],
        summary: "Search KV entries",
        description: "Searches KV entries by prefix or pattern",
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "prefix",
            in: "query",
            schema: {
              type: "string"
            },
            description: "Key prefix to search for (e.g., 'email:', 'api_key:')"
          },
          {
            name: "pattern",
            in: "query",
            schema: {
              type: "string"
            },
            description: "Pattern to match in keys or values"
          }
        ],
        responses: {
          200: {
            description: "Successfully retrieved matching KV entries",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          key: { type: "string" },
                          value: { type: "object" }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/debug/kv/truncate": {
      post: {
        tags: ["Debug"],
        summary: "Truncate KV data",
        description: "Deletes all KV data except tier settings configuration",
        security: [{ ApiKeyAuth: [] }],
        responses: {
          200: {
            description: "Successfully truncated KV data",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        deletedCount: { type: "integer", example: 42 },
                        skippedCount: { type: "integer", example: 1 },
                        deletedKeys: {
                          type: "array",
                          items: { type: "string" },
                          example: ["user:123", "email:<EMAIL>"]
                        },
                        skippedKeys: {
                          type: "array",
                          items: { type: "string" },
                          example: ["t_setting:tiers"]
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/debug/kv/truncate/status": {
      get: {
        tags: ["Debug"],
        summary: "Check KV truncate status",
        description: "Checks if the KV store is in a clean state with only tier settings remaining",
        security: [{ ApiKeyAuth: [] }],
        responses: {
          200: {
            description: "Successfully checked truncate status",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        isClean: {
                          type: "boolean",
                          description: "True if only tier settings remain",
                          example: true
                        },
                        totalKeys: {
                          type: "integer",
                          description: "Total number of keys in the KV store",
                          example: 1
                        },
                        hasTierSettings: {
                          type: "boolean",
                          description: "True if tier settings exist",
                          example: true
                        },
                        hasOtherData: {
                          type: "boolean",
                          description: "True if any non-tier data exists",
                          example: false
                        },
                        keys: {
                          type: "array",
                          description: "List of all keys in the KV store",
                          items: { type: "string" },
                          example: ["t_setting:tiers"]
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/xendit/invoice": {
      post: {
        summary: "Create Xendit Invoice",
        description: "Creates a new invoice using the Xendit API.",
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  external_id: {
                    type: "string",
                    example: "demo-invoice-001"
                  },
                  amount: {
                    type: "number",
                    example: 1e4
                  },
                  payer_email: {
                    type: "string",
                    example: "<EMAIL>"
                  },
                  description: {
                    type: "string",
                    example: "Test Invoice"
                  },
                  should_send_email: {
                    type: "boolean",
                    example: false
                  }
                },
                required: [
                  "external_id",
                  "amount",
                  "payer_email",
                  "description"
                ]
              }
            }
          }
        },
        responses: {
          201: {
            description: "Invoice created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object"
                  // You can further detail the invoice schema as needed
                }
              }
            }
          },
          500: {
            description: "Failed to create invoice",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    message: {
                      type: "string",
                      example: "Failed to create invoice"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/xendit/qris": {
      post: {
        summary: "Create QRIS Payment",
        description: "Generate a QRIS code for payment using Xendit. The response includes the QR code string that can be scanned using any QRIS-compatible mobile banking or e-wallet app.",
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["external_id", "amount"],
                properties: {
                  external_id: {
                    type: "string",
                    description: "Your unique reference ID for this payment",
                    example: "qris-payment-001"
                  },
                  amount: {
                    type: "number",
                    description: "Payment amount in IDR",
                    example: 1e4
                  },
                  description: {
                    type: "string",
                    description: "Description of the payment",
                    example: "Payment for order #123"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "QRIS payment created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    id: {
                      type: "string",
                      example: "qr_e93b8fef-aa55-4c27-b129-fc88335cf789"
                    },
                    reference_id: {
                      type: "string",
                      example: "qris-payment-001"
                    },
                    type: {
                      type: "string",
                      example: "DYNAMIC"
                    },
                    currency: {
                      type: "string",
                      example: "IDR"
                    },
                    channel_code: {
                      type: "string",
                      example: "ID_XENDIT"
                    },
                    amount: {
                      type: "number",
                      example: 1e4
                    },
                    description: {
                      type: "string",
                      example: "Payment for order #123"
                    },
                    metadata: {
                      type: "object",
                      nullable: true,
                      example: null
                    },
                    business_id: {
                      type: "string",
                      example: "679e3e04efb31029478754c"
                    },
                    created: {
                      type: "string",
                      format: "date-time",
                      example: "2025-02-04T14:22:04.056894Z"
                    },
                    updated: {
                      type: "string",
                      format: "date-time",
                      example: "2025-02-04T14:22:04.056894Z"
                    },
                    qr_string: {
                      type: "string",
                      description: "The QRIS code string that can be scanned",
                      example: "00020101021226670016ID.CO.QRIS.WWW0215ID10200324152880303UME51440014ID.CO.QRIS.WWW0215ID1020032415288020303UME5204581453033605405100005802ID5914XENDIT QR TEST6013JAKARTA PUSAT61051234562070703A016304E667"
                    },
                    status: {
                      type: "string",
                      example: "ACTIVE"
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Bad Request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "string",
                      example: "error"
                    },
                    message: {
                      type: "string",
                      example: "external_id and amount are required"
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "string",
                      example: "error"
                    },
                    message: {
                      type: "string",
                      example: "Failed to create QRIS payment"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/xendit/virtual-account": {
      post: {
        summary: "Create Virtual Account Payment",
        description: "<b>Create a Virtual Account payment</b>. <br><br> Creates a new virtual account for bank transfer payment using Xendit. Different banks have different requirements and supported features. <br><br> <b>Bank-specific notes:</b> <br> - Description field is only supported by BRI and BSI <br> - Min/max amounts are only supported by BCA, BNC, and CIMB <br> - BCA requires minimum amount of 50,000 IDR <br> - Suggested amounts for open VAs are only supported by BRI, BJB, and MANDIRI <br> - Each bank has different amount limits and currency support <br><br> <b>Supported Banks:</b> <br> - BCA (Bank Central Asia) - Min amount: 50,000 IDR <br> - BNI (Bank Negara Indonesia) <br> - BRI (Bank Rakyat Indonesia) <br> - BJB (Bank Jabar Banten) <br> - BSI (Bank Syariah Indonesia) <br> - BNC (Bank Neo Commerce) <br> - CIMB (CIMB Niaga) <br> - DBS (Bank DBS) <br> - MANDIRI (Bank Mandiri) <br> - PERMATA (Bank Permata) <br> - SAHABAT_SAMPOERNA (Bank Sahabat Sampoerna)",
        tags: ["Xendit"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["external_id", "bank_code", "name", "amount"],
                properties: {
                  external_id: {
                    type: "string",
                    description: "Unique ID for the virtual account",
                    example: "va-*************"
                  },
                  bank_code: {
                    type: "string",
                    description: "Bank code for the virtual account",
                    enum: [
                      "BCA",
                      "BNI",
                      "BRI",
                      "BJB",
                      "BSI",
                      "BNC",
                      "CIMB",
                      "DBS",
                      "MANDIRI",
                      "PERMATA",
                      "SAHABAT_SAMPOERNA"
                    ],
                    example: "BRI"
                  },
                  name: {
                    type: "string",
                    description: "Name that will be displayed in the virtual account",
                    example: "John Doe"
                  },
                  amount: {
                    type: "number",
                    description: "Payment amount. For closed VAs, this will be the expected amount. For open VAs, this will be the suggested amount.",
                    example: 5e4
                  },
                  is_closed: {
                    type: "boolean",
                    description: "Whether the virtual account has a fixed payment amount. If true, customer can only pay the exact expected_amount. If false, customer can pay any amount within min_amount and max_amount range.",
                    default: true
                  },
                  expected_amount: {
                    type: "number",
                    description: "Required for closed virtual accounts. Must match the payment amount",
                    example: 5e4
                  },
                  description: {
                    type: "string",
                    description: "Payment description (supported by some banks)",
                    example: "Payment for order #123"
                  },
                  expiration_date: {
                    type: "string",
                    format: "date-time",
                    description: "VA expiration time in UTC+0 (default: +31 years)",
                    example: "2025-02-05T14:51:18Z"
                  }
                }
              },
              examples: {
                "BNI Open VA": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BNI",
                    name: "Michael Chen",
                    amount: 5e4,
                    is_closed: false,
                    expiration_date: "2025-02-05T14:51:18Z"
                  },
                  summary: "Open Virtual Account with BNI"
                },
                "BRI Closed VA": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BRI",
                    name: "Michael Chen",
                    amount: 5e4,
                    is_closed: true,
                    expected_amount: 5e4,
                    description: "Payment for order #123",
                    expiration_date: "2025-02-05T14:51:18Z"
                  },
                  summary: "Closed Virtual Account with BRI (with description)"
                },
                "BCA VA with Amount Limits": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BCA",
                    name: "Michael Chen",
                    amount: 5e4,
                    is_closed: false,
                    min_amount: 1e4,
                    max_amount: 1e6,
                    expiration_date: "2025-02-05T14:51:18Z"
                  },
                  summary: "Open Virtual Account with BCA (with amount limits, minimum 10,000 IDR)"
                },
                "MANDIRI VA with Suggested Amount": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "MANDIRI",
                    name: "Michael Chen",
                    amount: 5e4,
                    is_closed: false,
                    expiration_date: "2025-02-05T14:51:18Z"
                  },
                  summary: "Open Virtual Account with MANDIRI (with suggested amount)"
                },
                "BSI VA with Description": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BSI",
                    name: "Michael Chen",
                    amount: 5e4,
                    is_closed: true,
                    expected_amount: 5e4,
                    description: "Payment for order #123",
                    expiration_date: "2025-02-05T14:51:18Z"
                  },
                  summary: "Closed Virtual Account with BSI (with description)"
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Virtual Account created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "57f6fbf26b9f064272622aa6"
                        },
                        external_id: {
                          type: "string",
                          example: "va-*************"
                        },
                        owner_id: {
                          type: "string",
                          example: "57b4e5181473eeb61c11f9b9"
                        },
                        bank_code: {
                          type: "string",
                          example: "BNI"
                        },
                        merchant_code: {
                          type: "string",
                          example: "8808"
                        },
                        account_number: {
                          type: "string",
                          example: "****************"
                        },
                        name: {
                          type: "string",
                          example: "Michael Chen"
                        },
                        is_single_use: {
                          type: "boolean",
                          example: false
                        },
                        is_closed: {
                          type: "boolean",
                          example: false
                        },
                        expected_amount: {
                          type: "number",
                          example: 5e4
                        },
                        suggested_amount: {
                          type: "number",
                          example: 5e4
                        },
                        expiration_date: {
                          type: "string",
                          format: "date-time",
                          example: "2025-02-05T14:51:18Z"
                        },
                        status: {
                          type: "string",
                          example: "PENDING"
                        },
                        currency: {
                          type: "string",
                          example: "IDR"
                        },
                        country: {
                          type: "string",
                          example: "ID"
                        },
                        description: {
                          type: "string",
                          example: "Payment for order #123"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Bad Request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "external_id, bank_code, and name are required"
                    }
                  }
                },
                examples: {
                  "Missing Required Fields": {
                    value: {
                      success: false,
                      error: "external_id, bank_code, and name are required"
                    }
                  },
                  "Invalid Amount": {
                    value: {
                      success: false,
                      error: "The expected amount is below the minimum limit"
                    }
                  },
                  "Bank Not Supported": {
                    value: {
                      success: false,
                      error: "That bank code is not currently supported"
                    }
                  },
                  "Feature Not Supported": {
                    value: {
                      success: false,
                      error: "description is not supported for the bank chosen"
                    }
                  }
                }
              }
            }
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "Failed to create virtual account"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/xendit/customer": {
      post: {
        summary: "Create Xendit Customer",
        description: "Create a new customer in Xendit for direct debit payments",
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["reference_id", "email", "given_names"],
                properties: {
                  reference_id: {
                    type: "string",
                    description: "Your unique reference ID for this customer",
                    example: "demo_1475801962607"
                  },
                  email: {
                    type: "string",
                    format: "email",
                    description: "Customer's email address",
                    example: "<EMAIL>"
                  },
                  given_names: {
                    type: "string",
                    description: "Customer's given names",
                    example: "John"
                  },
                  surname: {
                    type: "string",
                    description: "Customer's surname",
                    example: "Doe"
                  },
                  mobile_number: {
                    type: "string",
                    description: "Customer's mobile number in E.164 format",
                    example: "+628123456789"
                  },
                  description: {
                    type: "string",
                    description: "Description for this customer",
                    example: "Test customer for direct debit"
                  },
                  metadata: {
                    type: "object",
                    description: "Additional metadata",
                    example: {
                      city: "Jakarta"
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Customer created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "74bb3048-0bf3-4875-8938-c2c9d02e996e"
                        },
                        reference_id: {
                          type: "string",
                          example: "demo_1475801962607"
                        },
                        given_names: {
                          type: "string",
                          example: "John"
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "reference_id, email, and given_names are required"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/xendit/payment-request": {
      post: {
        summary: "Create Direct Debit Payment Request",
        description: `Create a new direct debit payment request using BRI Direct Debit.
        
        The response will include an authentication URL that requires an OTP verification:
        1. Customer will receive an OTP on their registered mobile number
        2. Send the OTP to the authentication URL provided in the response
        3. For testing, use OTP code: 333000
        
        Example authentication request:
        \`\`\`
        curl -X POST "https://api.xendit.co/v2/payment_methods/{payment_method_id}/auth" \\
          -H "Authorization: Basic {base64_encoded_api_key}" \\
          -H "Content-Type: application/json" \\
          -d '{"otp_code":"333000"}'
        \`\`\`
        `,
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: [
                  "amount",
                  "currency",
                  "payment_method",
                  "customer_id",
                  "reference_id"
                ],
                properties: {
                  amount: {
                    type: "number",
                    description: "Payment amount",
                    example: 5e4
                  },
                  currency: {
                    type: "string",
                    description: "Payment currency",
                    example: "IDR",
                    enum: ["IDR"]
                  },
                  payment_method: {
                    type: "object",
                    description: "Payment method details",
                    required: ["type", "direct_debit", "reusability"],
                    properties: {
                      type: {
                        type: "string",
                        description: "Payment method type",
                        example: "DIRECT_DEBIT",
                        enum: ["DIRECT_DEBIT"]
                      },
                      direct_debit: {
                        type: "object",
                        required: ["channel_code", "channel_properties"],
                        properties: {
                          channel_code: {
                            type: "string",
                            description: "Bank channel code",
                            example: "BRI",
                            enum: ["BRI"]
                          },
                          channel_properties: {
                            type: "object",
                            required: [
                              "success_return_url",
                              "failure_return_url",
                              "mobile_number",
                              "email",
                              "card_last_four",
                              "card_expiry"
                            ],
                            properties: {
                              success_return_url: {
                                type: "string",
                                description: "URL to redirect after successful payment",
                                example: "https://redirect.me/success"
                              },
                              failure_return_url: {
                                type: "string",
                                description: "URL to redirect after failed payment",
                                example: "https://redirect.me/failure"
                              },
                              mobile_number: {
                                type: "string",
                                description: "Customer's mobile number in E.164 format",
                                example: "+628123456789"
                              },
                              email: {
                                type: "string",
                                description: "Customer's email address",
                                example: "<EMAIL>"
                              },
                              card_last_four: {
                                type: "string",
                                description: "Last 4 digits of the debit card",
                                example: "8888"
                              },
                              card_expiry: {
                                type: "string",
                                description: "Card expiry in MM/YY format",
                                example: "11/25"
                              }
                            }
                          }
                        }
                      },
                      reusability: {
                        type: "string",
                        description: "Whether the payment method can be reused",
                        example: "ONE_TIME_USE",
                        enum: ["ONE_TIME_USE"]
                      }
                    }
                  },
                  customer_id: {
                    type: "string",
                    description: "Xendit customer ID (from create customer response)",
                    example: "74bb3048-0bf3-4875-8938-c2c9d02e996e"
                  },
                  description: {
                    type: "string",
                    description: "Payment description",
                    example: "Pembayaran untuk Order #123"
                  },
                  metadata: {
                    type: "object",
                    description: "Additional metadata",
                    example: {
                      order_id: "ORDER-123",
                      product_name: "Premium Package"
                    }
                  },
                  reference_id: {
                    type: "string",
                    description: "Your unique reference ID for this payment",
                    example: "order-123-abc"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "Payment request created successfully. Requires OTP authentication.",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "pr-e64ccb50-7b75-4c65-a909-958852c61386"
                        },
                        status: {
                          type: "string",
                          example: "REQUIRES_ACTION",
                          description: "Payment requires OTP authentication"
                        },
                        actions: {
                          type: "array",
                          description: "Authentication actions required to complete the payment",
                          items: {
                            type: "object",
                            properties: {
                              action: {
                                type: "string",
                                example: "AUTH",
                                description: "Action type - AUTH for OTP authentication"
                              },
                              url: {
                                type: "string",
                                example: "https://api.xendit.co/v2/payment_methods/pm-xxx/auth",
                                description: "URL to send the OTP for authentication"
                              },
                              method: {
                                type: "string",
                                example: "POST",
                                description: "HTTP method to use for authentication"
                              }
                            }
                          }
                        },
                        payment_method: {
                          type: "object",
                          properties: {
                            id: {
                              type: "string",
                              description: "Payment method ID needed for authentication",
                              example: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3"
                            }
                          }
                        }
                      }
                    }
                  }
                },
                example: {
                  success: true,
                  data: {
                    id: "pr-6fc5c020-e6a4-4d0a-8a23-d46d49f2427d",
                    status: "REQUIRES_ACTION",
                    actions: [
                      {
                        action: "AUTH",
                        url: "https://api.xendit.co/v2/payment_methods/pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3/auth",
                        method: "POST"
                      }
                    ],
                    payment_method: {
                      id: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3",
                      status: "PENDING"
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "amount, currency, payment_method, and customer_id are required"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/xendit/payment-methods/{payment_method_id}/auth": {
      post: {
        summary: "Validate Direct Debit OTP",
        description: `Validate the OTP for a direct debit payment.
        
        For testing, use OTP code: 333000
        `,
        tags: ["Xendit"],
        parameters: [
          {
            name: "payment_method_id",
            in: "path",
            required: true,
            description: "Payment method ID received from the payment request response",
            schema: {
              type: "string"
            },
            example: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3"
          }
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["otp_code"],
                properties: {
                  otp_code: {
                    type: "string",
                    description: "OTP code received by the customer (use 333000 for testing)",
                    example: "333000"
                  }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: "OTP validated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3"
                        },
                        status: {
                          type: "string",
                          example: "SUCCESS",
                          description: "Status of the payment method after OTP validation"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "payment_method_id and otp_code are required"
                    }
                  }
                }
              }
            }
          },
          401: {
            description: "Invalid OTP",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false
                    },
                    error: {
                      type: "string",
                      example: "Invalid OTP code"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  components: {
    schemas: {
      UserResponse: {
        type: "object",
        properties: {
          id: { type: "string" },
          username: { type: "string" },
          email: { type: "string" },
          api_key: { type: "string" }
        }
      },
      TierConfig: {
        type: "object",
        properties: {
          name: { type: "string" },
          maxQuota: { type: "number" },
          price: { type: "number" },
          features: {
            type: "array",
            items: { type: "string" }
          }
        }
      },
      TierStatusResponse: {
        type: "object",
        properties: {
          success: { type: "boolean" },
          data: {
            type: "object",
            properties: {
              currentTier: { type: "string" },
              tierName: { type: "string" },
              usage: { type: "number" },
              imagesUsage: { type: "number" },
              contentUsage: { type: "number" },
              titleUsage: { type: "number" },
              maxQuota: { type: "number" },
              imagesQuota: { type: "number" },
              contentQuota: { type: "number" },
              titleQuota: { type: "number" },
              remainingImagesQuota: { type: "number" },
              remainingContentQuota: { type: "number" },
              remainingTitleQuota: { type: "number" },
              price: { type: "number" },
              features: {
                type: "array",
                items: { type: "string" }
              },
              quotaPercentage: { type: "string" }
            }
          }
        }
      },
      UsageHistoryEntry: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          type: { type: "string", enum: ["images", "content"] },
          source: { type: "string" },
          timestamp: { type: "string", format: "date-time" },
          metadata: {
            type: "object",
            additionalProperties: true,
            example: {
              size: "1024x1024",
              model: "stable-diffusion"
            }
          }
        }
      },
      DailyUsage: {
        type: "object",
        properties: {
          date: { type: "string", format: "date" },
          images: { type: "integer" },
          content: { type: "integer" },
          title: { type: "integer" },
          details: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "string", format: "uuid" },
                type: { type: "string", enum: ["images", "content", "title"] },
                source: { type: "string" },
                time: { type: "string", format: "date-time" }
              }
            }
          }
        },
        example: {
          date: "2024-12-08",
          images: 15,
          content: 8,
          title: 12,
          details: [
            {
              id: "123e4567-e89b-12d3-a456-************",
              type: "title",
              source: "product-title",
              time: "2024-12-08T02:30:00.000Z"
            }
          ]
        }
      },
      PayPalSubscription: {
        type: "object",
        properties: {
          subscriptionId: {
            type: "string",
            description: "PayPal subscription identifier"
          },
          status: {
            type: "string",
            enum: [
              "APPROVAL_PENDING",
              "APPROVED",
              "ACTIVE",
              "SUSPENDED",
              "CANCELLED",
              "EXPIRED"
            ]
          },
          tier: {
            type: "string",
            enum: ["medium", "high"]
          },
          price: {
            type: "number",
            description: "Monthly subscription price in USD"
          },
          createdAt: {
            type: "string",
            format: "date-time"
          },
          nextBillingDate: {
            type: "string",
            format: "date-time"
          }
        }
      },
      Addons: {
        type: "object",
        properties: {
          addon1: {
            type: "boolean",
            description: "Enhanced features add-on"
          },
          addon2: {
            type: "boolean",
            description: "Premium support add-on"
          }
        }
      }
    },
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "x-sps-key",
        description: "API key for authentication"
      },
      DomainAuth: {
        type: "apiKey",
        in: "header",
        name: "x-sps-domain",
        description: "Domain associated with the API key"
      }
    }
  }
};
function getSwaggerHTML(swaggerSpec) {
  return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>API Documentation</title>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui.min.css" />
      </head>
      <body>
          <div id="swagger-ui"></div>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui-bundle.min.js"><\/script>
          <script>
              window.onload = () => {
                  window.ui = SwaggerUIBundle({
                      spec: ${JSON.stringify(swaggerSpec)},
                      dom_id: '#swagger-ui',
                      deepLinking: true,
                      presets: [
                          SwaggerUIBundle.presets.apis,
                          SwaggerUIBundle.SwaggerUIStandalonePreset
                      ],
                  });
              };
          <\/script>
      </body>
      </html>
    `;
}
__name(getSwaggerHTML, "getSwaggerHTML");

// src/routes/usageRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/usageController.js
init_checked_fetch();
init_modules_watch_stub();

// src/services/usageHistoryService.js
init_checked_fetch();
init_modules_watch_stub();
var _UsageHistoryService = class {
  constructor(env) {
    this.env = env;
  }
  async trackUsage(userId, data, requestDomain) {
    try {
      const historyId = crypto.randomUUID();
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      const day = timestamp.split("T")[0];
      const historyEntry = {
        id: historyId,
        userId,
        type: data.type,
        // 'images' or 'content'
        source: data.source,
        timestamp,
        metadata: data.metadata || {},
        domain: requestDomain
        // Store the domain from the request
      };
      await this.env.USERS_KV.put(
        `${_UsageHistoryService.KEYS.HISTORY}:${userId}:${historyId}`,
        JSON.stringify(historyEntry)
      );
      const dailyKey = `${_UsageHistoryService.KEYS.DAILY}:${userId}:${day}`;
      const existingDaily = await this.env.USERS_KV.get(dailyKey, "json") || {
        date: day,
        images: 0,
        content: 0,
        details: []
      };
      existingDaily[data.type] += 1;
      existingDaily.details.push({
        id: historyId,
        type: data.type,
        source: data.source,
        time: timestamp,
        domain: requestDomain
        // Include domain in daily details
      });
      await this.env.USERS_KV.put(dailyKey, JSON.stringify(existingDaily));
      return historyEntry;
    } catch (error) {
      console.error("Error tracking usage history:", error);
      throw error;
    }
  }
  async getUserHistory(userId, options = {}) {
    try {
      const {
        startDate,
        endDate = (/* @__PURE__ */ new Date()).toISOString(),
        type,
        limit = 100,
        page = 1
      } = options;
      const { keys } = await this.env.USERS_KV.list({
        prefix: `${_UsageHistoryService.KEYS.HISTORY}:${userId}:`
      });
      const entries = await Promise.all(
        keys.map((key) => this.env.USERS_KV.get(key.name, "json"))
      );
      let filteredEntries = entries.filter((entry) => {
        if (!entry)
          return false;
        const matchesType = !type || entry.type === type;
        const matchesDateRange = (!startDate || entry.timestamp >= startDate) && entry.timestamp <= endDate;
        return matchesType && matchesDateRange;
      }).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      const totalEntries = filteredEntries.length;
      const totalPages = Math.ceil(totalEntries / limit);
      const offset = (page - 1) * limit;
      filteredEntries = filteredEntries.slice(offset, offset + limit);
      return {
        entries: filteredEntries.map((entry) => ({
          ...entry,
          domain: entry.domain || null
          // Ensure domain is always included, even if null
        })),
        pagination: {
          total: totalEntries,
          totalPages,
          currentPage: page,
          limit
        }
      };
    } catch (error) {
      console.error("Error getting user history:", error);
      throw error;
    }
  }
  async getDailyUsage(userId, startDate, endDate = (/* @__PURE__ */ new Date()).toISOString()) {
    try {
      const { keys } = await this.env.USERS_KV.list({
        prefix: `${_UsageHistoryService.KEYS.DAILY}:${userId}:`
      });
      const dailyEntries = await Promise.all(
        keys.filter((key) => {
          const date = key.name.split(":")[3];
          return (!startDate || date >= startDate) && date <= endDate.split("T")[0];
        }).map((key) => this.env.USERS_KV.get(key.name, "json"))
      );
      return dailyEntries.sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error("Error getting daily usage:", error);
      throw error;
    }
  }
};
var UsageHistoryService = _UsageHistoryService;
__name(UsageHistoryService, "UsageHistoryService");
__publicField(UsageHistoryService, "KEYS", {
  HISTORY: "usage_history",
  DAILY: "daily_usage"
});

// src/controllers/usageController.js
var UsageController = class {
  constructor(env) {
    this.env = env;
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
    this.apiKeyService = new ApiKeyService(env);
    this.historyService = new UsageHistoryService(env);
  }
  async _validateApiKey(request, requireDomain = true) {
    const apiKey = request.headers.get("x-sps-key");
    const requestDomain = request.headers.get("x-sps-domain");
    if (!apiKey) {
      throw new Error("API Key is required");
    }
    if (requireDomain && !requestDomain) {
      throw new Error("Domain is required in x-sps-domain header");
    }
    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("Invalid API Key");
    }
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }
    const domain = user.domains?.find((d) => d.api_key === apiKey);
    if (!domain) {
      throw new Error("Invalid API Key");
    }
    if (requireDomain && requestDomain && domain.domain !== requestDomain) {
      throw new Error(
        "Failed activate api key, use the correct registered domain !"
      );
    }
    return {
      userId: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: apiKey
    };
  }
  async trackImageUsage(request) {
    try {
      const user = await this._validateApiKey(request, true);
      const data = await request.json();
      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }
      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.userId,
        "images",
        user.api_key
      );
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );
      await this.historyService.trackUsage(
        user.userId,
        {
          type: "images",
          source: data.source,
          timestamp: data.timestamp,
          metadata: data.metadata
        },
        user.domain
      );
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            type: "images",
            usage: updatedUsage,
            source: data.source,
            timestamp: data.timestamp,
            domain: user.domain,
            tierStatus: {
              current: updatedUsage,
              remaining: tierStatus.remainingImagesQuota,
              limit: tierStatus.imagesQuota
            }
          })
        ),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async trackContentUsage(request) {
    try {
      const user = await this._validateApiKey(request, true);
      const data = await request.json();
      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }
      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.userId,
        "content",
        user.api_key
      );
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );
      await this.historyService.trackUsage(
        user.userId,
        {
          type: "content",
          source: data.source,
          timestamp: data.timestamp,
          metadata: data.metadata
        },
        user.domain
      );
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            {
              type: "content",
              usage: updatedUsage,
              source: data.source,
              timestamp: data.timestamp,
              domain: user.domain,
              tierStatus: {
                current: updatedUsage,
                remaining: tierStatus.remainingContentQuota,
                limit: tierStatus.contentQuota
              }
            },
            "Content usage tracked successfully"
          )
        ),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUsageStats(request) {
    try {
      const user = await this._validateApiKey(request, false);
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );
      const emailTier = await this.tierService.getEmailTier(user.email);
      let usageStats;
      if (emailTier.tier === TierService2.TIERS.FREE) {
        const domainImagesUsage = await this.tierService.getDomainQuotaUsage(
          user.domain,
          "images"
        );
        const domainContentUsage = await this.tierService.getDomainQuotaUsage(
          user.domain,
          "content"
        );
        const domainTitleUsage = await this.tierService.getDomainQuotaUsage(
          user.domain,
          "title"
        );
        const tierConfig = TierService2.DEFAULT_TIER_CONFIG[TierService2.TIERS.FREE];
        usageStats = {
          images: {
            used: domainImagesUsage || 0,
            remaining: Math.max(0, tierConfig.imagesQuota - domainImagesUsage),
            total: tierConfig.imagesQuota,
            percentageUsed: ((domainImagesUsage || 0) / tierConfig.imagesQuota * 100).toFixed(2)
          },
          content: {
            used: domainContentUsage || 0,
            remaining: Math.max(
              0,
              tierConfig.contentQuota - domainContentUsage
            ),
            total: tierConfig.contentQuota,
            percentageUsed: ((domainContentUsage || 0) / tierConfig.contentQuota * 100).toFixed(2)
          },
          title: {
            used: domainTitleUsage || 0,
            remaining: Math.max(0, tierConfig.titleQuota - domainTitleUsage),
            total: tierConfig.titleQuota,
            percentageUsed: ((domainTitleUsage || 0) / tierConfig.titleQuota * 100).toFixed(2)
          }
        };
      } else {
        usageStats = {
          images: {
            used: tierStatus.imagesUsage || 0,
            remaining: tierStatus.remainingImagesQuota,
            total: tierStatus.imagesQuota,
            percentageUsed: ((tierStatus.imagesUsage || 0) / tierStatus.imagesQuota * 100).toFixed(2)
          },
          content: {
            used: tierStatus.contentUsage || 0,
            remaining: tierStatus.remainingContentQuota,
            total: tierStatus.contentQuota,
            percentageUsed: ((tierStatus.contentUsage || 0) / tierStatus.contentQuota * 100).toFixed(2)
          },
          title: {
            used: tierStatus.titleUsage || 0,
            remaining: tierStatus.remainingTitleQuota,
            total: tierStatus.titleQuota,
            percentageUsed: ((tierStatus.titleUsage || 0) / tierStatus.titleQuota * 100).toFixed(2)
          }
        };
      }
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            ...usageStats,
            tier: {
              name: tierStatus.tierName,
              current: tierStatus.currentTier,
              expirationDate: tierStatus.expirationDate,
              isExpired: tierStatus.isExpired,
              price: tierStatus.price
            },
            addons: {
              addon1: {
                enabled: tierStatus.addon1,
                price: tierStatus.addon1_price,
                features: tierStatus.addon1_detail
              },
              addon2: {
                enabled: tierStatus.addon2,
                price: tierStatus.addon2_price,
                features: tierStatus.addon2_detail
              }
            },
            features: tierStatus.features
          })
        ),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getUsageHistory(request) {
    try {
      const user = await this._validateApiKey(request, false);
      const url = new URL(request.url);
      const options = {
        startDate: url.searchParams.get("startDate"),
        endDate: url.searchParams.get("endDate"),
        type: url.searchParams.get("type"),
        limit: parseInt(url.searchParams.get("limit") || "100"),
        page: parseInt(url.searchParams.get("page") || "1")
      };
      const history = await this.historyService.getUserHistory(
        user.userId,
        options
      );
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(history)),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getDailyUsage(request) {
    try {
      const user = await this._validateApiKey(request, false);
      const url = new URL(request.url);
      const startDate = url.searchParams.get("startDate");
      const endDate = url.searchParams.get("endDate");
      const dailyUsage = await this.historyService.getDailyUsage(
        user.userId,
        startDate,
        endDate
      );
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(dailyUsage)),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async trackTitleUsage(request) {
    try {
      const user = await this._validateApiKey(request, true);
      const data = await request.json();
      if (!data.source || !data.timestamp) {
        throw new Error("Source and timestamp are required");
      }
      const updatedUsage = await this.tierService.incrementTypeQuotaUsage(
        user.userId,
        "title",
        user.api_key
      );
      const tierStatus = await this.tierService.getUserTierStatus(
        user.userId,
        user.api_key
      );
      await this.historyService.trackUsage(
        user.userId,
        {
          type: "title",
          source: data.source,
          timestamp: data.timestamp,
          metadata: data.metadata
        },
        user.domain
      );
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            tracked: true,
            currentUsage: updatedUsage,
            remainingQuota: tierStatus.remainingTitleQuota,
            timestamp: data.timestamp,
            domain: user.domain,
            tierStatus: {
              current: updatedUsage,
              remaining: tierStatus.remainingTitleQuota,
              limit: tierStatus.titleQuota
            }
          })
        ),
        {
          status: 201,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: error.message.includes("quota exceeded") ? 403 : 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(UsageController, "UsageController");

// src/routes/usageRoutes.js
function createUsageRouter(env) {
  const router2 = e({ base: "/api/usage" });
  const controller = new UsageController(env);
  router2.post("/images", (request) => controller.trackImageUsage(request));
  router2.post("/content", (request) => controller.trackContentUsage(request));
  router2.post("/title", (request) => controller.trackTitleUsage(request));
  router2.get("/stats", (request) => controller.getUsageStats(request));
  router2.get("/history", (request) => controller.getUsageHistory(request));
  router2.get("/daily", (request) => controller.getDailyUsage(request));
  return router2;
}
__name(createUsageRouter, "createUsageRouter");

// src/routes/webhookRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/webhookController.js
init_checked_fetch();
init_modules_watch_stub();
var WebhookController = class {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierService2(env);
    this.responseService = new ResponseService();
  }
  async handlePayPalWebhook(request) {
    try {
      const paypalSignature = request.headers.get("paypal-transmission-sig");
      const paypalCertUrl = request.headers.get("paypal-cert-url");
      const paypalTransmissionId = request.headers.get(
        "paypal-transmission-id"
      );
      const paypalTransmissionTime = request.headers.get(
        "paypal-transmission-time"
      );
      if (!paypalSignature || !paypalCertUrl || !paypalTransmissionId || !paypalTransmissionTime) {
        throw new Error("Missing required PayPal webhook headers");
      }
      const webhookData = await request.json();
      console.log("Received PayPal webhook:", webhookData);
      const eventType = webhookData.event_type;
      const resourceId = webhookData.resource.id;
      switch (eventType) {
        case "BILLING.SUBSCRIPTION.ACTIVATED":
          await this._handleSubscriptionActivated(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.UPDATED":
          await this._handleSubscriptionUpdated(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.CANCELLED":
          await this._handleSubscriptionCancelled(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.EXPIRED":
          await this._handleSubscriptionExpired(
            resourceId,
            webhookData.resource
          );
          break;
        default:
          console.log("Unhandled webhook event type:", eventType);
      }
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    } catch (error) {
      console.error("Error handling PayPal webhook:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to process webhook"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async _handleSubscriptionActivated(subscriptionId, resource) {
    try {
      console.log("Handling subscription activated:", subscriptionId);
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }
      const userId = subscriptionData.userId;
      if (!userId) {
        throw new Error("User ID not found in subscription data");
      }
      await this.tierService.updateUserTier(userId, {
        tier: subscriptionData.tier,
        subscriptionId,
        addons: subscriptionData.addons,
        status: "active",
        startDate: (/* @__PURE__ */ new Date()).toISOString(),
        expirationDate: resource.billing_info?.next_billing_time || null
      });
      console.log("Successfully activated subscription for user:", userId);
    } catch (error) {
      console.error("Error handling subscription activation:", error);
      throw error;
    }
  }
  async _handleSubscriptionUpdated(subscriptionId, resource) {
    try {
      console.log("Handling subscription updated:", subscriptionId);
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }
      await this.env.USERS_KV.put(
        `subscription:${subscriptionId}`,
        JSON.stringify({
          ...subscriptionData,
          status: resource.status,
          nextBillingTime: resource.billing_info?.next_billing_time || null,
          updatedAt: (/* @__PURE__ */ new Date()).toISOString()
        })
      );
      console.log("Successfully updated subscription:", subscriptionId);
    } catch (error) {
      console.error("Error handling subscription update:", error);
      throw error;
    }
  }
  async _handleSubscriptionCancelled(subscriptionId, resource) {
    try {
      console.log("Handling subscription cancelled:", subscriptionId);
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }
      await this.tierService.updateUserTier(subscriptionData.userId, {
        tier: "free",
        status: "cancelled",
        subscriptionId: null,
        addons: { addon1: false, addon2: false }
      });
      console.log(
        "Successfully cancelled subscription for user:",
        subscriptionData.userId
      );
    } catch (error) {
      console.error("Error handling subscription cancellation:", error);
      throw error;
    }
  }
  async _handleSubscriptionExpired(subscriptionId, resource) {
    await this._handleSubscriptionCancelled(subscriptionId, resource);
  }
  async getWebhookStatus(request) {
    try {
      const accessToken = await this.paypalService.getAccessToken();
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            paypal: {
              connected: !!accessToken,
              sandbox: this.env.PAYPAL_SANDBOX === "true",
              webhookId: !!this.env.PAYPAL_WEBHOOK_ID
            }
          }
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get webhook status"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async getRecentEvents(request) {
    try {
      const events = await this.env.USERS_KV.get("webhook_events", "json") || [];
      return new Response(
        JSON.stringify({
          success: true,
          data: events
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get recent events"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(WebhookController, "WebhookController");

// src/routes/webhookRoutes.js
function createWebhookRouter(env) {
  const router2 = e({ base: "/api/webhooks" });
  const controller = new WebhookController(env);
  router2.post("/paypal", (request) => controller.handlePayPalWebhook(request));
  router2.get("/status", (request) => controller.getWebhookStatus(request));
  router2.get("/events", (request) => controller.getRecentEvents(request));
  return router2;
}
__name(createWebhookRouter, "createWebhookRouter");

// src/routes/webhookTestRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/webhookTestController.js
init_checked_fetch();
init_modules_watch_stub();
var WebhookTestController = class {
  constructor(env) {
    this.env = env;
  }
  async simulatePayPalWebhook(request) {
    try {
      const { eventType, subscriptionId } = await request.json();
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:"
      });
      let subscriptionData = null;
      let userId = null;
      for (const key of keys) {
        const data = await this.env.USERS_KV.get(key.name, "json");
        if (data?.subscriptionId === subscriptionId) {
          subscriptionData = data;
          userId = key.name.split(":")[1];
          break;
        }
      }
      if (!subscriptionData || !userId) {
        throw new Error(`Subscription not found: ${subscriptionId}`);
      }
      const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
      if (!user) {
        throw new Error(`User not found for subscription: ${subscriptionId}`);
      }
      let webhookEvent;
      if (eventType === "PAYMENT.SALE.COMPLETED") {
        webhookEvent = this.generatePaymentEvent(
          subscriptionId,
          subscriptionData,
          user
        );
      } else {
        webhookEvent = this.generateSubscriptionEvent(
          eventType,
          subscriptionId,
          subscriptionData,
          user
        );
      }
      const transmissionId = crypto.randomUUID();
      const webhookHeaders = {
        "paypal-auth-algo": "SHA256withRSA",
        "paypal-cert-url": "https://api.sandbox.paypal.com/v1/notifications/certs/CERT-360caa42-fca2a594-bc34f77b",
        "paypal-transmission-id": transmissionId,
        "paypal-transmission-sig": `mock_${transmissionId}`,
        "paypal-transmission-time": webhookEvent.create_time,
        "Content-Type": "application/json"
      };
      const response = await fetch(
        new URL("/api/webhooks/paypal", request.url),
        {
          method: "POST",
          headers: webhookHeaders,
          body: JSON.stringify(webhookEvent)
        }
      );
      const result = await response.json();
      return new Response(
        JSON.stringify({
          success: true,
          message: `PayPal ${eventType} webhook simulated`,
          details: {
            webhookId: webhookEvent.id,
            transmissionId,
            subscription: subscriptionId,
            user: user.email,
            timestamp: webhookEvent.create_time,
            amount: subscriptionData.price
          },
          result
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Webhook simulation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  generatePaymentEvent(subscriptionId, subscriptionData, user) {
    const now = (/* @__PURE__ */ new Date()).toISOString();
    const paymentId = `PAY-${crypto.randomUUID()}`;
    return {
      id: `WH-${crypto.randomUUID()}`,
      create_time: now,
      resource_type: "sale",
      event_type: "PAYMENT.SALE.COMPLETED",
      summary: "Payment completed for subscription",
      resource: {
        id: paymentId,
        state: "completed",
        amount: {
          total: subscriptionData.price.toString(),
          currency: "USD",
          details: {
            subtotal: subscriptionData.price.toString()
          }
        },
        payment_mode: "INSTANT_TRANSFER",
        protection_eligibility: "ELIGIBLE",
        protection_eligibility_type: "ITEM_NOT_RECEIVED_ELIGIBLE,UNAUTHORIZED_PAYMENT_ELIGIBLE",
        transaction_fee: {
          value: (subscriptionData.price * 0.029 + 0.3).toFixed(2),
          currency: "USD"
        },
        billing_agreement_id: subscriptionId,
        create_time: now,
        update_time: now,
        links: [
          {
            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}`,
            rel: "self",
            method: "GET"
          },
          {
            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}/refund`,
            rel: "refund",
            method: "POST"
          }
        ]
      },
      links: [
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,
          rel: "self",
          method: "GET"
        },
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,
          rel: "resend",
          method: "POST"
        }
      ]
    };
  }
  generateSubscriptionEvent(eventType, subscriptionId, subscriptionData, user) {
    const now = (/* @__PURE__ */ new Date()).toISOString();
    return {
      id: `WH-${crypto.randomUUID()}`,
      event_type: eventType,
      event_version: "1.0",
      create_time: now,
      resource_type: "subscription",
      resource_version: "2.0",
      summary: `Subscription ${eventType.split(".").pop().toLowerCase()}`,
      resource: {
        start_time: subscriptionData.createdAt,
        quantity: "1",
        subscriber: {
          name: {
            given_name: user.email.split("@")[0],
            surname: ""
          },
          email_address: user.email,
          payer_id: user.id
        },
        status: "ACTIVE",
        status_update_time: now,
        id: subscriptionId,
        plan_id: subscriptionData.planId,
        billing_info: {
          outstanding_balance: {
            currency_code: "USD",
            value: "0.00"
          },
          cycle_executions: [
            {
              tenure_type: "REGULAR",
              sequence: 1,
              cycles_completed: 1,
              cycles_remaining: 0,
              current_pricing_scheme_version: 1
            }
          ],
          last_payment: {
            amount: {
              currency_code: "USD",
              value: subscriptionData.price.toString()
            },
            time: now
          },
          next_billing_time: new Date(
            Date.now() + 30 * 24 * 60 * 60 * 1e3
          ).toISOString(),
          failed_payments_count: 0
        }
      },
      links: [
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,
          rel: "self",
          method: "GET"
        },
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,
          rel: "resend",
          method: "POST"
        }
      ]
    };
  }
};
__name(WebhookTestController, "WebhookTestController");

// src/routes/webhookTestRoutes.js
function createWebhookTestRouter(env) {
  const router2 = e({ base: "/api/test/webhooks" });
  const controller = new WebhookTestController(env);
  const tierService = new TierService2(env);
  router2.post(
    "/simulate/paypal",
    (request) => controller.simulatePayPalWebhook(request)
  );
  router2.post("/simulate/paypal/activate", async (request) => {
    try {
      const { subscriptionId, userId, tier } = await request.json();
      if (!subscriptionId || !userId || !tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "subscriptionId, userId, and tier are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      let email = userId;
      if (!userId.includes("@")) {
        const userEmail = await tierService.getEmailFromUserId(userId);
        if (!userEmail) {
          return new Response(
            JSON.stringify({
              success: false,
              message: "User not found"
            }),
            {
              status: 404,
              headers: { "Content-Type": "application/json" }
            }
          );
        }
        email = userEmail;
      }
      const result = await tierService.upgradeEmailTier(email, tier, {
        subscriptionId,
        addon1: false,
        addon2: false
      });
      return new Response(
        JSON.stringify({
          success: true,
          message: "Subscription activated successfully",
          data: {
            email,
            subscriptionId,
            tier,
            timestamp: (/* @__PURE__ */ new Date()).toISOString(),
            status: result
          }
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error simulating webhook:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });
  return router2;
}
__name(createWebhookTestRouter, "createWebhookTestRouter");

// src/queues/emailQueueHandler.js
init_checked_fetch();
init_modules_watch_stub();
var emailQueueHandler_default = {
  async queue(batch, env) {
    const messages = batch.messages;
    for (const message of messages) {
      try {
        const emailData = JSON.parse(message.body);
        console.log("Processing email:", emailData);
        message.ack();
      } catch (error) {
        console.error("Error processing message:", error);
        message.retry();
      }
    }
  }
};

// src/routes/xenditRoutes.js
init_checked_fetch();
init_modules_watch_stub();

// src/controllers/xenditController.js
init_checked_fetch();
init_modules_watch_stub();

// src/services/xenditService.js
init_checked_fetch();
init_modules_watch_stub();
var XenditService = class {
  constructor(env) {
    console.log("XenditService: Initializing with environment:", {
      hasSecretKey: !!env.XENDIT_SECRET_KEY,
      secretKeyPrefix: env.XENDIT_SECRET_KEY ? env.XENDIT_SECRET_KEY.substring(0, 4) : "none"
    });
    this.env = env;
    this.secretKey = env.XENDIT_SECRET_KEY;
    if (!this.secretKey) {
      console.error("XenditService: XENDIT_SECRET_KEY is not configured");
      throw new Error(
        "XENDIT_SECRET_KEY is not configured in environment variables"
      );
    }
    if (!this.secretKey.startsWith("xnd_")) {
      console.error(
        "XenditService: Invalid API key format:",
        this.secretKey.substring(0, 4)
      );
      throw new Error(
        "Invalid Xendit API key format. Make sure you're using the correct API key from your Xendit dashboard"
      );
    }
    console.log(
      "XenditService: Successfully initialized with API key:",
      this.secretKey.substring(0, 8) + "..."
    );
    this.baseUrl = "https://api.xendit.co";
  }
  async createInvoice({
    external_id,
    amount,
    payer_email,
    description,
    should_send_email = false
  }) {
    const url = `${this.baseUrl}/v2/invoices`;
    const data = {
      external_id,
      amount,
      payer_email,
      description,
      should_send_email,
      currency: "IDR"
    };
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey)}`
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) {
      const errorData = await response.json();
      console.error("Xendit API Error:", {
        status: response.status,
        data: errorData,
        requestData: data
      });
      throw new Error(errorData.message || "Failed to create invoice");
    }
    return await response.json();
  }
  async createQRISPayment({
    external_id,
    amount,
    description,
    type = "DYNAMIC",
    currency = "IDR"
  }) {
    const url = `${this.baseUrl}/qr_codes`;
    const data = {
      reference_id: external_id,
      type,
      currency,
      amount
    };
    if (description)
      data.description = description;
    if (this.env.APP_URL) {
      data.callback_url = `${this.env.APP_URL}/api/xendit/callback`;
    }
    console.log("Sending QRIS request to:", url);
    console.log("Request data:", data);
    console.log("Using API Key:", this.secretKey.substring(0, 8) + "...");
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey)}`,
        "api-version": "2022-07-31"
      },
      body: JSON.stringify(data)
    });
    const responseData = await response.json();
    console.log("Xendit API Response:", responseData);
    if (!response.ok) {
      console.error("Xendit QRIS API Error:", {
        status: response.status,
        data: responseData,
        requestData: data
      });
      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }
      throw new Error(responseData.message || "Failed to create QRIS payment");
    }
    return responseData;
  }
  async createVirtualAccount({
    external_id,
    bank_code,
    name,
    amount,
    is_closed = true,
    expiration_date,
    expected_amount = amount,
    min_amount,
    max_amount,
    description,
    is_single_use = false,
    currency = "IDR"
  }) {
    const url = `${this.baseUrl}/callback_virtual_accounts`;
    const data = {
      external_id,
      bank_code,
      name,
      is_closed,
      is_single_use,
      currency
    };
    if (bank_code === "BCA") {
      const BCA_MIN_AMOUNT = 5e4;
      if (is_closed) {
        if (amount < BCA_MIN_AMOUNT) {
          throw new Error(
            `BCA virtual accounts require a minimum amount of ${BCA_MIN_AMOUNT} IDR`
          );
        }
        data.expected_amount = amount;
      } else {
        data.min_amount = Math.max(
          min_amount || BCA_MIN_AMOUNT,
          BCA_MIN_AMOUNT
        );
        if (max_amount) {
          data.max_amount = max_amount;
        }
      }
    } else {
      if (is_closed) {
        data.expected_amount = expected_amount;
      } else {
        if (amount && ["BRI", "BJB", "MANDIRI"].includes(bank_code)) {
          data.suggested_amount = amount;
        }
        if (min_amount && ["BNC", "CIMB"].includes(bank_code)) {
          data.min_amount = min_amount;
        }
        if (max_amount && ["BNC", "CIMB"].includes(bank_code)) {
          data.max_amount = max_amount;
        }
      }
    }
    if (description && ["BRI", "BSI"].includes(bank_code)) {
      data.description = description;
    }
    if (expiration_date) {
      data.expiration_date = expiration_date;
    }
    if (this.env.APP_URL) {
      data.callback_url = `${this.env.APP_URL}/api/xendit/callback`;
    }
    console.log("Creating Virtual Account:", data);
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey)}`
      },
      body: JSON.stringify(data)
    });
    const responseData = await response.json();
    console.log("Xendit VA Response:", responseData);
    if (!response.ok) {
      console.error("Xendit VA API Error:", {
        status: response.status,
        data: responseData,
        requestData: data
      });
      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }
      throw new Error(
        responseData.message || "Failed to create virtual account"
      );
    }
    return responseData;
  }
  async createPaymentRequest({
    amount,
    currency,
    payment_method,
    customer_id,
    description,
    metadata,
    reference_id,
    idempotency_key
  }) {
    const url = `${this.baseUrl}/payment_requests`;
    const data = {
      amount,
      currency,
      payment_method,
      customer_id,
      description,
      metadata,
      reference_id
    };
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Basic ${btoa(this.secretKey + ":")}`
    };
    if (idempotency_key) {
      headers["idempotency-key"] = idempotency_key;
    }
    console.log("Using API key:", this.secretKey);
    console.log("Request URL:", url);
    console.log("Request headers:", headers);
    console.log("Request data:", data);
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(data)
    });
    const responseData = await response.json();
    if (!response.ok) {
      console.error("Xendit Payment Request API Error:", {
        status: response.status,
        data: responseData,
        requestData: data
      });
      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }
      throw new Error(
        responseData.message || "Failed to create payment request"
      );
    }
    return responseData;
  }
  async createCustomer({
    reference_id,
    type = "INDIVIDUAL",
    email,
    given_names,
    surname,
    mobile_number,
    addresses = [],
    description,
    metadata = {}
  }) {
    console.log("XenditService: Creating customer with data:", {
      reference_id,
      type,
      email,
      given_names,
      surname,
      mobile_number,
      description
    });
    const url = `${this.baseUrl}/customers`;
    console.log("XenditService: Using URL:", url);
    const data = {
      reference_id,
      type,
      email,
      given_names,
      mobile_number,
      metadata
    };
    if (surname)
      data.surname = surname;
    if (addresses.length > 0)
      data.addresses = addresses;
    if (description)
      data.description = description;
    console.log("XenditService: Final request data:", data);
    console.log(
      "XenditService: Using API key:",
      this.secretKey.substring(0, 8) + "..."
    );
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
        "api-version": "2020-05-19"
      },
      body: JSON.stringify(data)
    });
    const responseData = await response.json();
    console.log("XenditService: Raw API Response:", responseData);
    console.log("XenditService: Response status:", response.status);
    console.log(
      "XenditService: Response headers:",
      Object.fromEntries(response.headers)
    );
    if (!response.ok) {
      console.error("XenditService: API Error:", {
        status: response.status,
        data: responseData,
        requestData: data
      });
      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }
      throw new Error(responseData.message || "Failed to create customer");
    }
    return responseData;
  }
};
__name(XenditService, "XenditService");

// src/controllers/xenditController.js
var import_qrcode = __toESM(require_browser());
var XenditController = class {
  constructor(env) {
    this.env = env;
    this.xenditService = new XenditService(env);
  }
  async createInvoice(req, res) {
    try {
      const invoice = await this.xenditService.createInvoice(req.body);
      return res.status(201).json({ success: true, data: invoice });
    } catch (error) {
      console.error("Invoice creation error:", error);
      return res.status(500).json({ success: false, error: error.message });
    }
  }
  async createQRISPayment(request) {
    try {
      const data = await request.json();
      const { external_id, amount, description } = data;
      if (!external_id || !amount) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "external_id and amount are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const qrisPayment = await this.xenditService.createQRISPayment({
        external_id,
        amount,
        description
      });
      return new Response(
        JSON.stringify({
          success: true,
          data: qrisPayment
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error creating QRIS payment:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create QRIS payment"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async generateQRCode(request) {
    try {
      const { qr_string } = await request.json();
      if (!qr_string) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "qr_string is required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const qrBuffer = await import_qrcode.default.toBuffer(qr_string, {
        errorCorrectionLevel: "H",
        margin: 1,
        scale: 8
      });
      return new Response(qrBuffer, {
        status: 200,
        headers: {
          "Content-Type": "image/png",
          "Content-Length": qrBuffer.length.toString()
        }
      });
    } catch (error) {
      console.error("QR Code Generation Error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to generate QR code image"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async createVirtualAccount(request) {
    try {
      const data = await request.json();
      const {
        external_id,
        bank_code,
        name,
        amount,
        is_closed = true,
        expected_amount,
        min_amount,
        max_amount,
        description,
        is_single_use,
        currency
      } = data;
      if (!external_id || !bank_code || !name) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "external_id, bank_code, and name are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const vaPayment = await this.xenditService.createVirtualAccount({
        external_id,
        bank_code,
        name,
        amount,
        is_closed,
        expected_amount: expected_amount || amount,
        // Use expected_amount if provided, otherwise use amount
        min_amount,
        max_amount,
        description,
        is_single_use,
        currency,
        expiration_date: new Date(
          Date.now() + 24 * 60 * 60 * 1e3
        ).toISOString()
        // 24 hours from now
      });
      return new Response(
        JSON.stringify({
          success: true,
          data: vaPayment
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error creating Virtual Account:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create virtual account"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async createPaymentRequest(request) {
    try {
      const data = await request.json();
      const {
        amount,
        currency,
        payment_method,
        customer_id,
        description,
        metadata,
        reference_id
      } = data;
      if (!amount || !currency || !payment_method || !customer_id) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "amount, currency, payment_method, and customer_id are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      const idempotencyKey = request.headers.get("idempotency-key");
      const paymentRequest = await this.xenditService.createPaymentRequest({
        amount,
        currency,
        payment_method,
        customer_id,
        description,
        metadata,
        reference_id,
        idempotency_key: idempotencyKey
      });
      return new Response(
        JSON.stringify({
          success: true,
          data: paymentRequest
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error creating payment request:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create payment request"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async createCustomer(request) {
    try {
      console.log("Received customer creation request");
      const rawBody = await request.text();
      console.log("Raw request body:", rawBody);
      let data;
      try {
        data = JSON.parse(rawBody);
      } catch (parseError) {
        console.error("Error parsing JSON:", parseError);
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid JSON format in request body"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      console.log("Parsed request data:", data);
      const {
        reference_id,
        type,
        email,
        given_names,
        surname,
        mobile_number,
        addresses,
        description,
        metadata
      } = data;
      console.log("Extracted fields:", {
        reference_id,
        type,
        email,
        given_names,
        surname,
        mobile_number,
        description
      });
      if (!reference_id || !email || !given_names) {
        console.log("Validation failed - missing required fields");
        return new Response(
          JSON.stringify({
            success: false,
            error: "reference_id, email, and given_names are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      console.log("Creating customer with Xendit service");
      const customer = await this.xenditService.createCustomer({
        reference_id,
        type,
        email,
        given_names,
        surname,
        mobile_number,
        addresses,
        description,
        metadata
      });
      console.log("Customer created successfully:", customer);
      return new Response(
        JSON.stringify({
          success: true,
          data: customer
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error creating customer:", error);
      console.error("Error stack:", error.stack);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create customer"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
  async validateDirectDebitOTP(request) {
    try {
      const requestUrl = new URL(request.url);
      const pathParts = requestUrl.pathname.split("/");
      const payment_method_id = pathParts[pathParts.length - 2];
      const body = await request.json();
      const { otp_code } = body;
      if (!payment_method_id || !otp_code) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "payment_method_id and otp_code are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      console.log("Validating OTP for payment method:", payment_method_id);
      const xenditUrl = `${this.xenditService.baseUrl}/v2/payment_methods/${payment_method_id}/auth`;
      const response = await fetch(xenditUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Basic ${btoa(this.xenditService.secretKey + ":")}`,
          "api-version": "2020-05-19"
        },
        body: JSON.stringify({ otp_code })
      });
      const responseData = await response.json();
      console.log("OTP validation response:", responseData);
      if (!response.ok) {
        return new Response(
          JSON.stringify({
            success: false,
            error: responseData.message || "Failed to validate OTP"
          }),
          {
            status: response.status,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      return new Response(
        JSON.stringify({
          success: true,
          data: responseData
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error validating OTP:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to validate OTP"
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};
__name(XenditController, "XenditController");

// src/routes/xenditRoutes.js
function createXenditRouter(env) {
  const router2 = e({ base: "/api/xendit" });
  const controller = new XenditController(env);
  router2.post("/customer", async (request) => {
    console.log("Xendit Router: Handling customer creation request");
    return controller.createCustomer(request);
  });
  router2.post("/invoice", (request) => controller.createInvoice(request));
  router2.post("/qris", (request) => controller.createQRISPayment(request));
  router2.post(
    "/virtual-account",
    (request) => controller.createVirtualAccount(request)
  );
  router2.post("/qris/qr-code", (request) => controller.generateQRCode(request));
  router2.post(
    "/payment-request",
    (request) => controller.createPaymentRequest(request)
  );
  router2.post(
    "/payment-methods/:payment_method_id/auth",
    (request) => controller.validateDirectDebitOTP(request)
  );
  router2.all("*", () => {
    console.log("Xendit Router: Route not found");
    return new Response(
      JSON.stringify({
        success: false,
        error: "Endpoint not found"
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" }
      }
    );
  });
  return router2;
}
__name(createXenditRouter, "createXenditRouter");

// src/index.js
var corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  // In production, replace with your frontend domain
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, x-sps-key",
  "Access-Control-Max-Age": "86400"
};
function handleOptions(request) {
  return new Response(null, {
    headers: corsHeaders
  });
}
__name(handleOptions, "handleOptions");
function addCorsHeaders(response) {
  const newHeaders = new Headers(response.headers);
  Object.entries(corsHeaders).forEach(([key, value]) => {
    newHeaders.set(key, value);
  });
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  });
}
__name(addCorsHeaders, "addCorsHeaders");
var router = e();
async function initializeTierSettings(env) {
  const tierService = new TierService2(env);
  await tierService.initializeTierSettings();
  console.log("Tier settings initialized");
}
__name(initializeTierSettings, "initializeTierSettings");
async function processEmailQueue(env) {
  try {
    console.log("Starting scheduled email queue processing...");
    const emailQueueService = new EmailQueueService(env);
    const beforeStatus = await emailQueueService.getQueueStatus();
    console.log("Queue status before processing:", beforeStatus);
    const results = await emailQueueService.processQueue(true);
    console.log("Queue processing results:", results);
    const afterStatus = await emailQueueService.getQueueStatus();
    console.log("Queue status after processing:", afterStatus);
    return { beforeStatus, results, afterStatus };
  } catch (error) {
    console.error("Error processing email queue:", error);
    throw error;
  }
}
__name(processEmailQueue, "processEmailQueue");
router.options("*", handleOptions);
router.get("/api/docs", () => {
  return addCorsHeaders(
    new Response(getSwaggerHTML(swaggerDocument), {
      headers: {
        "content-type": "text/html;charset=UTF-8"
      }
    })
  );
});
router.all("/api/subscriptions/*", async (request, env) => {
  try {
    const subscriptionRouter = createSubscriptionRouter(env);
    const response = await subscriptionRouter.handle(request);
    return addCorsHeaders(response);
  } catch (error) {
    console.error("Subscription route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      )
    );
  }
});
router.all("/api/tiers/*", async (request, env) => {
  const tierRouter = createTierRouter(env);
  const response = await tierRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/usage/*", async (request, env) => {
  const usageRouter = createUsageRouter(env);
  const response = await usageRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/users/*", async (request, env) => {
  const userRouter = createUserRouter(env);
  const response = await userRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/xendit/*", async (request, env) => {
  const xenditRouter = createXenditRouter(env);
  const response = await xenditRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/debug/*", async (request, env) => {
  try {
    const debugRouter = createDebugRouter(env);
    const response = await debugRouter.handle(request);
    return addCorsHeaders(response);
  } catch (error) {
    console.error("Debug route error:", error);
    return addCorsHeaders(
      new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      )
    );
  }
});
router.all("/api/test/webhooks/*", async (request, env) => {
  const testRouter = createWebhookTestRouter(env);
  const response = await testRouter.handle(request);
  return addCorsHeaders(response);
});
router.all("/api/webhooks/*", async (request, env) => {
  const webhookRouter = createWebhookRouter(env);
  const response = await webhookRouter.handle(request);
  return addCorsHeaders(response);
});
router.all(
  "*",
  () => addCorsHeaders(new Response("Not Found", { status: 404 }))
);
var src_default = {
  // Regular request handler
  async fetch(request, env, ctx) {
    try {
      await initializeTierSettings(env);
      if (request.method === "OPTIONS") {
        return handleOptions(request);
      }
      if (request.method === "POST" && request.url.endsWith("/api/debug/process-queue")) {
        const results = await processEmailQueue(env);
        return addCorsHeaders(
          new Response(
            JSON.stringify({
              success: true,
              message: "Queue processed manually",
              ...results
            }),
            {
              headers: { "Content-Type": "application/json" }
            }
          )
        );
      }
      const response = await router.handle(request, env, ctx);
      return addCorsHeaders(response);
    } catch (error) {
      console.error("Worker error:", error);
      return addCorsHeaders(
        new Response(
          JSON.stringify({
            success: false,
            error: error.message
          }),
          {
            status: 500,
            headers: { "Content-Type": "application/json" }
          }
        )
      );
    }
  },
  // Scheduled handler for cron
  async scheduled(event, env, ctx) {
    console.log(`Cron triggered: ${event.cron} at ${(/* @__PURE__ */ new Date()).toISOString()}`);
    try {
      ctx.waitUntil(processEmailQueue(env));
    } catch (error) {
      console.error("Scheduled job error:", error);
    }
  },
  // Add the queue handler
  queue: emailQueueHandler_default.queue
};

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
init_checked_fetch();
init_modules_watch_stub();
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e2) {
      console.error("Failed to drain the unused request body.", e2);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-scheduled.ts
init_checked_fetch();
init_modules_watch_stub();
var scheduled = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  const url = new URL(request.url);
  if (url.pathname === "/__scheduled") {
    const cron = url.searchParams.get("cron") ?? "";
    await middlewareCtx.dispatch("scheduled", { cron });
    return new Response("Ran scheduled event");
  }
  const resp = await middlewareCtx.next(request, env);
  if (request.headers.get("referer")?.endsWith("/__scheduled") && url.pathname === "/favicon.ico" && resp.status === 500) {
    return new Response(null, { status: 404 });
  }
  return resp;
}, "scheduled");
var middleware_scheduled_default = scheduled;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
init_checked_fetch();
init_modules_watch_stub();
function reduceError(e2) {
  return {
    name: e2?.name,
    message: e2?.message ?? String(e2),
    stack: e2?.stack,
    cause: e2?.cause === void 0 ? void 0 : reduceError(e2.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e2) {
    const error = reduceError(e2);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-xQ5NsH/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_scheduled_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
init_checked_fetch();
init_modules_watch_stub();
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-xQ5NsH/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
