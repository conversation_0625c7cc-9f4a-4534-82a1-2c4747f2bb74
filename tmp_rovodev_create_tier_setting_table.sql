-- Create t_tier_setting table for individual tier configurations
CREATE TABLE IF NOT EXISTS t_tier_setting (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    max_quota INTEGER NOT NULL,
    images_quota INTEGER NOT NULL,
    content_quota INTEGER NOT NULL,
    title_quota INTEGER NOT NULL,
    domain_limit INTEGER NOT NULL, -- -1 for unlimited
    price REAL NOT NULL,
    expiration_days INTEGER NOT NULL,
    addon1 BOOLEAN DEFAULT FALSE,
    addon2 BOOLEAN DEFAULT FALSE,
    addon1_price REAL NOT NULL,
    addon2_price REAL NOT NULL,
    features TEXT NOT NULL, -- JSON array of features
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_t_tier_setting_id ON t_tier_setting(id);
CREATE INDEX IF NOT EXISTS idx_t_tier_setting_price ON t_tier_setting(price);