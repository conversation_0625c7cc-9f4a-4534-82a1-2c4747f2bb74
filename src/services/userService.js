import { EmailService } from "./emailService.js";
import { DatabaseService } from "./databaseService.js";
import { EmailQueueService } from "./emailQueueServiceD1.js";

export class UserService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
    this.emailService = new EmailService(env);
    this.emailQueueService = new EmailQueueServiceD1(env);
    // Keep KV for backward compatibility during migration
    this.kv = env.USERS_KV;
  }

  _generatePassword() {
    try {
      // Generate a strong random password
      const length = 12;
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const numbers = "0123456789";
      const special = "!@#$%^&*";

      let password = "";

      // Add at least one of each type
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += special[Math.floor(Math.random() * special.length)];

      // Complete the rest of the password
      const allChars = lowercase + uppercase + numbers + special;
      for (let i = password.length; i < length; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      // Shuffle the password
      return password
        .split("")
        .sort(() => 0.5 - Math.random())
        .join("");
    } catch (error) {
      console.error("Error generating password:", error);
      // Fallback password generation
      return `Pass${Math.random().toString(36).substring(2, 10)}!`;
    }
  }

  async createUser(userData) {
    try {
      // Input validation
      if (!userData.domain || !userData.email) {
        throw new Error("Domain and email are required");
      }

      // Normalize email and domain
      const normalizedEmail = userData.email.toLowerCase().trim();
      const normalizedDomain = userData.domain.toLowerCase().trim();

      // Check if domain exists globally
      const existingDomain = await this.env.USERS_KV.get(
        `domain:${normalizedDomain}`,
        "json"
      );

      if (existingDomain) {
        // Instead of throwing an error, get the existing user data and send an email
        const existingUser = await this.env.USERS_KV.get(
          `user:${existingDomain.userId}`,
          "json"
        );

        if (existingUser) {
          // Get the existing user's credentials
          const credentials = await this.env.USERS_KV.get(
            `credentials:${existingUser.id}`,
            "json"
          );

          // Queue email with domain registration information
          try {
            const emailQueueId = await this.emailQueueService.addToQueue({
              email: existingDomain.email, // Send to the email that owns the domain
              username: existingDomain.email.split("@")[0],
              domain: normalizedDomain,
              existingEmail: existingDomain.email, // The email that owns the domain
              apiKey: existingDomain.api_key,
              password: credentials ? credentials.plainPassword : undefined, // Include the password if available
              type: "domain_already_registered",
              attemptedEmail: normalizedEmail, // Store the email that attempted registration
            });

            console.log(
              "📬 Domain already registered email queued:",
              emailQueueId
            );

            // Return a response with information about the existing registration
            return {
              success: false,
              message:
                "Domain is already registered. Each domain can only have one API key. An email with details has been sent to the registered email address.",
              domain: normalizedDomain,
              registeredTo: existingDomain.email,
            };
          } catch (error) {
            console.error(
              "❌ Error queueing domain already registered email:",
              error
            );
          }
        }

        // If we couldn't find the existing user or send the email, throw the original error
        throw new Error(
          "Domain is already registered. Each domain can only have one API key."
        );
      }

      // Get existing user by email
      let user = await this.env.USERS_KV.get(
        `email:${normalizedEmail}`,
        "json"
      );

      const apiKey = crypto.randomUUID();
      let plainPassword;
      let hashedPassword;

      if (user) {
        // User exists, add new domain - get hashed password from credentials (no plain needed)
        const credentials = await this.env.USERS_KV.get(
          `credentials:${user.id}`,
          "json"
        );

        hashedPassword = credentials.hashedPassword;

        const newDomain = {
          domain: normalizedDomain,
          api_key: apiKey,
          status: "pending",
          createdAt: new Date().toISOString(),
        };

        if (!user.domains) {
          user.domains = [];
        }
        user.domains.push(newDomain);

        // Store API key mapping
        await this.env.USERS_KV.put(`apikey:${apiKey}`, user.id);
      } else {
        // First time user - generate new password
        plainPassword = this._generatePassword();
        hashedPassword = await this._hashPassword(plainPassword);

        // Create new user with first domain
        user = {
          id: crypto.randomUUID(),
          email: normalizedEmail,
          password: hashedPassword,
          domains: [
            {
              domain: normalizedDomain,
              api_key: apiKey,
              status: "pending",
              createdAt: new Date().toISOString(),
            },
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Store API key mapping
        await this.env.USERS_KV.put(`apikey:${apiKey}`, user.id);
      }

      // Store credentials separately for verification
      const credentials = {
        hashedPassword,
        userId: user.id,
        email: user.email,
        createdAt: user.createdAt,
      };

      // Initialize tier data for new user
      const tierData = {
        tier: "starter",
        email: user.email,
        userId: user.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        additionalQuotas: {
          images: 0,
          content: 0,
          title: 0,
        },
      };

      // Store in KV
      await Promise.all([
        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
        this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
        this.env.USERS_KV.put(
          `domain:${normalizedDomain}`,
          JSON.stringify({
            userId: user.id,
            email: user.email,
            domain: normalizedDomain,
            api_key: apiKey,
          })
        ),
        this.env.USERS_KV.put(
          `api_key:${apiKey}`,
          JSON.stringify({
            userId: user.id,
            email: user.email,
            domain: normalizedDomain,
            api_key: apiKey,
          })
        ),
        this.env.USERS_KV.put(
          `credentials:${user.id}`,
          JSON.stringify(credentials)
        ),
        this.env.USERS_KV.put(
          `t_setting:email_tier:${user.email}`,
          JSON.stringify(tierData)
        ),
      ]);

      // Queue welcome email with plain password only for new users
      try {
        const emailQueueId = await this.emailQueueService.addToQueue({
          email: user.email,
          username: user.email.split("@")[0],
          domain: normalizedDomain,
          password: plainPassword, // Only set for new users; will be undefined for existing
          apiKey: apiKey,
          type:
            user.domains && user.domains.length > 1 ? "new_domain" : "welcome",
        });

        console.log("📬 Email queued:", emailQueueId);
      } catch (error) {
        console.error("❌ Error queueing email:", error);
      }

      // Return response
      return {
        id: user.id,
        email: user.email,
        domain: normalizedDomain,
        credentials: {
          apiKey: apiKey,
          // Only include password for first-time users
          ...((!user.domains || user.domains.length === 1) && {
            password: plainPassword,
          }),
        },
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch (error) {
      console.error("Error in createUser:", error);
      throw error;
    }
  }

  async _hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hash = await crypto.subtle.digest("SHA-256", data);
    return Array.from(new Uint8Array(hash))
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("");
  }

  // Helper method to get plain password from credentials
  async _getPlainPasswordFromCredentials(credentials) {
    // For existing users, return the stored plain password
    return credentials.plainPassword;
  }

  async getUserDetail(apiKey) {
    // Get user ID from API key mapping
    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("User not found");
    }

    // Get user details
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }

    // Find the domain using this API key
    const domain = user.domains?.find((d) => d.api_key === apiKey);
    if (!domain) {
      throw new Error("User not found");
    }

    return {
      id: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: domain.api_key,
      status: domain.status,
      createdAt: domain.createdAt,
      updatedAt: user.updatedAt,
      tier: user.tier,
      quota: user.quota,
    };
  }

  async verifyPassword(userId, password) {
    const credentials = await this.env.USERS_KV.get(
      `credentials:${userId}`,
      "json"
    );
    if (!credentials) {
      throw new Error("Credentials not found");
    }
    const hashedInput = await this._hashPassword(password);
    return hashedInput === credentials.hashedPassword;
  }

  async activateUser(license, domain) {
    try {
      console.log("🔧 UserService.activateUser called with:", { license, domain });
      
      // Get user by API key instead of license
      const apiKeyData = await this.env.USERS_KV.get(
        `api_key:${license}`,
        "json"
      );

      console.log("🔍 API key data found:", apiKeyData);

      if (!apiKeyData) {
        console.log("❌ No API key data found for license:", license);
        return false;
      }

      // Validate that the provided domain matches the registration
      const normalizedDomain = domain.toLowerCase().trim();
      console.log("🔍 Comparing domains:", { provided: normalizedDomain, registered: apiKeyData.domain });
      
      if (apiKeyData.domain !== normalizedDomain) {
        console.log("❌ Domain mismatch:", { provided: normalizedDomain, registered: apiKeyData.domain });
        return false;
      }

      // Get full user data
      const user = await this.env.USERS_KV.get(
        `user:${apiKeyData.userId}`,
        "json"
      );
      
      console.log("🔍 User data found:", user ? "Yes" : "No");
      
      if (!user) {
        console.log("❌ No user data found for userId:", apiKeyData.userId);
        return false;
      }

      // Update domain status in the domains array
      const updatedDomains = user.domains.map((d) => {
        if (d.api_key === license && d.domain === normalizedDomain) {
          return {
            ...d,
            status: "active",
            activatedAt: new Date().toISOString(),
          };
        }
        return d;
      });

      // Create updated user object
      const updatedUser = {
        ...user,
        domains: updatedDomains,
        updatedAt: new Date().toISOString(),
      };

      // Update API key data
      const updatedApiKeyData = {
        ...apiKeyData,
        status: "active",
        activatedAt: new Date().toISOString(),
      };

      // Update all references
      await Promise.all([
        this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(updatedUser)),
        this.env.USERS_KV.put(
          `email:${user.email}`,
          JSON.stringify(updatedUser)
        ),
        this.env.USERS_KV.put(
          `api_key:${license}`,
          JSON.stringify(updatedApiKeyData)
        ),
        this.env.USERS_KV.put(
          `domain:${normalizedDomain}`,
          JSON.stringify(updatedApiKeyData)
        ),
      ]);

      return true;
    } catch (error) {
      console.error("Error activating user:", error);
      return false;
    }
  }

  async getLicenseStatus(license) {
    try {
      const user = await this.env.USERS_KV.get(`api_key:${license}`, "json");

      if (!user) {
        return {
          isValid: false,
          message: "License not found",
        };
      }

      return {
        isValid: true,
        status: user.status || "pending",
        activatedAt: user.activatedAt || null,
        domain: user.domain,
        message:
          user.status === "active"
            ? "License is activated"
            : "License is pending activation",
      };
    } catch (error) {
      console.error("Error checking license status:", error);
      return {
        isValid: false,
        message: "Error checking license status",
      };
    }
  }

  _formatUserData(user) {
    const { password, ...userData } = user;
    return userData;
  }

  async loginUser(email, password) {
    // Normalize email
    const normalizedEmail = email.toLowerCase().trim();

    // Get user by email
    const user = await this.env.USERS_KV.get(
      `email:${normalizedEmail}`,
      "json"
    );
    if (!user) {
      throw new Error("Invalid email or password");
    }

    // Get credentials to verify password
    const credentials = await this.env.USERS_KV.get(
      `credentials:${user.id}`,
      "json"
    );
    if (!credentials) {
      throw new Error("Invalid email or password");
    }

    // Verify password
    const hashedInput = await this._hashPassword(password);
    if (hashedInput !== credentials.hashedPassword) {
      throw new Error("Invalid email or password");
    }

    // Get user's tier information
    const tierInfo = (await this.env.USERS_KV.get(
      `tier:${user.id}`,
      "json"
    )) || {
      currentTier: "starter",
      tierName: "Starter",
      usage: 0,
      imagesUsage: 0,
      contentUsage: 0,
      titleUsage: 0,
      maxQuota: 100,
      imagesQuota: 50,
      contentQuota: 30,
      titleQuota: 20,
      remainingImagesQuota: 50,
      remainingContentQuota: 30,
      remainingTitleQuota: 20,
      price: 0,
      features: ["Basic API Access", "Standard Support"],
      quotaPercentage: "0%",
    };

    // Calculate remaining quotas if not present
    if (!tierInfo.remainingImagesQuota) {
      tierInfo.remainingImagesQuota = Math.max(
        0,
        tierInfo.imagesQuota - tierInfo.imagesUsage
      );
    }
    if (!tierInfo.remainingContentQuota) {
      tierInfo.remainingContentQuota = Math.max(
        0,
        tierInfo.contentQuota - tierInfo.contentUsage
      );
    }
    if (!tierInfo.remainingTitleQuota) {
      tierInfo.remainingTitleQuota = Math.max(
        0,
        tierInfo.titleQuota - tierInfo.titleUsage
      );
    }

    // Calculate quota percentage
    const totalUsage =
      tierInfo.usage ||
      tierInfo.imagesUsage + tierInfo.contentUsage + tierInfo.titleUsage;
    const totalQuota =
      tierInfo.maxQuota ||
      tierInfo.imagesQuota + tierInfo.contentQuota + tierInfo.titleQuota;
    tierInfo.quotaPercentage =
      totalQuota > 0 ? `${Math.round((totalUsage / totalQuota) * 100)}%` : "0%";

    // Return user data with credentials and detailed tier info
    return {
      ...this._formatUserData(user),
      credentials: {
        password: password, // Include the original password in response
        apiKey: credentials.apiKey,
      },
      tier: tierInfo,
    };
  }

  async getDomains(email) {
    const normalizedEmail = email.toLowerCase().trim();
    const user = await this.env.USERS_KV.get(
      `email:${normalizedEmail}`,
      "json"
    );

    if (!user) {
      throw new Error("User not found");
    }

    // Ensure all API keys are properly stored
    if (user.domains && user.domains.length > 0) {
      await Promise.all(
        user.domains.map(async (domain) => {
          // Check if API key exists
          const apiKeyData = await this.env.USERS_KV.get(
            `api_key:${domain.api_key}`,
            "json"
          );

          // If API key doesn't exist or is malformed, repair it
          if (!apiKeyData || !apiKeyData.userId || !apiKeyData.id) {
            const repairData = {
              id: user.id,
              userId: user.id,
              email: user.email,
              domain: domain.domain,
              api_key: domain.api_key,
              status: domain.status || "pending",
              createdAt: domain.createdAt,
              activatedAt: domain.activatedAt,
              password: user.password,
            };

            // Update both api_key and domain entries
            await Promise.all([
              this.env.USERS_KV.put(
                `api_key:${domain.api_key}`,
                JSON.stringify(repairData)
              ),
              this.env.USERS_KV.put(
                `domain:${domain.domain}`,
                JSON.stringify(repairData)
              ),
            ]);

            console.log(`Repaired API key data for ${domain.api_key}`);
          }
        })
      );
    }

    // Add user ID to each domain object
    return (user.domains || []).map((domain) => ({
      ...domain,
      userId: user.id,
    }));
  }

  async removeDomain(email, domain) {
    const user = await this.env.USERS_KV.get(`email:${email}`, "json");
    if (!user) {
      throw new Error("User not found");
    }

    const domainIndex = user.domains.findIndex((d) => d.domain === domain);
    if (domainIndex === -1) {
      throw new Error("Domain not found for this user");
    }

    const removedDomain = user.domains[domainIndex];
    user.domains.splice(domainIndex, 1);
    user.updatedAt = new Date().toISOString();

    // Update KV store
    await Promise.all([
      this.env.USERS_KV.put(`user:${user.id}`, JSON.stringify(user)),
      this.env.USERS_KV.put(`email:${user.email}`, JSON.stringify(user)),
      this.env.USERS_KV.delete(`domain:${domain}`),
      this.env.USERS_KV.delete(`api_key:${removedDomain.api_key}`),
    ]);

    return true;
  }

  async getUserByEmail(email) {
    try {
      const normalizedEmail = email.toLowerCase().trim();
      const user = await this.env.USERS_KV.get(`email:${normalizedEmail}`, "json");
      return user;
    } catch (error) {
      console.error("Error getting user by email:", error);
      return null;
    }
  }

  async createPortalUser(userData) {
    try {
      const { email, password } = userData;
      const normalizedEmail = email.toLowerCase().trim();

      // Generate unique user ID
      const userId = `portal_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
      
      // Hash the password
      const hashedPassword = await this._hashPassword(password);

      // Create user object
      const user = {
        id: userId,
        email: normalizedEmail,
        type: "portal", // Mark as portal user
        hashedPassword: hashedPassword,
        status: "active",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        domains: [], // Portal users don't have domains by default
      };

      // Store user data
      await Promise.all([
        this.env.USERS_KV.put(`user:${userId}`, JSON.stringify(user)),
        this.env.USERS_KV.put(`email:${normalizedEmail}`, JSON.stringify(user)),
        // Store credentials for portal user
        this.env.USERS_KV.put(`portal_credentials:${userId}`, JSON.stringify({
          plainPassword: password, // Store plain password for portal users if needed
          hashedPassword: hashedPassword,
          createdAt: new Date().toISOString()
        }))
      ]);

      console.log("✅ Portal user created successfully:", normalizedEmail);

      return {
        id: userId,
        email: normalizedEmail,
        type: "portal",
        status: "active",
        createdAt: user.createdAt
      };

    } catch (error) {
      console.error("Error creating portal user:", error);
      throw error;
    }
  }
}
