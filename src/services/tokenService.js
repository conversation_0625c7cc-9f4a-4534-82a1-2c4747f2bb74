import jwt from "jsonwebtoken";

export class TokenService {
  constructor(env) {
    this.env = env;
  }

  generateToken(user) {
    if (!this.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not defined in environment variables.");
    }
    const payload = {
      id: user.id,
      email: user.email,
      type: "user",
      iss: "superuser.id",
      aud: "api_app",
    };

    return jwt.sign(payload, this.env.JWT_SECRET, { expiresIn: "24h" });
  }

  async verifyToken(token) {
    if (!this.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not defined in environment variables.");
    }
    try {
      const decoded = jwt.verify(token, this.env.JWT_SECRET);
      if (decoded.type !== "user") {
        throw new Error("Invalid token type.");
      }
      return decoded;
    } catch (error) {
      throw new Error("Invalid or expired token.");
    }
  }

  async generatePortalToken(userId) {
    if (!this.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not defined in environment variables.");
    }
    const payload = {
      userId,
      type: "portal_user",
      iss: "superuser.id", // Issuer
      aud: "portal_app", // Audience
    };

    return jwt.sign(payload, this.env.JWT_SECRET, { expiresIn: "8h" });
  }

  async generatePasswordResetToken(userId) {
    if (!this.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not defined in environment variables.");
    }
    const payload = {
      userId,
      type: "password_reset",
      iss: "superuser.id", // Issuer
      aud: "portal_app", // Audience
    };

    return jwt.sign(payload, this.env.JWT_SECRET, { expiresIn: "1h" });
  }

  async verifyPortalToken(token) {
    if (!this.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not defined in environment variables.");
    }
    try {
      const decoded = jwt.verify(token, this.env.JWT_SECRET);
      if (decoded.type !== "portal_user") {
        throw new Error("Invalid token type.");
      }
      return { success: true, data: decoded };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async verifyPasswordResetToken(token) {
    if (!this.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not defined in environment variables.");
    }
    try {
      const decoded = jwt.verify(token, this.env.JWT_SECRET);
      if (decoded.type !== "password_reset") {
        throw new Error("Invalid token type.");
      }
      return { success: true, data: decoded };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}