// src/services/apiKeyServiceD1.js
// Updated ApiKeyService to use D1 database instead of KV storage

import { DatabaseService } from "./databaseService.js";

export class ApiKeyService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
  }

  async validateApi<PERSON>ey(request) {
    const apiKey = request.headers.get("x-sps-key");
    const requestDomain = request.headers.get("x-sps-domain");

    if (!apiKey) {
      throw new Error("API Key is required in x-sps-key header");
    }

    if (!requestDomain) {
      throw new Error("Domain is required in x-sps-domain header");
    }

    // Get user ID directly from API key mapping
    const userId = await this.db.getUserIdByApiKey(apiKey);
    if (!userId) {
      throw new Error("Invalid API Key");
    }

    // Get user details with domains
    const user = await this.db.getUserWithDomains(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Find domain with matching API key
    const domain = user.domains?.find((domain) => domain.api_key === apiKey);
    if (!domain) {
      throw new Error("Invalid API Key");
    }

    // Validate if the request domain matches the registered domain
    if (domain.domain !== requestDomain) {
      throw new Error(
        "Failed activate api key, use the correct registered domain !"
      );
    }

    return {
      userId: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: apiKey,
    };
  }
}