export class XenditService {
  constructor(env) {
    console.log("XenditService: Initializing with environment:", {
      hasSecretKey: !!env.XENDIT_SECRET_KEY,
      secretKeyPrefix: env.XENDIT_SECRET_KEY
        ? env.XENDIT_SECRET_KEY.substring(0, 4)
        : "none",
    });

    this.env = env;
    this.secretKey = env.XENDIT_SECRET_KEY;

    if (!this.secretKey) {
      console.error("XenditService: XENDIT_SECRET_KEY is not configured");
      throw new Error(
        "XENDIT_SECRET_KEY is not configured in environment variables"
      );
    }

    if (!this.secretKey.startsWith("xnd_")) {
      console.error(
        "XenditService: Invalid API key format:",
        this.secretKey.substring(0, 4)
      );
      throw new Error(
        "Invalid Xendit API key format. Make sure you're using the correct API key from your Xendit dashboard"
      );
    }

    console.log(
      "XenditService: Successfully initialized with API key:",
      this.secretKey.substring(0, 8) + "..."
    );
    this.baseUrl = "https://api.xendit.co";
  }

  async createInvoice(invoiceData) {
    const url = `${this.baseUrl}/v2/invoices`;

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Basic ${btoa(this.secretKey + ":")}`,
    };

    console.log("Using API key:", this.secretKey);
    console.log("Request URL:", url);
    console.log("Request headers:", headers);
    console.log("Request data:", invoiceData);

    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(invoiceData),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Invoice API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to create invoice");
    }

    return responseData;
  }

  async createQRISPayment({
    external_id,
    amount,
    description,
    type = "DYNAMIC",
    currency = "IDR",
  }) {
    const url = `${this.baseUrl}/qr_codes`;

    // Match the exact format from Xendit API docs
    const data = {
      reference_id: external_id,
      type,
      currency,
      amount,
    };

    // Add optional fields if provided
    if (description) data.description = description;
    if (this.env.APP_URL) {
      data.callback_url = `${this.env.APP_URL}/api/xendit/callback`;
    }

    console.log("Sending QRIS request to:", url);
    console.log("Request data:", data);
    console.log("Using API Key:", this.secretKey.substring(0, 8) + "...");

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey)}`,
        "api-version": "2022-07-31",
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();
    console.log("Xendit API Response:", responseData);

    if (!response.ok) {
      console.error("Xendit QRIS API Error:", {
        status: response.status,
        data: responseData,
        requestData: data,
      });

      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }

      throw new Error(responseData.message || "Failed to create QRIS payment");
    }

    return responseData;
  }

  async createVirtualAccount({
    external_id,
    bank_code,
    name,
    amount,
    is_closed = true,
    expiration_date,
    expected_amount = amount,
    min_amount,
    max_amount,
    description,
    is_single_use = false,
    currency = "IDR",
  }) {
    const url = `${this.baseUrl}/callback_virtual_accounts`;

    // Base data that works for all banks
    const data = {
      external_id,
      bank_code,
      name,
      is_closed,
      is_single_use,
      currency,
    };

    // Handle BCA specific requirements
    if (bank_code === "BCA") {
      const BCA_MIN_AMOUNT = 50000; // Updated minimum amount for BCA

      // For closed VA
      if (is_closed) {
        if (amount < BCA_MIN_AMOUNT) {
          throw new Error(
            `BCA virtual accounts require a minimum amount of ${BCA_MIN_AMOUNT} IDR`
          );
        }
        data.expected_amount = amount;
      }
      // For open VA
      else {
        // Set minimum amount for BCA
        data.min_amount = Math.max(
          min_amount || BCA_MIN_AMOUNT,
          BCA_MIN_AMOUNT
        );
        if (max_amount) {
          data.max_amount = max_amount;
        }
      }
    }
    // Handle BNI specific requirements
    else if (bank_code === "BNI") {
      if (is_closed) {
        data.expected_amount = expected_amount || amount;
      } else {
        // For open BNI VA, suggested_amount is NOT supported
        // BNI open VA only supports min/max amounts if provided
        if (min_amount) {
          data.min_amount = min_amount;
        }
        if (max_amount) {
          data.max_amount = max_amount;
        }
        // Do NOT set suggested_amount for BNI open VA
      }
    }
    // Handle other banks
    else {
      // Add amount-related fields
      if (is_closed) {
        data.expected_amount = expected_amount;
      } else {
        // For open virtual accounts, use amount as suggested_amount only for supported banks
        // Note: BNI does NOT support suggested_amount for open VA
        if (amount && ["BRI", "BJB", "MANDIRI"].includes(bank_code)) {
          data.suggested_amount = amount;
        }
        // Add min/max amounts for supported banks
        if (min_amount && ["BNC", "CIMB", "BNI"].includes(bank_code)) {
          data.min_amount = min_amount;
        }
        if (max_amount && ["BNC", "CIMB", "BNI"].includes(bank_code)) {
          data.max_amount = max_amount;
        }
      }
    }

    // Add optional fields
    if (description && ["BRI", "BSI"].includes(bank_code)) {
      data.description = description;
    }
    if (expiration_date) {
      // Validate expiration date is in the future
      const expirationTime = new Date(expiration_date);
      const currentTime = new Date();
      
      if (expirationTime > currentTime) {
        data.expiration_date = expiration_date;
      } else {
        console.warn(`Expiration date ${expiration_date} is in the past, using default (24h from now)`);
        data.expiration_date = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
      }
    }
    if (this.env.APP_URL) {
      data.callback_url = `${this.env.APP_URL}/api/xendit/callback`;
    }

    console.log("Creating Virtual Account with final data:", JSON.stringify(data, null, 2));

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();
    console.log("Xendit VA Response:", responseData);

    if (!response.ok) {
      console.error("Xendit VA API Error:", {
        status: response.status,
        data: responseData,
        requestData: data,
      });

      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }

      throw new Error(
        responseData.message || "Failed to create virtual account"
      );
    }

    return responseData;
  }

  async createPaymentRequest({
    amount,
    currency,
    payment_method,
    customer_id,
    description,
    metadata,
    reference_id,
    idempotency_key,
  }) {
    const url = `${this.baseUrl}/payment_requests`;

    const data = {
      amount,
      currency,
      payment_method,
      customer_id,
      description,
      metadata,
      reference_id,
    };

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Basic ${btoa(this.secretKey + ":")}`,
    };

    // Add optional idempotency key if provided
    if (idempotency_key) {
      headers["idempotency-key"] = idempotency_key;
    }

    console.log("Using API key:", this.secretKey);
    console.log("Request URL:", url);
    console.log("Request headers:", headers);
    console.log("Request data:", data);

    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Payment Request API Error:", {
        status: response.status,
        data: responseData,
        requestData: data,
      });

      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }

      throw new Error(
        responseData.message || "Failed to create payment request"
      );
    }

    return responseData;
  }

  async createCustomer({
    reference_id,
    type = "INDIVIDUAL",
    email,
    given_names,
    surname,
    mobile_number,
    addresses = [],
    description,
    metadata = {},
  }) {
    console.log("XenditService: Creating customer with data:", {
      reference_id,
      type,
      email,
      given_names,
      surname,
      mobile_number,
      description,
    });

    const url = `${this.baseUrl}/customers`;
    console.log("XenditService: Using URL:", url);

    const data = {
      reference_id,
      type,
      email,
      given_names,
      mobile_number,
      metadata,
    };

    // Add optional fields if provided
    if (surname) data.surname = surname;
    if (addresses.length > 0) data.addresses = addresses;
    if (description) data.description = description;

    console.log("XenditService: Final request data:", data);
    console.log(
      "XenditService: Using API key:",
      this.secretKey.substring(0, 8) + "..."
    );

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
        "api-version": "2020-05-19",
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();
    console.log("XenditService: Raw API Response:", responseData);
    console.log("XenditService: Response status:", response.status);
    console.log(
      "XenditService: Response headers:",
      Object.fromEntries(response.headers)
    );

    if (!response.ok) {
      console.error("XenditService: API Error:", {
        status: response.status,
        data: responseData,
        requestData: data,
      });

      if (response.status === 401) {
        throw new Error(
          "Invalid Xendit API key. Please check your XENDIT_SECRET_KEY configuration"
        );
      }

      throw new Error(responseData.message || "Failed to create customer");
    }

    return responseData;
  }

  async getInvoiceStatus(invoiceId) {
    const url = `${this.baseUrl}/v2/invoices/${invoiceId}`;

    const headers = {
      Authorization: `Basic ${btoa(this.secretKey + ":")}`,
    };

    const response = await fetch(url, {
      method: "GET",
      headers,
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Get Invoice API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to get invoice status");
    }

    return responseData;
  }

  // Customer Management Methods
  async getCustomers({ limit = 10, after_id } = {}) {
    const url = `${this.baseUrl}/customers`;
    const params = new URLSearchParams();
    
    if (limit) params.append('limit', limit);
    if (after_id) params.append('after_id', after_id);

    const response = await fetch(`${url}?${params}`, {
      method: "GET",
      headers: {
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
        "api-version": "2020-05-19",
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Get Customers API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to get customers");
    }

    return responseData;
  }

  async getCustomer(customerId) {
    const url = `${this.baseUrl}/customers/${customerId}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
        "api-version": "2020-05-19",
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Get Customer API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to get customer");
    }

    return responseData;
  }

  async updateCustomer(customerId, data) {
    const url = `${this.baseUrl}/customers/${customerId}`;

    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
        "api-version": "2020-05-19",
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Update Customer API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to update customer");
    }

    return responseData;
  }

  // Invoice Management Methods
  async getInvoices({ limit = 10, after_id, statuses } = {}) {
    const url = `${this.baseUrl}/v2/invoices`;
    const params = new URLSearchParams();
    
    if (limit) params.append('limit', limit);
    if (after_id) params.append('after_id', after_id);
    if (statuses && statuses.length > 0) {
      statuses.forEach(status => params.append('statuses[]', status));
    }

    const response = await fetch(`${url}?${params}`, {
      method: "GET",
      headers: {
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Get Invoices API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to get invoices");
    }

    return responseData;
  }

  async updateInvoice(invoiceId, data) {
    const url = `${this.baseUrl}/v2/invoices/${invoiceId}`;

    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Update Invoice API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to update invoice");
    }

    return responseData;
  }

  async expireInvoice(invoiceId) {
    const url = `${this.baseUrl}/v2/invoices/${invoiceId}/expire!`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Expire Invoice API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to expire invoice");
    }

    return responseData;
  }

  // Balance & Account Methods
  async getBalance() {
    const url = `${this.baseUrl}/balance`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Get Balance API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to get balance");
    }

    return responseData;
  }

  async getAccount() {
    const url = `${this.baseUrl}/account`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Basic ${btoa(this.secretKey + ":")}`,
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Xendit Get Account API Error:", {
        status: response.status,
        data: responseData,
      });
      throw new Error(responseData.message || "Failed to get account info");
    }

    return responseData;
  }

}
