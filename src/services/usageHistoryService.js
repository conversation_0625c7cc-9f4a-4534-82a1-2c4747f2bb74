// src/services/usageHistoryService.js
export class UsageHistoryService {
  constructor(env) {
    this.env = env;
  }

  static KEYS = {
    HISTORY: "usage_history",
    DAILY: "daily_usage",
  };

  async trackUsage(userId, data, requestDomain) {
    try {
      const historyId = crypto.randomUUID();
      const timestamp = new Date().toISOString();
      const day = timestamp.split("T")[0]; // Get YYYY-MM-DD

      const historyEntry = {
        id: historyId,
        userId,
        type: data.type, // 'images' or 'content'
        source: data.source,
        timestamp,
        metadata: data.metadata || {},
        domain: requestDomain, // Store the domain from the request
      };

      // Store individual history entry
      await this.env.USERS_KV.put(
        `${UsageHistoryService.KEYS.HISTORY}:${userId}:${historyId}`,
        JSON.stringify(historyEntry)
      );

      // Update daily aggregation
      const dailyKey = `${UsageHistoryService.KEYS.DAILY}:${userId}:${day}`;
      const existingDaily = (await this.env.USERS_KV.get(dailyKey, "json")) || {
        date: day,
        images: 0,
        content: 0,
        details: [],
      };

      // Increment the appropriate counter
      existingDaily[data.type] += 1;
      // Add to details array, keeping only essential info
      existingDaily.details.push({
        id: historyId,
        type: data.type,
        source: data.source,
        time: timestamp,
        domain: requestDomain, // Include domain in daily details
      });

      await this.env.USERS_KV.put(dailyKey, JSON.stringify(existingDaily));

      return historyEntry;
    } catch (error) {
      console.error("Error tracking usage history:", error);
      throw error;
    }
  }

  async getUserHistory(userId, options = {}) {
    try {
      const {
        startDate,
        endDate = new Date().toISOString(),
        type,
        limit = 100,
        page = 1,
      } = options;

      // List all history entries for the user
      const { keys } = await this.env.USERS_KV.list({
        prefix: `${UsageHistoryService.KEYS.HISTORY}:${userId}:`,
      });

      // Fetch all entries
      const entries = await Promise.all(
        keys.map((key) => this.env.USERS_KV.get(key.name, "json"))
      );

      // Filter and sort entries
      let filteredEntries = entries
        .filter((entry) => {
          if (!entry) return false;

          const matchesType = !type || entry.type === type;
          const matchesDateRange =
            (!startDate || entry.timestamp >= startDate) &&
            entry.timestamp <= endDate;

          return matchesType && matchesDateRange;
        })
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Calculate pagination
      const totalEntries = filteredEntries.length;
      const totalPages = Math.ceil(totalEntries / limit);
      const offset = (page - 1) * limit;

      // Paginate results
      filteredEntries = filteredEntries.slice(offset, offset + limit);

      return {
        entries: filteredEntries.map((entry) => ({
          ...entry,
          domain: entry.domain || null, // Ensure domain is always included, even if null
        })),
        pagination: {
          total: totalEntries,
          totalPages,
          currentPage: page,
          limit,
        },
      };
    } catch (error) {
      console.error("Error getting user history:", error);
      throw error;
    }
  }

  async getDailyUsage(userId, startDate, endDate = new Date().toISOString()) {
    try {
      const { keys } = await this.env.USERS_KV.list({
        prefix: `${UsageHistoryService.KEYS.DAILY}:${userId}:`,
      });

      const dailyEntries = await Promise.all(
        keys
          .filter((key) => {
            const date = key.name.split(":")[3];
            return (
              (!startDate || date >= startDate) && date <= endDate.split("T")[0]
            );
          })
          .map((key) => this.env.USERS_KV.get(key.name, "json"))
      );

      return dailyEntries.sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error("Error getting daily usage:", error);
      throw error;
    }
  }

  async getDomainHistory(domain, options) {
    try {
      const historyKey = `usage_history:${domain}`;
      const history = (await this.env.USERS_KV.get(historyKey, "json")) || [];

      let filteredHistory = history;

      // Filter by type if specified
      if (options.type) {
        filteredHistory = filteredHistory.filter(
          (entry) => entry.type === options.type
        );
      }

      // Sort by timestamp descending (newest first)
      filteredHistory.sort(
        (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
      );

      return {
        history: filteredHistory,
      };
    } catch (error) {
      console.error("Error in getDomainHistory:", error);
      throw error;
    }
  }
}
