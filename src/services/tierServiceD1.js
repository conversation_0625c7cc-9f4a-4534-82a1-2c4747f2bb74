// src/services/tierServiceD1.js
// Updated TierService to use D1 database instead of KV storage

import { DatabaseService } from "./databaseService.js";

export class TierService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
  }

  // Tier definitions
  static TIERS = {
    STARTER: "starter",
    BASIC: "basic",
    PRO: "pro",
    ENTERPRISE: "enterprise",
  };

  // Default tier configuration
  static DEFAULT_TIERS = {
    [TierService.TIERS.STARTER]: {
      name: "Starter",
      maxQuota: 10,
      imagesQuota: 10,
      contentQuota: 10,
      titleQuota: 10,
      domainLimit: 1,
      price: 0,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 4.99,
      addon2_price: 9.99,
      features: ["Basic API access", "Community support", "1 domain"],
    },
    [TierService.TIERS.BASIC]: {
      name: "Basic",
      maxQuota: 100,
      imagesQuota: 100,
      contentQuota: 100,
      titleQuota: 100,
      domainLimit: 5,
      price: 15,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 9.99,
      addon2_price: 19.99,
      features: ["Increased quota", "Email support", "Up to 5 domains"],
    },
    [TierService.TIERS.PRO]: {
      name: "Pro",
      maxQuota: 500,
      imagesQuota: 500,
      contentQuota: 500,
      titleQuota: 500,
      domainLimit: 15,
      price: 40,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 19.99,
      addon2_price: 39.99,
      features: ["High quota", "Priority support", "Up to 15 domains"],
    },
    [TierService.TIERS.ENTERPRISE]: {
      name: "Enterprise",
      maxQuota: 2000,
      imagesQuota: 2000,
      contentQuota: 2000,
      titleQuota: 2000,
      domainLimit: -1,
      price: 99,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 29.99,
      addon2_price: 49.99,
      features: ["Maximum quota", "Priority support", "24/7 phone support", "Unlimited domains"],
    },
  };

  async initializeTierSettings() {
    try {
      // Check if tier settings already exist
      const existingSettings = await this.db.getTierSettings();
      
      if (!existingSettings) {
        console.log("🔄 Initializing tier settings...");
        
        await this.db.createOrUpdateTierSettings(
          TierService.DEFAULT_TIERS,
          "1.0"
        );
        
        console.log("✅ Tier settings initialized successfully");
      } else {
        console.log("✅ Tier settings already exist");
      }
    } catch (error) {
      console.error("Error initializing tier settings:", error);
      throw error;
    }
  }

  async getTierSettings() {
    try {
      const settings = await this.db.getTierSettings();
      
      if (!settings) {
        // Initialize with default settings if not found
        await this.initializeTierSettings();
        return await this.db.getTierSettings();
      }
      
      return settings;
    } catch (error) {
      console.error("Error getting tier settings:", error);
      throw error;
    }
  }

  async updateTierSettings(newConfig) {
    try {
      const version = new Date().toISOString();
      await this.db.createOrUpdateTierSettings(newConfig, version);
      
      console.log("✅ Tier settings updated successfully");
      return await this.db.getTierSettings();
    } catch (error) {
      console.error("Error updating tier settings:", error);
      throw error;
    }
  }

  async getUserTier(userId) {
    try {
      // First try to get from user_tiers table
      let userTier = await this.db.getUserTier(userId);
      
      if (!userTier) {
        // If not found, check if it's a portal user
        const user = await this.db.getUserById(userId);
        if (user && user.type === 'portal') {
          userTier = await this.db.getUserTier(userId);
        }
      }
      
      return userTier?.tier || 'free';
    } catch (error) {
      console.error("Error getting user tier:", error);
      return 'free'; // Default to free tier on error
    }
  }

  async getEmailTier(email) {
    try {
      const emailTier = await this.db.getEmailTier(email);
      return emailTier?.tier || 'free';
    } catch (error) {
      console.error("Error getting email tier:", error);
      return 'free'; // Default to free tier on error
    }
  }

  async getEmailFromUserId(userId) {
    try {
      // If userId starts with 'portal_', check KV store first for portal users
      if (userId.startsWith('portal_')) {
        try {
          const portalUser = await this.env.USERS_KV.get(`portal_user:${userId}`, "json");
          if (portalUser?.email) {
            return portalUser.email;
          }
        } catch (kvError) {
          console.warn("Could not check KV for portal user:", kvError);
        }
      }

      // Try to get user from D1 database for regular users
      try {
        let user = await this.db.getUserById(userId);
        if (user?.email) {
          return user.email;
        }
      } catch (dbError) {
        console.warn("Could not check D1 for user:", dbError);
      }

      return null;
    } catch (error) {
      console.error("Error getting email from user ID:", error);
      return null;
    }
  }

  async getQuotaUsage(userId, type) {
    try {
      const usage = await this.db.getQuotaUsage(userId, type);
      return usage || { count: 0, last_reset: null, reset_date: null };
    } catch (error) {
      console.error("Error getting quota usage:", error);
      return { count: 0, last_reset: null, reset_date: null };
    }
  }

  async incrementQuotaUsage(userId, type) {
    try {
      const currentUsage = await this.getQuotaUsage(userId, type);
      const newCount = (currentUsage.count || 0) + 1;
      
      const usageData = {
        count: newCount,
        last_reset: currentUsage.last_reset,
        reset_date: currentUsage.reset_date
      };
      
      await this.db.updateQuotaUsage(userId, type, usageData);
      
      return newCount;
    } catch (error) {
      console.error("Error incrementing quota usage:", error);
      throw error;
    }
  }

  async getUserQuotaInfo(userId) {
    try {
      const user = await this.db.getUserById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      const tier = await this.getUserTier(userId);
      const tierSettings = await this.getTierSettings();
      const tierConfig = tierSettings.config[tier];

      if (!tierConfig) {
        throw new Error(`Tier configuration not found for: ${tier}`);
      }

      // Get quota usage for all types
      const imageUsage = await this.getQuotaUsage(userId, 'images');
      const contentUsage = await this.getQuotaUsage(userId, 'content');
      const titleUsage = await this.getQuotaUsage(userId, 'title');

      return {
        tier: tier,
        quota: {
          resetDate: imageUsage.reset_date || new Date(Date.now() + tierConfig.expirationDays * 24 * 60 * 60 * 1000).toISOString(),
          usage: {
            images: imageUsage.count || 0,
            content: contentUsage.count || 0,
            title: titleUsage.count || 0
          },
          limits: {
            images: tierConfig.imagesQuota || tierConfig.maxQuota,
            content: tierConfig.contentQuota || tierConfig.maxQuota,
            title: tierConfig.titleQuota || tierConfig.maxQuota
          }
        }
      };
    } catch (error) {
      console.error("Error getting user quota info:", error);
      throw error;
    }
  }

  async checkAndIncrementQuota(apiKey, type) {
    try {
      // Get user ID from API key
      const userId = await this.db.getUserIdByApiKey(apiKey);
      if (!userId) {
        throw new Error("Invalid API key");
      }

      // Get user quota info
      const quotaInfo = await this.getUserQuotaInfo(userId);
      const currentUsage = quotaInfo.quota.usage[type] || 0;
      const limit = quotaInfo.quota.limits[type] || 0;

      // Check if quota exceeded
      if (currentUsage >= limit) {
        throw new Error(`${type} quota exceeded. Current: ${currentUsage}, Limit: ${limit}`);
      }

      // Increment usage
      await this.incrementQuotaUsage(userId, type);

      return {
        success: true,
        usage: currentUsage + 1,
        limit: limit,
        remaining: limit - (currentUsage + 1)
      };
    } catch (error) {
      console.error("Error checking and incrementing quota:", error);
      throw error;
    }
  }

  async setUserTier(userId, tier) {
    try {
      await this.db.setUserTier(userId, tier);
      console.log(`✅ User tier set: ${userId} -> ${tier}`);
      return true;
    } catch (error) {
      console.error("Error setting user tier:", error);
      throw error;
    }
  }

  async setEmailTier(email, tier) {
    try {
      await this.db.setEmailTier(email, tier);
      console.log(`✅ Email tier set: ${email} -> ${tier}`);
      return true;
    } catch (error) {
      console.error("Error setting email tier:", error);
      throw error;
    }
  }

  async upgradeUserTier(userId, newTier, subscriptionData = null) {
    try {
      console.log(`🔄 Upgrading user ${userId} to ${newTier}`);

      // Set user tier
      await this.setUserTier(userId, newTier);

      // Update all user domains to new tier
      const domains = await this.db.getDomainsByUserId(userId);
      for (const domain of domains) {
        await this.db.updateDomain(domain.id, {
          status: domain.status,
          tier: newTier,
          activated_at: domain.activated_at
        });
      }

      // If subscription data provided, create subscription record
      if (subscriptionData) {
        await this.db.createSubscription({
          ...subscriptionData,
          user_id: userId,
          tier: newTier,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }

      console.log(`✅ User tier upgraded successfully: ${userId} -> ${newTier}`);
      return true;
    } catch (error) {
      console.error("Error upgrading user tier:", error);
      throw error;
    }
  }

  async resetUserQuota(userId) {
    try {
      console.log(`🔄 Resetting quota for user: ${userId}`);

      const tier = await this.getUserTier(userId);
      const tierSettings = await this.getTierSettings();
      const tierConfig = tierSettings.config[tier];

      if (!tierConfig) {
        throw new Error(`Tier configuration not found for: ${tier}`);
      }

      const resetData = {
        count: 0,
        last_reset: new Date().toISOString(),
        reset_date: new Date(Date.now() + tierConfig.expirationDays * 24 * 60 * 60 * 1000).toISOString()
      };

      // Reset all quota types
      await Promise.all([
        this.db.updateQuotaUsage(userId, 'images', resetData),
        this.db.updateQuotaUsage(userId, 'content', resetData),
        this.db.updateQuotaUsage(userId, 'title', resetData)
      ]);

      console.log(`✅ Quota reset successfully for user: ${userId}`);
      return {
        quotaReset: true,
        nextResetDate: resetData.reset_date
      };
    } catch (error) {
      console.error("Error resetting user quota:", error);
      throw error;
    }
  }

  async checkAndResetExpiredQuotas(userId) {
    try {
      const tier = await this.getUserTier(userId);
      const tierSettings = await this.getTierSettings();
      const tierConfig = tierSettings.config[tier];

      if (!tierConfig) {
        throw new Error(`Tier configuration not found for: ${tier}`);
      }

      // Get current quota usage
      const quotaUsage = await this.getQuotaUsage(userId, 'images'); // Use images as reference

      // If no quota usage exists, initialize it
      if (!quotaUsage.reset_date) {
        const resetData = {
          count: 0,
          last_reset: new Date().toISOString(),
          reset_date: new Date(Date.now() + tierConfig.expirationDays * 24 * 60 * 60 * 1000).toISOString()
        };

        await Promise.all([
          this.db.updateQuotaUsage(userId, 'images', resetData),
          this.db.updateQuotaUsage(userId, 'content', resetData),
          this.db.updateQuotaUsage(userId, 'title', resetData)
        ]);

        return {
          quotaInitialized: true,
          nextResetDate: resetData.reset_date
        };
      }

      // Check if it's time to reset quota
      if (new Date() >= new Date(quotaUsage.reset_date)) {
        return await this.resetUserQuota(userId);
      }

      return {
        quotaReset: false,
        nextResetDate: quotaUsage.reset_date
      };
    } catch (error) {
      console.error("Error checking and resetting expired quotas:", error);
      throw error;
    }
  }

  async upgradeEmailTier(email, newTier, options = {}) {
    try {
      console.log(`🔄 Upgrading email ${email} to ${newTier} tier`);

      // Get current email tier from KV (for backward compatibility)
      const currentEmailTier = await this.env.USERS_KV.get(
        `t_setting:email_tier:${email}`,
        "json"
      );

      const currentTier = currentEmailTier || {
        tier: TierService.TIERS.STARTER,
        addon1: false,
        addon2: false,
        startDate: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        additionalQuotas: {
          images: 0,
          content: 0,
          title: 0,
        },
        history: []
      };

      // Validate tier exists in settings
      const tierSettings = await this.getTierSettings();
      if (!tierSettings?.config?.[newTier]) {
        throw new Error(`Invalid tier: ${newTier}. Available tiers: ${Object.keys(tierSettings?.config || {}).join(", ")}`);
      }

      // Calculate additional quotas based on tier upgrade
      const tierConfig = tierSettings.config[newTier];
      const previousAdditionalQuotas = currentTier.additionalQuotas || { images: 0, content: 0, title: 0 };

      // Add new quotas to existing additional quotas
      const additionalQuotas = {
        images: previousAdditionalQuotas.images + (tierConfig.imagesQuota || tierConfig.maxQuota || 0),
        content: previousAdditionalQuotas.content + (tierConfig.contentQuota || tierConfig.maxQuota || 0),
        title: previousAdditionalQuotas.title + (tierConfig.titleQuota || tierConfig.maxQuota || 0),
      };

      // Calculate total quotas (base + additional)
      const totalQuotas = {
        images: (tierConfig.imagesQuota || tierConfig.maxQuota || 0) + additionalQuotas.images,
        content: (tierConfig.contentQuota || tierConfig.maxQuota || 0) + additionalQuotas.content,
        title: (tierConfig.titleQuota || tierConfig.maxQuota || 0) + additionalQuotas.title,
      };

      // Create updated tier data
      const updatedTier = {
        tier: newTier,
        addon1: options.addon1 || false,
        addon2: options.addon2 || false,
        startDate: currentTier.startDate || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        previousTier: currentTier.tier,
        additionalQuotas,
        history: [
          ...(currentTier.history || []),
          {
            from: currentTier.tier,
            to: newTier,
            date: new Date().toISOString(),
            previousAdditionalQuotas,
            newAdditionalQuotas: additionalQuotas,
            totalQuotas,
          },
        ],
      };

      // Store the updated tier information in KV
      await this.env.USERS_KV.put(
        `t_setting:email_tier:${email}`,
        JSON.stringify(updatedTier)
      );

      // Also try to update in D1 database if user exists
      try {
        await this.db.setEmailTier(email, newTier);
      } catch (dbError) {
        console.warn("Could not update email tier in D1:", dbError);
      }

      console.log(`✅ Email tier upgraded successfully: ${email} -> ${newTier}`);
      return updatedTier;

    } catch (error) {
      console.error("Error upgrading email tier:", error);
      throw error;
    }
  }
}