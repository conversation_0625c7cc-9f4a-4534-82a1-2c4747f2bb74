// src/services/tierService.js
export class TierService {
  constructor(env) {
    this.env = env;
  }

  // KV key prefixes
  static KEYS = {
    SETTINGS: "t_setting",
    USER_TIER: "t_setting:user_tier",
    QUOTA_USAGE: "t_setting:quota_usage",
    EMAIL_TIER: "t_setting:email_tier",
  };

  // Tier definitions
  static TIERS = {
    STARTER: "starter",
    BASIC: "basic",
    PRO: "pro",
    ENTERPRISE: "enterprise",
  };

  // Default tier configuration
  static DEFAULT_TIER_CONFIG = {
    [TierService.TIERS.STARTER]: {
      name: "Starter",
      maxQuota: 10,
      imagesQuota: 10,
      contentQuota: 10,
      titleQuota: 10,
      domainLimit: 1,
      price: 0,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 4.99,
      addon2_price: 9.99,
      addon1_detail: [
        "Enhanced resolution",
        "Advanced filters",
        "Batch processing",
      ],
      addon2_detail: [
        "Premium support",
        "1-on-1 training",
        "Custom integration",
      ],
      features: ["Basic API access", "Community support", "1 domain"],
    },
    [TierService.TIERS.BASIC]: {
      name: "Basic",
      maxQuota: 100,
      imagesQuota: 100,
      contentQuota: 100,
      titleQuota: 100,
      domainLimit: 5,
      price: 15,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 9.99,
      addon2_price: 19.99,
      addon1_detail: [
        "Enhanced resolution",
        "Advanced filters",
        "Batch processing",
        "Priority processing",
      ],
      addon2_detail: [
        "Premium support",
        "Weekly training",
        "Custom integration",
        "API consultation",
      ],
      features: ["Increased quota", "Email support", "Up to 5 domains"],
    },
    [TierService.TIERS.PRO]: {
      name: "Pro",
      maxQuota: 500,
      imagesQuota: 500,
      contentQuota: 500,
      titleQuota: 500,
      domainLimit: 15,
      price: 40,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 19.99,
      addon2_price: 39.99,
      addon1_detail: [
        "Enhanced resolution",
        "Advanced filters",
        "Batch processing",
        "Priority processing",
        "Custom models",
      ],
      addon2_detail: [
        "Premium support",
        "Daily training",
        "Custom integration",
        "Dedicated manager",
        "24/7 phone support",
      ],
      features: ["High quota", "Priority support", "Up to 15 domains"],
    },
    [TierService.TIERS.ENTERPRISE]: {
      name: "Enterprise",
      maxQuota: 2000,
      imagesQuota: 2000,
      contentQuota: 2000,
      titleQuota: 2000,
      domainLimit: -1, // -1 for unlimited
      price: 99,
      expirationDays: 30,
      addon1: false,
      addon2: false,
      addon1_price: 29.99,
      addon2_price: 49.99,
      addon1_detail: [
        "Enhanced resolution",
        "Advanced filters",
        "Batch processing",
        "Priority processing",
        "Custom models",
        "Enterprise features",
      ],
      addon2_detail: [
        "Premium support",
        "Daily training",
        "Custom integration",
        "Dedicated manager",
        "24/7 phone support",
        "Enterprise SLA",
      ],
      features: ["Maximum quota", "Priority support", "24/7 phone support", "Unlimited domains"],
    },
  };

  async getEmailFromUserId(userId) {
    // First try regular user format
    let user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (user?.email) {
      return user.email;
    }
    
    // If not found and userId starts with 'portal_', try portal user format
    if (userId.startsWith('portal_')) {
      user = await this.env.USERS_KV.get(`portal_user:${userId}`, "json");
      return user?.email;
    }
    
    return null;
  }

  async getUsersByEmail(email) {
    const emailUser = await this.env.USERS_KV.get(`email:${email}`, "json");
    if (!emailUser) return [];
    return emailUser.domains.map((d) => ({
      userId: emailUser.id,
      domain: d.domain,
      api_key: d.api_key,
    }));
  }

  async getTypeQuotaUsage(email, type) {
    const quotaKey = `${TierService.KEYS.QUOTA_USAGE}:${email}:${type}`;
    const usage = await this.env.USERS_KV.get(quotaKey, "json");
    return usage ? usage.count : 0;
  }

  async getDomainQuotaUsage(domain, type) {
    const quotaKey = `${TierService.KEYS.QUOTA_USAGE}:domain:${domain}:${type}`;
    const usage = await this.env.USERS_KV.get(quotaKey, "json");
    return usage ? usage.count : 0;
  }

  async incrementDomainQuotaUsage(domain, type) {
    const quotaKey = `${TierService.KEYS.QUOTA_USAGE}:domain:${domain}:${type}`;
    const currentUsage = (await this.env.USERS_KV.get(quotaKey, "json")) || {
      count: 0,
      lastReset: new Date().toISOString(),
      resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    };

    currentUsage.count += 1;
    await this.env.USERS_KV.put(quotaKey, JSON.stringify(currentUsage));
    return currentUsage;
  }

  async incrementTypeQuotaUsage(userId, type, apiKey) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }

    // Get user's tier
    const emailTier = await this.getEmailTier(user.email);

    // Get the API key data to find the domain
    const apiKeyData = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
    if (!apiKeyData) {
      throw new Error("API key data not found");
    }

    const domain = user.domains?.find((d) => d.api_key === apiKey);
    if (!domain) {
      throw new Error("Domain not found for user");
    }

    if (emailTier.tier === TierService.TIERS.STARTER) {
      // For starter tier, track quota per domain
      const domainUsage = await this.getDomainQuotaUsage(domain.domain, type);
      const tierConfig =
        TierService.DEFAULT_TIER_CONFIG[TierService.TIERS.STARTER];
      const quotaLimit = tierConfig[`${type}Quota`];

      if (domainUsage >= quotaLimit) {
        throw new Error(`Domain quota exceeded for ${type}`);
      }

      return this.incrementDomainQuotaUsage(domain.domain, type);      } else {
        // For paid tiers (basic/pro/enterprise), track quota at email level
        const quotaKey = `${TierService.KEYS.QUOTA_USAGE}:${user.email}:${type}`;
      const currentUsage = (await this.env.USERS_KV.get(quotaKey, "json")) || {
        count: 0,
        lastReset: new Date().toISOString(),
        resetDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      };

      // Get tier config and check quota
      const tierConfig = TierService.DEFAULT_TIER_CONFIG[emailTier.tier];
      const quotaLimit = tierConfig[`${type}Quota`];

      if (currentUsage.count >= quotaLimit) {
        throw new Error(`Email quota exceeded for ${type}`);
      }

      // Increment usage
      currentUsage.count += 1;
      await this.env.USERS_KV.put(quotaKey, JSON.stringify(currentUsage));

      return currentUsage;
    }
  }

  async getEmailTier(email) {
    const tier = await this.env.USERS_KV.get(
      `${TierService.KEYS.EMAIL_TIER}:${email}`,
      "json"
    );
    return (
      tier || {
        tier: TierService.TIERS.STARTER,
        addon1: false,
        addon2: false,
        startDate: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        additionalQuotas: {
          images: 0,
          content: 0,
          title: 0,
        },
      }
    );
  }

  async upgradeEmailTier(email, newTier, options = {}) {
    const currentTier = await this.getEmailTier(email);
    const tierSettings = await this.getTierSettings();

    // Validate tier upgrade
    if (!Object.values(TierService.TIERS).includes(newTier)) {
      throw new Error("Invalid tier specified");
    }

    // Prevent downgrade to starter tier - Check history to see if user ever had a paid tier
    if (newTier === TierService.TIERS.STARTER) {
      const currentEmailTier = await this.env.USERS_KV.get(
        `${TierService.KEYS.EMAIL_TIER}:${email}`,
        "json"
      );

      // Check if user has ever had a non-starter tier by looking at history
      const hasHadPaidTier =
        currentEmailTier &&
        (currentEmailTier.tier !== TierService.TIERS.STARTER ||
          (currentEmailTier.history &&
            currentEmailTier.history.some(
              (h) =>
                h.from !== TierService.TIERS.STARTER ||
                h.to !== TierService.TIERS.STARTER
            )));

      if (hasHadPaidTier) {
        throw new Error(
          "Downgrading to starter tier is not allowed. Starter tier is only for new users."
        );
      }
    }

    const currentTierConfig = tierSettings.config[currentTier.tier];
    const newTierConfig = tierSettings.config[newTier];

    // Get current quotas and usage
    const [imagesUsage, contentUsage, titleUsage] = await Promise.all([
      this.getTypeQuotaUsage(email, "images"),
      this.getTypeQuotaUsage(email, "content"),
      this.getTypeQuotaUsage(email, "title"),
    ]);

    // Get users and current status - Handle case where user doesn't exist yet
    const users = await this.getUsersByEmail(email);
    let currentStatus;
    
    if (!users.length) {
      // For purchases without existing users, create a minimal status
      console.log(`No existing users found for email ${email}, creating tier upgrade for purchase`);
      currentStatus = {
        remainingImagesQuota: 0,
        remainingContentQuota: 0,
        remainingTitleQuota: 0
      };
    } else {
      currentStatus = await this.getUserTierStatus(
        users[0].userId,
        users[0].api_key
      );
    }

    // Get the current additional quotas
    const currentEmailTier = await this.env.USERS_KV.get(
      `${TierService.KEYS.EMAIL_TIER}:${email}`,
      "json"
    );

    // Keep track of previous additional quotas
    const previousAdditionalQuotas = currentEmailTier?.additionalQuotas || {
      images: 0,
      content: 0,
      title: 0,
    };

    // Store the new quotas being added
    const additionalQuotas = {
      images: previousAdditionalQuotas.images + newTierConfig.imagesQuota,
      content: previousAdditionalQuotas.content + newTierConfig.contentQuota,
      title: previousAdditionalQuotas.title + newTierConfig.titleQuota,
    };

    // Calculate new total quotas by adding new tier quotas to current remaining quotas
    const totalQuotas = {
      images: currentStatus.remainingImagesQuota + newTierConfig.imagesQuota,
      content: currentStatus.remainingContentQuota + newTierConfig.contentQuota,
      title: currentStatus.remainingTitleQuota + newTierConfig.titleQuota,
    };

    // Set new tier with quotas
    const updatedTier = {
      tier: newTier,
      addon1: options.addon1 || false,
      addon2: options.addon2 || false,
      startDate: currentEmailTier?.startDate || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      previousTier: currentTier.tier,
      additionalQuotas, // Store accumulated additional quotas
      history: [
        ...(currentTier.history || []),
        {
          from: currentTier.tier,
          to: newTier,
          date: new Date().toISOString(),
          previousAdditionalQuotas,
          newAdditionalQuotas: additionalQuotas,
          totalQuotas,
        },
      ],
    };

    // Store the updated tier information
    await this.env.USERS_KV.put(
      `${TierService.KEYS.EMAIL_TIER}:${email}`,
      JSON.stringify(updatedTier)
    );

    // Update all users under this email (if any exist)
    if (users.length > 0) {
      await Promise.all(
        users.map((user) =>
          this.env.USERS_KV.put(
            `${TierService.KEYS.USER_TIER}:${user.userId}`,
            JSON.stringify(updatedTier)
          )
        )
      );
    }

    return {
      ...updatedTier,
      currentStatus: {
        tierName: newTierConfig.name,
        previousTier: currentTier.tier,
        maxQuota: Object.values(totalQuotas).reduce((a, b) => a + b, 0),
        quotas: {
          images: {
            base: newTierConfig.imagesQuota,
            additional: additionalQuotas.images,
            total: totalQuotas.images,
            used: imagesUsage,
            remaining: totalQuotas.images - imagesUsage,
          },
          content: {
            base: newTierConfig.contentQuota,
            additional: additionalQuotas.content,
            total: totalQuotas.content,
            used: contentUsage,
            remaining: totalQuotas.content - contentUsage,
          },
          title: {
            base: newTierConfig.titleQuota,
            additional: additionalQuotas.title,
            total: totalQuotas.title,
            used: titleUsage,
            remaining: totalQuotas.title - titleUsage,
          },
        },
        price: newTierConfig.price,
        features: newTierConfig.features,
        addons: {
          addon1: {
            enabled: options.addon1 || false,
            price: newTierConfig.addon1_price,
            features: newTierConfig.addon1_detail,
          },
          addon2: {
            enabled: options.addon2 || false,
            price: newTierConfig.addon2_price,
            features: newTierConfig.addon2_detail,
          },
        },
      },
    };
  }

  async initializeTierSettings() {
    const defaultConfig = {
      starter: {
        name: "Starter",
        maxQuota: 10,
        imagesQuota: 10,
        contentQuota: 10,
        titleQuota: 10,
        domainLimit: 1,
        price: 0,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 4.99,
        addon2_price: 9.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
        ],
        addon2_detail: [
          "Premium support",
          "1-on-1 training",
          "Custom integration",
        ],
        features: ["Basic API access", "Community support", "1 domain"],
      },
      basic: {
        name: "Basic",
        maxQuota: 100,
        imagesQuota: 100,
        contentQuota: 100,
        titleQuota: 100,
        domainLimit: 5,
        price: 15,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 9.99,
        addon2_price: 19.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing",
        ],
        addon2_detail: [
          "Premium support",
          "Weekly training",
          "Custom integration",
          "API consultation",
        ],
        features: ["Increased quota", "Email support", "Up to 5 domains"],
      },
      pro: {
        name: "Pro",
        maxQuota: 500,
        imagesQuota: 500,
        contentQuota: 500,
        titleQuota: 500,
        domainLimit: 15,
        price: 40,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 19.99,
        addon2_price: 39.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing",
          "Custom models",
        ],
        addon2_detail: [
          "Premium support",
          "Daily training",
          "Custom integration",
          "Dedicated manager",
          "24/7 phone support",
        ],
        features: ["High quota", "Priority support", "Up to 15 domains"],
      },
      enterprise: {
        name: "Enterprise",
        maxQuota: 2000,
        imagesQuota: 2000,
        contentQuota: 2000,
        titleQuota: 2000,
        domainLimit: -1, // -1 for unlimited
        price: 99,
        expirationDays: 30,
        addon1: false,
        addon2: false,
        addon1_price: 29.99,
        addon2_price: 49.99,
        addon1_detail: [
          "Enhanced resolution",
          "Advanced filters",
          "Batch processing",
          "Priority processing",
          "Custom models",
          "Enterprise features",
        ],
        addon2_detail: [
          "Premium support",
          "Daily training",
          "Custom integration",
          "Dedicated manager",
          "24/7 phone support",
          "Enterprise SLA",
        ],
        features: ["Maximum quota", "Priority support", "24/7 phone support", "Unlimited domains"],
      },
    };

    // Store the settings
    await this.env.USERS_KV.put(
      `${TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify({
        config: defaultConfig,
        updatedAt: new Date().toISOString(),
        version: "1.0",
      })
    );

    return await this.getTierSettings();
  }

  async getTierSettings() {
    const settings = await this.env.USERS_KV.get(
      `${TierService.KEYS.SETTINGS}:tiers`,
      "json"
    );
    
    // If settings don't exist, initialize them
    if (!settings) {
      await this.initializeTierSettings();
      return await this.env.USERS_KV.get(
        `${TierService.KEYS.SETTINGS}:tiers`,
        "json"
      );
    }
    
    return settings;
  }

  async updateTierSettings(tierConfig) {
    const settings = {
      config: tierConfig,
      updatedAt: new Date().toISOString(),
      version: "1.0",
    };
    await this.env.USERS_KV.put(
      `${TierService.KEYS.SETTINGS}:tiers`,
      JSON.stringify(settings)
    );
    return settings;
  }

  async getUserTier(userId) {
    try {
      const userTier = await this.env.USERS_KV.get(
        `${TierService.KEYS.USER_TIER}:${userId}`,
        "json"
      );
      // If no specific tier is set, return the default starter tier
      return (
        userTier || {
          tier: TierService.TIERS.STARTER,
          updatedAt: new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error("Error getting user tier:", error);
      throw error;
    }
  }

  async setUserTier(userId, newTier, options = {}) {
    if (!Object.values(TierService.TIERS).includes(newTier)) {
      throw new Error("Invalid tier specified");
    }

    const tierSettings = await this.getTierSettings();
    const newTierConfig = tierSettings.config[newTier];

    if (!newTierConfig) {
      throw new Error(`Configuration not found for tier: ${newTier}`);
    }

    // Validate current usage
    const [imagesUsage, contentUsage] = await Promise.all([
      this.getTypeQuotaUsage(userId, "images"),
      this.getTypeQuotaUsage(userId, "content"),
    ]);

    const startDate = new Date();
    const expirationDate = new Date(startDate);
    expirationDate.setDate(startDate.getDate() + newTierConfig.expirationDays);

    const tierData = {
      tier: newTier,
      startDate: startDate.toISOString(),
      expirationDate: expirationDate.toISOString(),
      updatedAt: new Date().toISOString(),
      addon1: options.addon1 || false,
      addon2: options.addon2 || false,
      previousUsage: {
        images: imagesUsage || 0,
        content: contentUsage || 0,
      },
    };

    await this.env.USERS_KV.put(
      `${TierService.KEYS.USER_TIER}:${userId}`,
      JSON.stringify(tierData)
    );

    return {
      ...tierData,
      currentStatus: await this.getUserTierStatus(userId, null),
    };
  }

  async getUserQuotaUsage(userId) {
    const usage = await this.env.USERS_KV.get(
      `${TierService.KEYS.QUOTA_USAGE}:${userId}`,
      "json"
    );
    return usage?.count || 0;
  }

  async incrementQuotaUsage(userId) {
    const currentUsage = await this.getUserQuotaUsage(userId);
    const userTier = await this.getUserTier(userId);
    const tierSettings = await this.getTierSettings();

    if (currentUsage >= tierSettings.config[userTier].maxQuota) {
      throw new Error("Quota exceeded for current tier");
    }

    const newUsage = {
      count: currentUsage + 1,
      lastUpdated: new Date().toISOString(),
    };

    await this.env.USERS_KV.put(
      `${TierService.KEYS.QUOTA_USAGE}:${userId}`,
      JSON.stringify(newUsage)
    );
    return newUsage.count;
  }

  async resetQuotaUsage(userId) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }

    const resetData = {
      count: 0,
      lastReset: new Date().toISOString(),
      resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    };

    // Reset all quota types
    await Promise.all([
      this.env.USERS_KV.put(
        `${TierService.KEYS.QUOTA_USAGE}:${user.email}:images`,
        JSON.stringify(resetData)
      ),
      this.env.USERS_KV.put(
        `${TierService.KEYS.QUOTA_USAGE}:${user.email}:content`,
        JSON.stringify(resetData)
      ),
      this.env.USERS_KV.put(
        `${TierService.KEYS.QUOTA_USAGE}:${user.email}:title`,
        JSON.stringify(resetData)
      ),
    ]);

    return {
      email: user.email,
      resetDate: resetData.resetDate,
      quotas: {
        images: 0,
        content: 0,
        title: 0,
      },
    };
  }

  async getUserTierStatus(userId, apiKey) {
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) throw new Error("User not found");

    const [emailTier, tierSettings] = await Promise.all([
      this.getEmailTier(user.email),
      this.getTierSettings(),
    ]);

    if (!tierSettings || !tierSettings.config) {
      throw new Error("Tier settings not found");
    }

    const tierConfig = tierSettings.config[emailTier.tier];
    
    if (!tierConfig) {
      throw new Error(`Tier configuration not found for tier: ${emailTier.tier}`);
    }

    let imagesUsage, contentUsage, titleUsage;

    if (emailTier.tier === TierService.TIERS.STARTER) {
      // For starter tier, get domain-specific usage
      const domain = user.domains?.find((d) => d.api_key === apiKey);
      if (!domain) {
        throw new Error("Domain not found for user");
      }

      [imagesUsage, contentUsage, titleUsage] = await Promise.all([
        this.getDomainQuotaUsage(domain.domain, "images"),
        this.getDomainQuotaUsage(domain.domain, "content"),
        this.getDomainQuotaUsage(domain.domain, "title"),
      ]);
    } else {
      // For paid tiers, get email-level usage
      [imagesUsage, contentUsage, titleUsage] = await Promise.all([
        this.getTypeQuotaUsage(user.email, "images"),
        this.getTypeQuotaUsage(user.email, "content"),
        this.getTypeQuotaUsage(user.email, "title"),
      ]);
    }

    // Calculate total usage and quotas
    const totalUsage = imagesUsage + contentUsage + titleUsage;
    const maxQuota =
      tierConfig.maxQuota +
      (emailTier.additionalQuotas?.images || 0) +
      (emailTier.additionalQuotas?.content || 0) +
      (emailTier.additionalQuotas?.title || 0);

    // Calculate remaining quotas
    const remainingImagesQuota =
      tierConfig.imagesQuota +
      (emailTier.additionalQuotas?.images || 0) -
      imagesUsage;
    const remainingContentQuota =
      tierConfig.contentQuota +
      (emailTier.additionalQuotas?.content || 0) -
      contentUsage;
    const remainingTitleQuota =
      tierConfig.titleQuota +
      (emailTier.additionalQuotas?.title || 0) -
      titleUsage;

    return {
      currentTier: emailTier.tier,
      tierName: tierConfig.name,
      usage: contentUsage, // Changed to contentUsage
      imagesUsage,
      contentUsage,
      titleUsage,
      maxQuota: tierConfig.contentQuota, // Changed to contentQuota
      imagesQuota:
        tierConfig.imagesQuota + (emailTier.additionalQuotas?.images || 0),
      contentQuota:
        tierConfig.contentQuota + (emailTier.additionalQuotas?.content || 0),
      titleQuota:
        tierConfig.titleQuota + (emailTier.additionalQuotas?.title || 0),
      remainingImagesQuota,
      remainingContentQuota,
      remainingTitleQuota,
      price: tierConfig.price,
      features: tierConfig.features,
      quotaPercentage: `${Math.round((contentUsage / (tierConfig.contentQuota + (emailTier.additionalQuotas?.content || 0))) * 100)}%`, // Updated calculation
    };
  }

  async setQuotaToZero(userId) {
    try {
      // Set both image and content quotas to their maximum values
      const usage = {
        count: Number.MAX_SAFE_INTEGER, // This will effectively make remaining = 0
        lastUpdated: new Date().toISOString(),
      };

      // Update both image and content quotas
      await Promise.all([
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(usage)
        ),
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(usage)
        ),
      ]);

      // Get updated tier status to confirm
      const tierStatus = await this.getUserTierStatus(userId, null);

      // Verify remaining quotas are 0
      const isQuotaZero =
        tierStatus.remainingImagesQuota === 0 &&
        tierStatus.remainingContentQuota === 0;

      return {
        success: true,
        isQuotaZero,
        tierStatus: {
          remainingImages: tierStatus.remainingImagesQuota,
          remainingContent: tierStatus.remainingContentQuota,
          totalImages: tierStatus.imagesQuota,
          totalContent: tierStatus.contentQuota,
          currentTier: tierStatus.currentTier,
        },
      };
    } catch (error) {
      console.error("Error setting quota to zero:", error);
      throw error;
    }
  }

  async checkAndResetQuota(userId) {
    const userTier = await this.getUserTier(userId);
    const tierStatus = await this.getUserTierStatus(userId, null);
    const tierSettings = await this.getTierSettings();
    const starterTierConfig = tierSettings.config[TierService.TIERS.STARTER];

    // For paid tier users (basic/pro/enterprise)
    if (userTier.tier !== TierService.TIERS.STARTER) {
      // Check if all quotas are zero
      if (tierStatus.remainingQuota <= 0) {
        // Downgrade to starter tier
        await this.setUserTier(userId, TierService.TIERS.STARTER);

        // Reset quota to starter tier limits
        const resetData = {
          count: 0,
          lastReset: new Date().toISOString(),
          resetDate: new Date(
            Date.now() + starterTierConfig.expirationDays * 24 * 60 * 60 * 1000
          ).toISOString(),
        };

        await Promise.all([
          this.env.USERS_KV.put(
            `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
            JSON.stringify(resetData)
          ),
          this.env.USERS_KV.put(
            `${TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
            JSON.stringify(resetData)
          ),
          this.env.USERS_KV.put(
            `${TierService.KEYS.QUOTA_USAGE}:${userId}:title`,
            JSON.stringify(resetData)
          ),
        ]);

        return {
          downgraded: true,
          newTier: TierService.TIERS.STARTER,
          quotaReset: true,
          nextResetDate: resetData.resetDate,
        };
      }
      return {
        downgraded: false,
        currentTier: userTier.tier,
        remainingQuota: tierStatus.remainingQuota,
      };
    }

    // For starter tier users
    const quotaUsage = await this.env.USERS_KV.get(
      `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
      "json"
    );

    if (!quotaUsage || !quotaUsage.resetDate) {
      // Initialize reset date if not set
      const resetData = {
        count: 0,
        lastReset: new Date().toISOString(),
        resetDate: new Date(
          Date.now() + starterTierConfig.expirationDays * 24 * 60 * 60 * 1000
        ).toISOString(),
      };

      await Promise.all([
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:title`,
          JSON.stringify(resetData)
        ),
      ]);

      return {
        quotaInitialized: true,
        nextResetDate: resetData.resetDate,
      };
    }

    // Check if it's time to reset quota
    if (new Date() >= new Date(quotaUsage.resetDate)) {
      const resetData = {
        count: 0,
        lastReset: new Date().toISOString(),
        resetDate: new Date(
          Date.now() + starterTierConfig.expirationDays * 24 * 60 * 60 * 1000
        ).toISOString(),
      };

      await Promise.all([
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:images`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:content`,
          JSON.stringify(resetData)
        ),
        this.env.USERS_KV.put(
          `${TierService.KEYS.QUOTA_USAGE}:${userId}:title`,
          JSON.stringify(resetData)
        ),
      ]);

      return {
        quotaReset: true,
        nextResetDate: resetData.resetDate,
      };
    }

    return {
      quotaReset: false,
      nextResetDate: quotaUsage.resetDate,
    };
  }
}
