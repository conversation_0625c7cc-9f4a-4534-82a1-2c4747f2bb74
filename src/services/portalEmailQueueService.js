// src/services/portalEmailQueueService.js
import { PortalEmailService } from "./portalEmailService.js";

export class PortalEmailQueueService {
  constructor(env) {
    this.env = env;
    this.namespace = env.USERS_KV; // Use same KV namespace for email queue
    this.portalEmailService = new PortalEmailService(env);
  }

  async addToQueue(email, username, type = "portal_welcome", additionalData = {}) {
    console.log(`📧 Adding portal email to queue for ${email}, type: ${type}`);

    const queueId = `portal_email_queue:${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const queueItem = {
      id: queueId,
      email,
      username,
      type,
      status: "pending",
      createdAt: new Date().toISOString(),
      attempts: 0,
      ...additionalData,
    };

    try {
      await this.namespace.put(queueId, JSON.stringify(queueItem));
      console.log(`✅ Portal email queued successfully: ${queueId}`);

      // Process immediately
      await this.processQueueItem(queueItem);
      return { success: true, queueId };
    } catch (error) {
      console.error("❌ Error adding portal email to queue:", error);
      throw error;
    }
  }

  async processQueueItem(queueItem) {
    console.log(`📧 Processing portal email queue item: ${queueItem.id}`);
    console.log("Debug - Portal queue item:", queueItem);

    try {
      let result;

      // Handle different portal email types
      if (queueItem.type === "portal_welcome") {
        result = await this.portalEmailService.sendPortalWelcomeEmail(
          queueItem.email,
          queueItem.username
        );
      } else if (queueItem.type === "portal_password_reset") {
        result = await this.portalEmailService.sendPortalPasswordResetEmail(
          queueItem.email,
          queueItem.username,
          queueItem.resetToken
        );
      } else {
        throw new Error(`Unknown portal email type: ${queueItem.type}`);
      }

      if (result.success) {
        console.log(`✅ Portal email sent successfully to ${queueItem.email}`);
        // Move to sent items
        await Promise.all([
          this.namespace.delete(queueItem.id),
          this.namespace.put(
            `portal_email_sent:${queueItem.id}`,
            JSON.stringify({
              ...queueItem,
              status: "sent",
              sentAt: new Date().toISOString(),
              messageId: result.messageId,
            })
          ),
        ]);
        return result;
      } else {
        throw new Error(result.error || "Failed to send portal email");
      }
    } catch (error) {
      console.error(`❌ Error processing portal email for ${queueItem.email}:`, error);

      // Handle retry logic
      const attempts = (queueItem.attempts || 0) + 1;
      if (attempts >= 3) {
        await this.moveToFailed(queueItem, error);
      } else {
        await this.scheduleRetry(queueItem, attempts, error);
      }
      throw error;
    }
  }

  async moveToFailed(queueItem, error) {
    console.log(`❌ Moving portal email to failed: ${queueItem.id}`);
    await Promise.all([
      this.namespace.delete(queueItem.id),
      this.namespace.put(
        `portal_email_failed:${queueItem.id}`,
        JSON.stringify({
          ...queueItem,
          status: "failed",
          failedAt: new Date().toISOString(),
          error: error.message,
          attempts: (queueItem.attempts || 0) + 1,
        })
      ),
    ]);
  }

  async scheduleRetry(queueItem, attempts, error) {
    console.log(`🔄 Scheduling portal email retry ${attempts}/3: ${queueItem.id}`);
    const retryDelay = Math.pow(2, attempts) * 1000; // Exponential backoff
    const retryAt = new Date(Date.now() + retryDelay).toISOString();

    await this.namespace.put(
      queueItem.id,
      JSON.stringify({
        ...queueItem,
        attempts,
        retryAt,
        lastError: error.message,
        status: "retry_scheduled",
      })
    );
  }

  async getQueueStats() {
    const keys = await this.namespace.list({ prefix: "portal_email_" });
    const stats = {
      pending: 0,
      sent: 0,
      failed: 0,
      retry_scheduled: 0,
    };

    for (const key of keys.keys) {
      if (key.name.startsWith("portal_email_queue:")) {
        const item = JSON.parse(await this.namespace.get(key.name));
        stats[item.status] = (stats[item.status] || 0) + 1;
      } else if (key.name.startsWith("portal_email_sent:")) {
        stats.sent++;
      } else if (key.name.startsWith("portal_email_failed:")) {
        stats.failed++;
      }
    }

    return stats;
  }
}
