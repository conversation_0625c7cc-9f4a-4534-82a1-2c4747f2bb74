export class ApiKeyService {
  constructor(env) {
    this.env = env;
  }

  async validateApi<PERSON>ey(request) {
    const apiKey = request.headers.get("x-sps-key");
    const requestDomain = request.headers.get("x-sps-domain");

    if (!apiKey) {
      throw new Error("API Key is required in x-sps-key header");
    }

    if (!requestDomain) {
      throw new Error("Domain is required in x-sps-domain header");
    }

    // Get user ID directly from API key mapping
    const userId = await this.env.USERS_KV.get(`apikey:${apiKey}`);
    if (!userId) {
      throw new Error("Invalid API Key");
    }

    // Get user details
    const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
    if (!user) {
      throw new Error("User not found");
    }

    // Find domain with matching API key
    const domain = user.domains?.find((domain) => domain.api_key === apiKey);
    if (!domain) {
      throw new Error("Invalid API Key");
    }

    // Validate if the request domain matches the registered domain
    if (domain.domain !== requestDomain) {
      throw new Error(
        "Failed activate api key, use the correct registered domain !"
      );
    }

    return {
      userId: user.id,
      email: user.email,
      domain: domain.domain,
      api_key: apiKey,
    };
  }
}
