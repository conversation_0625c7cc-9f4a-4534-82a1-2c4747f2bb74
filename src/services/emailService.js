export class EmailService {
  constructor(env) {
    this.env = env; // Add this line to store the env object

    if (!env.ZEPTO_TOKEN) {
      console.error(
        "❌ ZEPTO_TOKEN is not configured in environment variables"
      );
      throw new Error("Email service configuration missing");
    }

    if (!env.ZEPTO_FROM_ADDRESS) {
      console.error(
        "❌ ZEPTO_FROM_ADDRESS is not configured in environment variables"
      );
      throw new Error("Email service configuration missing");
    }

    this.API_URL = "https://api.zeptomail.com/v1.1/email/template";
    this.token = env.ZEPTO_TOKEN;
    this.fromAddress = env.ZEPTO_FROM_ADDRESS.includes("@")
      ? env.ZEPTO_FROM_ADDRESS
      : `noreply@${env.ZEPTO_FROM_ADDRESS}`;
    this.newUserTemplateKey =
      "2d6f.15d6311547e5377.k1.d66d6b50-5fe5-11f0-9afb-525400114fe6.19803c85e85";
    this.existingUserTemplateKey =
      "2d6f.15d6311547e5377.k1.2b51baa0-d804-11ef-a765-525400033811.1948945034a";
    this.domainAlreadyRegisteredTemplateKey =
      "2d6f.15d6311547e5377.k1.2b51baa0-d804-11ef-a765-525400033811.1948945034a";
    this.passwordResetTemplateKey =
      "2d6f.15d6311547e5377.k1.345c5b70-6119-11f0-9afb-525400114fe6.1980ba6baa7";
  }

  async sendApiKeyEmail(email, username, apiKey, password, domain) {
    console.log("📧 Starting to send email to:", email);
    console.log("Debug - Password value:", password);
    console.log("Debug - All params:", {
      email,
      username,
      apiKey,
      password: password ? "***" : undefined,
      domain,
    });

    if (!password) {
      console.log("ℹ️ Existing user - sending domain notification email");
    }

    try {
      const requestBody = {
        template_key: password
          ? this.newUserTemplateKey
          : this.existingUserTemplateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID",
        },
        to: [
          {
            email_address: {
              address: email,
              name: email.split("@")[0],
            },
          },
        ],
        merge_info: {
          API_KEY: apiKey,
          DOMAIN: domain,
          USER_EMAIL: email,
          ...(password
            ? { TEMP_PASSWORD: password }
            : { DOMAIN_NOTE: "This is an additional domain for your account" }),
          YEAR: new Date().getFullYear(),
          AWP_APP: "Superuser.ID",
          DOCS_URL: "https://docs.superuser.id",
          API_DOCS: "https://superuser.id/portal",
          EMAIL_SUPPORT: "<EMAIL>",
        },
        track_clicks: false,
        track_opens: false,
      };

      console.log("📤 Sending email with API key:", apiKey);

      const response = await fetch(this.API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: this.token,
        },
        body: JSON.stringify(requestBody),
      });

      const responseData = await response.json();
      console.log(
        "📨 Email API Response:",
        JSON.stringify(responseData, null, 2)
      );

      if (!response.ok) {
        throw new Error(
          `Email API error: ${response.status} - ${JSON.stringify(
            responseData
          )}`
        );
      }

      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("❌ Error sending email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendDomainAlreadyRegisteredEmail(
    email,
    username,
    apiKey,
    domain,
    existingEmail,
    password,
    attemptedEmail
  ) {
    console.log(
      "📧 Starting to send domain already registered email to:",
      email
    );
    console.log("Debug - All params:", {
      email,
      username,
      apiKey,
      domain,
      existingEmail,
      password: password ? "***" : undefined,
      attemptedEmail,
    });

    try {
      const requestBody = {
        template_key: this.newUserTemplateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID",
        },
        to: [
          {
            email_address: {
              address: email,
              name: username,
            },
          },
        ],
        merge_info: {
          API_KEY: apiKey,
          DOMAIN: domain,
          USER_EMAIL: email,
          TEMP_PASSWORD: password || "Contact support for password reset",
          DOMAIN_NOTE: attemptedEmail
            ? `Note: Someone with email ${attemptedEmail} attempted to register your domain. This domain is already registered to your account. Each domain can only have one API key.`
            : `Note: This domain is already registered to your account. Each domain can only have one API key.`,
          YEAR: new Date().getFullYear(),
          AWP_APP: "Superuser.ID",
          DOCS_URL: "https://docs.superuser.id",
          API_DOCS: "https://superuser.id/portal",
          EMAIL_SUPPORT: "<EMAIL>",
        },
        track_clicks: false,
        track_opens: false,
      };

      console.log(
        "📤 Sending domain already registered email for domain:",
        domain
      );

      const response = await fetch(this.API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: this.token,
        },
        body: JSON.stringify(requestBody),
      });

      const responseData = await response.json();
      console.log(
        "📨 Domain already registered email API Response:",
        JSON.stringify(responseData, null, 2)
      );

      if (!response.ok) {
        throw new Error(
          `Failed to send domain already registered email: ${
            responseData.message || response.statusText
          }`
        );
      }

      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("❌ Error sending domain already registered email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendPortalWelcomeEmail(email, username, password, activationToken) {
    console.log("📧 Starting to send portal welcome email to:", email);
    console.log("Debug - Portal welcome email params:", {
      email,
      username,
      password: password ? "***" : undefined,
      activationToken: activationToken ? "***" : undefined,
    });

    try {
      const requestBody = {
        template_key: this.newUserTemplateKey, // Menggunakan template yang sama untuk sementara
        from: {
          address: this.fromAddress,
          name: "Superuser.ID",
        },
        to: [
          {
            email_address: {
              address: email,
              name: username,
            },
          },
        ],
        merge_info: {
          USER_EMAIL: email,
          TEMP_PASSWORD: password,
          LINK_ACTIVATION: `${
            this.env.FRONTEND_URL || "http://localhost:3001"
          }/activate?token=${activationToken}`,
          YEAR: new Date().getFullYear(),
          AWP_APP: "Superuser.ID",
          DOCS_URL: "https://docs.superuser.id",
          API_DOCS: "https://superuser.id/portal",
          EMAIL_SUPPORT: "<EMAIL>",
          // Placeholder untuk template portal
          API_KEY: "Portal Access", // Placeholder karena portal user tidak punya API key
          DOMAIN: "Portal Dashboard", // Placeholder karena portal user tidak punya domain
          DOMAIN_NOTE:
            "Welcome to Portal! Please click the activation link to activate your account.",
        },
        track_clicks: false,
        track_opens: false,
      };

      console.log("📤 Sending portal welcome email");

      const response = await fetch(this.API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: this.token,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ Email API error:", {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        });
        throw new Error(`Email API returned ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log("✅ Portal welcome email sent successfully:", responseData);

      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("❌ Error sending portal welcome email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendPasswordResetEmail(email, resetLink) {
    console.log("📧 Starting to send password reset email to:", email);

    try {
      const requestBody = {
        template_key: this.passwordResetTemplateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID",
        },
        to: [
          {
            email_address: {
              address: email,
              name: email.split("@")[0],
            },
          },
        ],
        merge_info: {
          LINK_RESET_PASSWORD: resetLink,
          YEAR: new Date().getFullYear(),
          AWP_APP: "Superuser.ID",
        },
        track_clicks: false,
        track_opens: false,
      };

      console.log("📤 Sending password reset email");

      const response = await fetch(this.API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: this.token,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ Email API error:", {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        });
        throw new Error(`Email API returned ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log("✅ Password reset email sent successfully:", responseData);

      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("❌ Error sending password reset email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Generic sendEmail method that routes to specific methods based on email type
  async sendEmail(emailData) {
    console.log("📧 Generic sendEmail called with type:", emailData.type);

    try {
      switch (emailData.type) {
        case "welcome":
        case "new_domain":
          return await this.sendApiKeyEmail(
            emailData.email,
            emailData.username,
            emailData.apiKey,
            emailData.password,
            emailData.domain
          );

        case "domain_already_registered":
          return await this.sendDomainAlreadyRegisteredEmail(
            emailData.email,
            emailData.username,
            emailData.apiKey,
            emailData.domain,
            emailData.existingEmail,
            emailData.password,
            emailData.attemptedEmail
          );

        case "portal_welcome":
          return await this.sendPortalWelcomeEmail(
            emailData.email,
            emailData.username,
            emailData.password,
            emailData.activationToken
          );

        case "portal_password_reset":
          return await this.sendPasswordResetEmail(
            emailData.email,
            emailData.resetLink
          );

        default:
          console.error(`❌ Unknown email type: ${emailData.type}`);
          return {
            success: false,
            error: `Unknown email type: ${emailData.type}`
          };
      }
    } catch (error) {
      console.error("❌ Error in generic sendEmail:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
