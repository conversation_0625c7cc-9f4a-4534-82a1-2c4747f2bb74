// emailQueueService.js
import { EmailService } from "./emailService.js"; // Add this import
export class EmailQueueService {
  constructor(env) {
    this.env = env;
    this.emailService = new EmailService(env);
  }

  async addToQueue(emailData) {
    try {
      console.log("🚀 Adding email to queue:", {
        email: emailData.email,
        type: emailData.type,
        domain: emailData.domain,
      });

      const id = crypto.randomUUID();
      const queueItem = {
        id,
        email: emailData.email,
        username: emailData.username,
        domain: emailData.domain,
        password: emailData.password,
        apiKey: emailData.apiKey,
        type: emailData.type,
        activationToken: emailData.activationToken,
        status: "pending",
        attempts: 0,
        queuedAt: new Date().toISOString(),
        processAfter: new Date().toISOString(),
      };

      // Add existingEmail for domain_already_registered type
      if (
        emailData.type === "domain_already_registered" &&
        emailData.existingEmail
      ) {
        queueItem.existingEmail = emailData.existingEmail;
      }

      // Add attemptedEmail for domain_already_registered type
      if (
        emailData.type === "domain_already_registered" &&
        emailData.attemptedEmail
      ) {
        queueItem.attemptedEmail = emailData.attemptedEmail;
      }

      if (emailData.type === "portal_password_reset" && emailData.resetLink) {
        queueItem.resetLink = emailData.resetLink;
      }

      console.log("Debug - Created queue item with data:", {
        ...queueItem,
        password: queueItem.password ? "***" : undefined,
      });

      await this.env.USERS_KV.put(
        `email_queue:${id}`,
        JSON.stringify(queueItem)
      );

      // Process immediately
      await this.processQueueItem(queueItem);

      return id;
    } catch (error) {
      console.error("❌ Error adding to queue:", error);
      throw error;
    }
  }

  async processQueueItem(queueItem) {
    console.log(`📧 Processing email ${queueItem.id} for ${queueItem.email}`);
    console.log("Debug - Queue item password:", queueItem.password);
    console.log("Debug - Full queue item:", queueItem);

    try {
      let result;

      // Handle different email types
      if (queueItem.type === "domain_already_registered") {
        result = await this.emailService.sendDomainAlreadyRegisteredEmail(
          queueItem.email,
          queueItem.username,
          queueItem.apiKey,
          queueItem.domain,
          queueItem.existingEmail,
          queueItem.password,
          queueItem.attemptedEmail
        );
      } else if (queueItem.type === "portal_welcome") {
        console.log(
          "📧 Processing portal_welcome email with data:",
          JSON.stringify(queueItem, null, 2)
        );
        console.log(
          "📧 ActivationToken being passed:",
          queueItem.activationToken
        );
        result = await this.emailService.sendPortalWelcomeEmail(
          queueItem.email,
          queueItem.username,
          queueItem.password,
          queueItem.activationToken
        );
      } else if (queueItem.type === "portal_password_reset") {
        result = await this.emailService.sendPasswordResetEmail(
          queueItem.email,
          queueItem.resetLink
        );
      } else {
        // Default case for welcome and new_domain emails
        result = await this.emailService.sendApiKeyEmail(
          queueItem.email,
          queueItem.username,
          queueItem.apiKey,
          queueItem.password,
          queueItem.domain
        );
      }

      if (result.success) {
        console.log(`✅ Email sent successfully to ${queueItem.email}`);
        // Move to sent items
        await Promise.all([
          this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),
          this.env.USERS_KV.put(
            `email_sent:${queueItem.id}`,
            JSON.stringify({
              ...queueItem,
              status: "sent",
              sentAt: new Date().toISOString(),
              messageId: result.messageId,
            })
          ),
        ]);
        return result;
      } else {
        throw new Error(result.error || "Failed to send email");
      }
    } catch (error) {
      console.error(`❌ Error processing email for ${queueItem.email}:`, error);

      // Handle retry logic
      const attempts = (queueItem.attempts || 0) + 1;
      if (attempts >= 3) {
        await this.moveToFailed(queueItem, error);
      } else {
        await this.scheduleRetry(queueItem, attempts, error); // Pass the error
      }

      throw error;
    }
  }

  async moveToFailed(queueItem, error) {
    await Promise.all([
      this.env.USERS_KV.delete(`email_queue:${queueItem.id}`),
      this.env.USERS_KV.put(
        `email_failed:${queueItem.id}`,
        JSON.stringify({
          ...queueItem,
          status: "failed",
          error: error.message,
          failedAt: new Date().toISOString(),
        })
      ),
    ]);
  }

  async scheduleRetry(queueItem, attempts, error) {
    // Added error parameter
    const delayMinutes = Math.pow(2, attempts - 1);
    const processAfter = new Date(
      Date.now() + delayMinutes * 60 * 1000
    ).toISOString();

    await this.env.USERS_KV.put(
      `email_queue:${queueItem.id}`,
      JSON.stringify({
        ...queueItem,
        attempts,
        processAfter,
        lastError: error.message, // Now error is defined
        lastAttempt: new Date().toISOString(),
      })
    );

    console.log(
      `⏳ Scheduled retry #${attempts} for ${queueItem.email} after ${delayMinutes} minutes`
    );
  }

  async getQueueStatus() {
    try {
      const [pending, sent, failed] = await Promise.all([
        this.env.USERS_KV.list({ prefix: "email_queue:" }),
        this.env.USERS_KV.list({ prefix: "email_sent:" }),
        this.env.USERS_KV.list({ prefix: "email_failed:" }),
      ]);

      const status = {
        pending: pending.keys.length,
        sent: sent.keys.length,
        failed: failed.length,
        details: {
          pending: await this._getQueueItems("email_queue:"),
          sent: await this._getQueueItems("email_sent:"),
          failed: await this._getQueueItems("email_failed:"),
        },
      };

      console.log("📊 Queue Status:", status);
      return status;
    } catch (error) {
      console.error("❌ Error getting queue status:", error);
      throw error;
    }
  }

  async processQueue(logDetails = false) {
    try {
      console.log("🔄 Starting queue processing...");

      const { keys } = await this.env.USERS_KV.list({
        prefix: "email_queue:",
      });

      if (logDetails) {
        console.log(`📧 Found ${keys.length} emails in queue to process`);
      }

      const results = {
        processed: 0,
        succeeded: 0,
        failed: 0,
        skipped: 0,
        errors: [],
      };

      for (const key of keys) {
        try {
          const queueItem = await this.env.USERS_KV.get(key.name, "json");

          if (!queueItem) {
            if (logDetails) {
              console.log(`⚠️ Skipping invalid queue item: ${key.name}`);
            }
            results.skipped++;
            continue;
          }

          // Check if it's time to process this item
          if (new Date(queueItem.processAfter) > new Date()) {
            if (logDetails) {
              console.log(
                `⏰ Skipping email ${queueItem.id} - scheduled for later`
              );
            }
            results.skipped++;
            continue;
          }

          if (logDetails) {
            console.log(
              `📤 Processing email ${queueItem.id} for ${queueItem.email}`
            );
          }

          // Use the existing processQueueItem method
          await this.processQueueItem(queueItem);

          results.processed++;
          results.succeeded++;

          if (logDetails) {
            console.log(
              `✅ Successfully processed email for ${queueItem.email}`
            );
          }
        } catch (error) {
          console.error(`❌ Error processing queue item ${key.name}:`, error);
          results.processed++;
          results.failed++;
          results.errors.push({
            key: key.name,
            error: error.message,
          });
        }
      }

      const summary = `Queue processing completed: ${results.succeeded} succeeded, ${results.failed} failed, ${results.skipped} skipped`;
      console.log(`✅ ${summary}`);

      return {
        success: true,
        message: summary,
        ...results,
      };
    } catch (error) {
      console.error("❌ Error in processQueue:", error);
      throw error;
    }
  }

  async _getQueueItems(prefix) {
    const { keys } = await this.env.USERS_KV.list({ prefix });
    const items = await Promise.all(
      keys.map(async (key) => {
        const value = await this.env.USERS_KV.get(key.name, "json");
        return { key: key.name, ...value };
      })
    );
    return items;
  }

  async getDebugInfo() {
    const status = await this.getQueueStatus();
    return {
      queueStatus: status,
      timestamp: new Date().toISOString(),
      environment: {
        hasZeptoToken: !!this.env.ZEPTO_TOKEN,
        hasFromAddress: !!this.env.ZEPTO_FROM_ADDRESS,
        zeptoToken: {
          exists: !!this.env.ZEPTO_TOKEN,
          length: this.env.ZEPTO_TOKEN?.length || 0,
          preview: this.env.ZEPTO_TOKEN
            ? `${this.env.ZEPTO_TOKEN.substring(0, 10)}...`
            : null,
        },
        fromAddress: this.env.ZEPTO_FROM_ADDRESS || "not configured",
      },
    };
  }
}
