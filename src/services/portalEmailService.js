// src/services/portalEmailService.js
export class PortalEmailService {
  constructor(env) {
    if (!env.ZEPTO_TOKEN) {
      console.error(
        "❌ ZEPTO_TOKEN is not configured in environment variables"
      );
      throw new Error("Portal email service configuration missing");
    }

    if (!env.ZEPTO_FROM_ADDRESS) {
      console.error(
        "❌ ZEPTO_FROM_ADDRESS is not configured in environment variables"
      );
      throw new Error("Portal email service configuration missing");
    }

    this.env = env;
    this.apiUrl = "https://api.zeptomail.com/v1.1/email/template";
    this.token = env.ZEPTO_TOKEN;
    this.fromAddress = env.ZEPTO_FROM_ADDRESS.includes("@")
      ? env.ZEPTO_FROM_ADDRESS
      : `noreply@${env.ZEPTO_FROM_ADDRESS}`;
    this.portalTemplateKey = "2d6f.15d6311547e5377.k1.d66d6b50-5fe5-11f0-9afb-525400114fe6.19803c85e85";
  }

  async sendPortalWelcomeEmail(email, username) {
    console.log("📧 Starting to send portal welcome email to:", email);
    console.log("Debug - Portal welcome email params:", {
      email,
      username,
    });

    try {
      const requestBody = {
        template_key: this.portalTemplateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID Portal",
        },
        to: [
          {
            email_address: {
              address: email,
              name: username.split("@")[0],
            },
          },
        ],
        merge_info: {
          USER_EMAIL: email,
          TEMP_PASSWORD: "Please use your registered password to login", // Placeholder
          YEAR: new Date().getFullYear(),
          AWP_APP: "Superuser.ID",
          DOCS_URL: "https://docs.superuser.id",
          API_DOCS: "https://portal.superuser.id",
          EMAIL_SUPPORT: "<EMAIL>",
          DOMAIN_NOTE: "Welcome to Superuser.ID ! Your account has been created successfully.",
        },
        track_clicks: false,
        track_opens: false,
      };

      console.log("📧 Sending portal welcome email with template:", this.portalTemplateKey);
      console.log("Debug - Portal email request body:", JSON.stringify(requestBody, null, 2));

      const response = await fetch(this.apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Zoho-enczapikey ${this.token}`,
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📧 Portal email API response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ Portal email API error response:", {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        });
        throw new Error(`Portal Email API returned ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log("✅ Portal welcome email sent successfully:", responseData);

      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("❌ Error sending portal welcome email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async sendPortalPasswordResetEmail(email, username, resetToken) {
    console.log("📧 Starting to send portal password reset email to:", email);

    try {
      const requestBody = {
        template_key: this.portalTemplateKey,
        from: {
          address: this.fromAddress,
          name: "Superuser.ID Portal",
        },
        to: [
          {
            email_address: {
              address: email,
              name: username,
            },
          },
        ],
        merge_info: {
          USERNAME: username,
          USER_EMAIL: email,
          RESET_TOKEN: resetToken,
          RESET_URL: `https://portal.superuser.id/reset-password?token=${resetToken}`,
          YEAR: new Date().getFullYear(),
          PORTAL_NAME: "Superuser.ID Portal",
          EMAIL_SUPPORT: "<EMAIL>",
          WELCOME_MESSAGE: "You have requested a password reset for your portal account.",
        },
        track_clicks: false,
        track_opens: false,
      };

      const response = await fetch(this.apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Zoho-enczapikey ${this.token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Portal Email API returned ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log("✅ Portal password reset email sent successfully:", responseData);

      return {
        success: true,
        messageId: responseData.message_id,
      };
    } catch (error) {
      console.error("❌ Error sending portal password reset email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
