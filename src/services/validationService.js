export class ValidationService {
  validateUserData(userData) {
    const errors = [];

    if (!userData.email) errors.push("Email is required");
    if (!userData.domain) errors.push("Domain is required");

    if (userData.email && !this.isValidEmail(userData.email)) {
      errors.push("Invalid email format");
    }

    if (userData.domain && !this._isValidDomain(userData.domain)) {
      errors.push("Invalid domain format");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  validatePortalUserData(userData) {
    const errors = [];

    // Check if username (email) is provided
    if (!userData.username) {
      errors.push("Username is required");
    }

    // Check if password is provided
    if (!userData.password) {
      errors.push("Password is required");
    }

    // Validate username as email format
    if (userData.username) {
      if (!this.isValidEmail(userData.username)) {
        errors.push("Username must be a valid email format");
      }
    }

    // Validate username doesn't contain spaces
    if (userData.username && userData.username.includes(" ")) {
      errors.push("Username cannot contain spaces");
    }

    // Validate password requirements
    if (userData.password) {
      if (userData.password.length < 6) {
        errors.push("Password must be at least 6 characters long");
      }
      if (userData.password.includes(" ")) {
        errors.push("Password cannot contain spaces");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  _isValidDomain(domain) {
    // Basic domain validation regex
    const domainRegex =
      /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }
}
