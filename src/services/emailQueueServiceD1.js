// src/services/emailQueueServiceD1.js
// Updated EmailQueueService to use D1 database instead of KV storage

import { DatabaseService } from "./databaseService.js";
import { EmailService } from "./emailService.js";
import { v4 as uuidv4 } from 'uuid';

export class EmailQueueService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
    this.emailService = new EmailService(env);
  }

  async queueEmail(emailData) {
    try {
      const queueId = uuidv4();
      const now = new Date().toISOString();

      const queueData = {
        id: queueId,
        user_id: emailData.user_id || null,
        type: emailData.type || 'general',
        status: 'pending',
        data: emailData,
        attempts: 0,
        queued_at: now,
        process_after: emailData.process_after || now
      };

      await this.db.createEmailQueueItem(queueData);

      console.log(`📧 Email queued successfully: ${queueId}`);
      return queueId;
    } catch (error) {
      console.error("Error queueing email:", error);
      throw error;
    }
  }

  async processQueue(forceProcess = false) {
    try {
      console.log("🔄 Processing email queue...");

      // Get pending email items
      const pendingItems = await this.db.getPendingEmailQueueItems();
      
      if (pendingItems.length === 0) {
        console.log("📧 No pending emails to process");
        return { processed: 0, failed: 0, skipped: 0 };
      }

      console.log(`📧 Found ${pendingItems.length} pending emails`);

      let processed = 0;
      let failed = 0;
      let skipped = 0;

      for (const queueItem of pendingItems) {
        try {
          // Check if it's time to process this email
          const processAfter = new Date(queueItem.process_after);
          const now = new Date();

          if (!forceProcess && now < processAfter) {
            console.log(`⏰ Skipping email ${queueItem.id} - not ready to process`);
            skipped++;
            continue;
          }

          console.log(`📧 Processing email: ${queueItem.id}`);

          // Send email
          const result = await this.emailService.sendEmail(queueItem.data);

          if (result.success) {
            // Mark as sent and delete from queue
            await this.db.deleteEmailQueueItem(queueItem.id);
            
            // Optionally store in sent emails table for tracking
            // await this.db.createEmailQueueItem({
            //   ...queueItem,
            //   id: `sent_${queueItem.id}`,
            //   status: 'sent',
            //   sent_at: new Date().toISOString()
            // });

            console.log(`✅ Email sent successfully: ${queueItem.id}`);
            processed++;
          } else {
            // Update attempts and mark as failed if max attempts reached
            const newAttempts = (queueItem.attempts || 0) + 1;
            const maxAttempts = 3;

            if (newAttempts >= maxAttempts) {
              await this.db.updateEmailQueueItem(queueItem.id, {
                status: 'failed',
                attempts: newAttempts,
                sent_at: null,
                error_message: result.error || 'Max attempts reached'
              });
              
              console.log(`❌ Email failed permanently: ${queueItem.id}`);
              failed++;
            } else {
              // Retry later
              const retryDelay = Math.pow(2, newAttempts) * 60 * 1000; // Exponential backoff
              const retryTime = new Date(Date.now() + retryDelay).toISOString();
              
              await this.db.updateEmailQueueItem(queueItem.id, {
                status: 'pending',
                attempts: newAttempts,
                sent_at: null,
                error_message: result.error || 'Send failed'
              });

              console.log(`🔄 Email retry scheduled: ${queueItem.id} (attempt ${newAttempts})`);
              skipped++;
            }
          }
        } catch (error) {
          console.error(`Error processing email ${queueItem.id}:`, error);
          
          // Update attempts
          const newAttempts = (queueItem.attempts || 0) + 1;
          await this.db.updateEmailQueueItem(queueItem.id, {
            status: newAttempts >= 3 ? 'failed' : 'pending',
            attempts: newAttempts,
            sent_at: null,
            error_message: error.message
          });
          
          failed++;
        }
      }

      const results = { processed, failed, skipped };
      console.log("📧 Queue processing completed:", results);
      
      return results;
    } catch (error) {
      console.error("Error processing email queue:", error);
      throw error;
    }
  }

  async retryFailedEmails() {
    try {
      console.log("🔄 Retrying failed emails...");

      // This would require a query to get failed emails
      // For now, we'll implement a simple approach
      const stats = await this.getQueueStatus();
      
      console.log("📧 Failed email retry completed");
      return stats;
    } catch (error) {
      console.error("Error retrying failed emails:", error);
      throw error;
    }
  }

  async getQueueStatus() {
    try {
      const stats = await this.db.getEmailQueueStats();
      
      return {
        pending: stats.pending || 0,
        sent: stats.sent || 0,
        failed: stats.failed || 0,
        total: (stats.pending || 0) + (stats.sent || 0) + (stats.failed || 0)
      };
    } catch (error) {
      console.error("Error getting queue status:", error);
      return { pending: 0, sent: 0, failed: 0, total: 0 };
    }
  }

  async getPendingEmails() {
    try {
      return await this.db.getPendingEmailQueueItems();
    } catch (error) {
      console.error("Error getting pending emails:", error);
      return [];
    }
  }

  async getQueueItems(status = null) {
    try {
      if (status) {
        // This would require additional database methods for filtering by status
        // For now, return all pending items
        return await this.db.getPendingEmailQueueItems();
      } else {
        return await this.db.getPendingEmailQueueItems();
      }
    } catch (error) {
      console.error("Error getting queue items:", error);
      return [];
    }
  }

  async getDebugInfo() {
    try {
      const status = await this.getQueueStatus();
      
      return {
        queueStatus: status,
        timestamp: new Date().toISOString(),
        environment: {
          hasZeptoToken: !!this.env.ZEPTO_TOKEN,
          hasFromAddress: !!this.env.ZEPTO_FROM_ADDRESS,
          zeptoToken: {
            exists: !!this.env.ZEPTO_TOKEN,
            length: this.env.ZEPTO_TOKEN?.length || 0,
            preview: this.env.ZEPTO_TOKEN
              ? `${this.env.ZEPTO_TOKEN.substring(0, 10)}...`
              : null,
          },
          fromAddress: this.env.ZEPTO_FROM_ADDRESS || "not configured",
        },
      };
    } catch (error) {
      console.error("Error getting debug info:", error);
      throw error;
    }
  }

  // Helper method to queue welcome email
  async queueWelcomeEmail(userEmail, userName = null) {
    return await this.queueEmail({
      type: 'welcome',
      to: userEmail,
      subject: 'Welcome to SuperSense API',
      template: 'welcome',
      data: {
        userName: userName || userEmail,
        email: userEmail
      }
    });
  }

  // Helper method to queue tier upgrade email
  async queueTierUpgradeEmail(userEmail, tierName, userName = null) {
    return await this.queueEmail({
      type: 'tier_upgrade',
      to: userEmail,
      subject: `Tier Upgraded to ${tierName}`,
      template: 'tier_upgrade',
      data: {
        userName: userName || userEmail,
        email: userEmail,
        tierName: tierName
      }
    });
  }

  // Helper method to queue quota warning email
  async queueQuotaWarningEmail(userEmail, quotaInfo, userName = null) {
    return await this.queueEmail({
      type: 'quota_warning',
      to: userEmail,
      subject: 'API Quota Warning',
      template: 'quota_warning',
      data: {
        userName: userName || userEmail,
        email: userEmail,
        quotaInfo: quotaInfo
      }
    });
  }

  // Compatibility method to match the interface expected by other services
  // This allows the D1-based service to work with code expecting addToQueue()
  async addToQueue(emailData) {
    return await this.queueEmail(emailData);
  }
}