// src/services/webhookService.js
import { TierService as TierServiceD1 } from "./tierServiceD1.js";
import { EmailQueueService } from "./emailQueueService";

export class WebhookService {
  constructor(env) {
    this.env = env;
    this.tierService = new TierServiceD1(env);
    this.emailQueueService = new EmailQueueServiceD1(env);
    this.isDevelopment =
      env.NODE_ENV === "development" ||
      env.ENVIRONMENT === "development" ||
      env.PAYPAL_SANDBOX === "true";
  }

  logWebhook(message, data = {}) {
    const timestamp = new Date().toISOString();
    console.log("\n📮 WEBHOOK:", message);
    console.log("⌚", timestamp);
    console.log("📄 Data:", JSON.stringify(data, null, 2), "\n");
  }

  logError(message, error) {
    console.error("\n❌ WEBHOOK ERROR:", message);
    console.error("🔍 Details:", error);
    console.error("📍 Stack:", error.stack, "\n");
  }

  async verifyWebhookSignature(request) {
    const headers = request.headers;

    // Check if this is a test/simulation request
    const isSimulation = headers.get("PAYPAL-SIMULATION") === "true";

    // Skip verification for development/sandbox mode or simulated webhooks
    if (this.isDevelopment || isSimulation) {
      console.log(
        "📝 Development/Sandbox mode or simulation: Skipping webhook signature verification"
      );
      return true;
    }

    // Production verification logic
    const webhookId = this.env.PAYPAL_WEBHOOK_ID;
    const transmissionId = headers.get("paypal-transmission-id");
    const timestamp = headers.get("paypal-transmission-time");
    const signature = headers.get("paypal-transmission-sig");
    const certUrl = headers.get("paypal-cert-url");

    console.log("📨 Webhook headers received:", {
      webhookId: webhookId ? "(present)" : "(missing)",
      transmissionId,
      timestamp,
      signature: signature ? "(present)" : "(missing)",
      certUrl,
      environment: this.isDevelopment ? "development" : "production",
    });

    if (!webhookId || !transmissionId || !timestamp || !signature || !certUrl) {
      if (this.isDevelopment) {
        console.warn(
          "⚠️ Missing webhook headers in development mode - continuing anyway"
        );
        return true;
      }
      throw new Error("Missing required webhook headers");
    }

    // Here you would implement actual PayPal signature verification
    // using their API for production environments
    return true;
  }

  async processWebhookEvent(event) {
    this.logWebhook("Received webhook event", {
      eventType: event.event_type,
      eventId: event.id,
      timestamp: event.create_time,
    });
    try {
      // Store webhook event
      await this.storeWebhookEvent(event);

      // Process based on event type
      let result;
      switch (event.event_type) {
        case "BILLING.SUBSCRIPTION.ACTIVATED":
          this.logWebhook("Processing subscription activation");
          result = await this.handleSubscriptionActivated(event);
          break;

        case "PAYMENT.SALE.COMPLETED":
          this.logWebhook("Processing payment completion");
          result = await this.handlePaymentCompleted(event);
          break;

        case "BILLING.SUBSCRIPTION.CANCELLED":
          this.logWebhook("Processing subscription cancellation");
          result = await this.handleSubscriptionCancelled(event);
          break;

        case "BILLING.SUBSCRIPTION.PAYMENT.FAILED":
          this.logWebhook("Processing payment failure");
          result = await this.handlePaymentFailed(event);
          break;

        default:
          this.logWebhook("Unhandled event type", {
            eventType: event.event_type,
          });
          return { status: "unhandled", eventType: event.event_type };
      }

      this.logWebhook("Successfully processed webhook", result);
      return result;
    } catch (error) {
      this.logError("Failed to process webhook", error);
      throw error;
    }
  }

  async handleSubscriptionActivated(event) {
    try {
      const subscription = event.resource;
      this.logWebhook("Processing subscription activation", {
        subscriptionId: subscription.id,
      });

      // Find our stored subscription
      const subscriptionData = await this.getSubscriptionData(subscription.id);
      if (!subscriptionData) {
        throw new Error(`Subscription not found: ${subscription.id}`);
      }

      this.logWebhook("Found subscription data", subscriptionData);

      // Update user tier
      this.logWebhook("Updating user tier", {
        userId: subscriptionData.userId,
        tier: subscriptionData.tier,
      });
      await this.tierService.setUserTier(
        subscriptionData.userId,
        subscriptionData.tier
      );

      // Queue welcome email
      this.logWebhook("Queueing welcome email", {
        email: subscription.subscriber.email_address,
      });
      await this.emailQueueService.addToQueue({
        email: subscription.subscriber.email_address,
        type: "subscription_activated",
        data: {
          tier: subscriptionData.tier,
          price: subscriptionData.price,
          nextBillingDate: subscription.billing_info?.next_billing_time,
        },
      });

      // Update subscription status
      this.logWebhook("Updating subscription status", {
        status: subscription.status,
        nextBilling: subscription.billing_info?.next_billing_time,
      });
      await this.updateSubscriptionStatus(
        subscriptionData.userId,
        subscription.id,
        {
          status: subscription.status,
          nextBillingTime: subscription.billing_info?.next_billing_time,
          lastPayment: subscription.billing_info?.last_payment,
          startTime: subscription.start_time,
        }
      );

      const result = { status: "activated", subscriptionId: subscription.id };
      this.logWebhook("Successfully activated subscription", result);
      return result;
    } catch (error) {
      this.logError("Failed to activate subscription", error);
      throw error;
    }
  }

  async handlePaymentCompleted(event) {
    const payment = event.resource;
    const subscriptionId = payment.billing_agreement_id;
    this.logWebhook("Processing payment completion", {
      paymentId: payment.id,
      amount: payment.amount,
    });

    const subscriptionData = await this.getSubscriptionData(subscriptionId);
    if (!subscriptionData) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }

    // Record payment
    await this.recordPayment({
      id: payment.id,
      subscriptionId,
      userId: subscriptionData.userId,
      amount: payment.amount.total,
      currency: payment.amount.currency,
      status: payment.state,
      createTime: payment.create_time,
      updateTime: payment.update_time,
    });

    // Send confirmation email
    await this.emailQueueService.addToQueue({
      email: subscriptionData.email,
      type: "payment_success",
      data: {
        amount: payment.amount.total,
        currency: payment.amount.currency,
        date: payment.create_time,
        tier: subscriptionData.tier,
      },
    });

    return { status: "completed", paymentId: payment.id };
  }

  async handleSubscriptionCancelled(event) {
    const subscription = event.resource;
    this.logWebhook("Processing subscription cancellation", {
      subscriptionId: subscription.id,
    });
    const subscriptionData = await this.getSubscriptionData(subscription.id);
    if (!subscriptionData) {
      throw new Error(`Subscription not found: ${subscription.id}`);
    }

    // Downgrade to starter tier
    await this.tierService.setUserTier(subscriptionData.userId, "starter");

    // Send cancellation email
    await this.emailQueueService.addToQueue({
      email: subscription.subscriber.email_address,
      type: "subscription_cancelled",
      data: {
        cancellationDate: subscription.status_update_time,
        previousTier: subscriptionData.tier,
      },
    });

    // Update subscription status
    await this.updateSubscriptionStatus(
      subscriptionData.userId,
      subscription.id,
      {
        status: subscription.status,
        cancellationTime: subscription.status_update_time,
      }
    );

    return { status: "cancelled", subscriptionId: subscription.id };
  }

  async handlePaymentFailed(event) {
    const subscription = event.resource;
    const subscriptionData = await this.getSubscriptionData(subscription.id);
    if (!subscriptionData) {
      throw new Error(`Subscription not found: ${subscription.id}`);
    }

    // Send failure notification
    await this.emailQueueService.addToQueue({
      email: subscription.subscriber.email_address,
      type: "payment_failed",
      data: {
        amount: subscription.billing_info.outstanding_balance.value,
        currency: subscription.billing_info.outstanding_balance.currency_code,
        failedPaymentsCount: subscription.billing_info.failed_payments_count,
        nextRetry: subscription.billing_info.next_payment_retry_time,
      },
    });

    // Update subscription status
    await this.updateSubscriptionStatus(
      subscriptionData.userId,
      subscription.id,
      {
        status: subscription.status,
        failedPaymentsCount: subscription.billing_info.failed_payments_count,
        lastFailedPayment: subscription.billing_info.last_failed_payment,
      }
    );

    return { status: "failed", subscriptionId: subscription.id };
  }

  // Helper methods
  async getSubscriptionData(subscriptionId) {
    this.logWebhook("Looking up subscription data", { subscriptionId });
    const { keys } = await this.env.USERS_KV.list({ prefix: "subscription:" });

    for (const key of keys) {
      const data = await this.env.USERS_KV.get(key.name, "json");
      if (data?.subscriptionId === subscriptionId) {
        this.logWebhook("Found subscription data", {
          userId: key.name.split(":")[1],
          subscription: data,
        });
        return {
          ...data,
          userId: key.name.split(":")[1],
        };
      }
    }

    this.logWebhook("Subscription not found", { subscriptionId });
    return null;
  }

  async storeWebhookEvent(event) {
    const eventId = `webhook_event:${event.id}`;
    await this.env.USERS_KV.put(
      eventId,
      JSON.stringify({
        ...event,
        receivedAt: new Date().toISOString(),
      })
    );
  }

  async updateSubscriptionStatus(userId, subscriptionId, updates) {
    const key = `subscription:${userId}`;
    const data = await this.env.USERS_KV.get(key, "json");

    if (data && data.subscriptionId === subscriptionId) {
      await this.env.USERS_KV.put(
        key,
        JSON.stringify({
          ...data,
          ...updates,
          updatedAt: new Date().toISOString(),
        })
      );
    }
  }

  async recordPayment(payment) {
    await this.env.USERS_KV.put(
      `payment:${payment.id}`,
      JSON.stringify({
        ...payment,
        recordedAt: new Date().toISOString(),
      })
    );
  }
}
