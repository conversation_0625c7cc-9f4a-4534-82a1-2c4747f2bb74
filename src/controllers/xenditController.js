import { XenditService } from "../services/xenditService";
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import QRCode from "qrcode";

export class XenditController {
  constructor(env) {
    this.env = env;
    this.xenditService = new XenditService(env);
  }

  async createInvoice(request) {
    try {
      const data = await request.json();
      const {
        external_id,
        amount,
        payer_email,
        description,
        currency = "IDR",
      } = data;

      // Validate required fields
      if (!external_id || !amount || !payer_email) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "external_id, amount, and payer_email are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Create invoice data
      const invoiceData = {
        external_id,
        amount,
        payer_email,
        description,
        currency,
        payment_methods: data.payment_methods || ["QRIS"],
        should_send_email: data.should_send_email,
      };

      const invoice = await this.xenditService.createInvoice(invoiceData);

      return new Response(
        JSON.stringify({
          success: true,
          data: invoice,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async createQRISPayment(request) {
    try {
      const data = await request.json();
      const { external_id, amount, description } = data;

      // Validate required fields
      if (!external_id || !amount) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "external_id and amount are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Create QRIS payment
      const qrisPayment = await this.xenditService.createQRISPayment({
        external_id,
        amount,
        description,
      });

      // Return success response
      return new Response(
        JSON.stringify({
          success: true,
          data: qrisPayment,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error creating QRIS payment:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create QRIS payment",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async generateQRCode(request) {
    try {
      const { qr_string } = await request.json();

      if (!qr_string) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "qr_string is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Generate QR code as PNG
      const qrBuffer = await QRCode.toBuffer(qr_string, {
        errorCorrectionLevel: "H",
        margin: 1,
        scale: 8,
      });

      return new Response(qrBuffer, {
        status: 200,
        headers: {
          "Content-Type": "image/png",
          "Content-Length": qrBuffer.length.toString(),
        },
      });
    } catch (error) {
      console.error("QR Code Generation Error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to generate QR code image",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async createVirtualAccount(request) {
    try {
      const data = await request.json();
      console.log("Received VA creation request:", data);
      
      const {
        external_id,
        bank_code,
        name,
        amount,
        is_closed = true,
        expected_amount,
        min_amount,
        max_amount,
        description,
        is_single_use,
        currency,
        expiration_date,
      } = data;

      // Validate required fields
      if (!external_id || !bank_code || !name) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "external_id, bank_code, and name are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Additional validation for closed VA
      if (is_closed && !amount) {
        console.log("Validation failed: amount required for closed VA");
        return new Response(
          JSON.stringify({
            success: false,
            error: "amount is required for closed virtual accounts",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      console.log("Creating VA with params:", {
        external_id,
        bank_code,
        name,
        amount,
        is_closed,
        expiration_date
      });

      // Create Virtual Account
      const vaPayment = await this.xenditService.createVirtualAccount({
        external_id,
        bank_code,
        name,
        amount,
        is_closed,
        expected_amount: expected_amount || amount, // Use expected_amount if provided, otherwise use amount
        min_amount,
        max_amount,
        description,
        is_single_use,
        currency,
        expiration_date: expiration_date && new Date(expiration_date) > new Date() 
          ? expiration_date 
          : new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      });

      // Return success response
      return new Response(
        JSON.stringify({
          success: true,
          data: vaPayment,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error creating Virtual Account:", error);
      console.error("Error stack:", error.stack);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create virtual account",
          details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async createPaymentRequest(request) {
    try {
      const data = await request.json();
      const {
        amount,
        currency,
        payment_method,
        customer_id,
        description,
        metadata,
        reference_id,
      } = data;

      // Validate required fields
      if (!amount || !currency || !payment_method || !customer_id) {
        return new Response(
          JSON.stringify({
            success: false,
            error:
              "amount, currency, payment_method, and customer_id are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Add QRIS validation
      if (!payment_method.type || payment_method.type !== "QRIS") {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Only QRIS payment method is supported",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get idempotency key from headers if present
      const idempotencyKey = request.headers.get("idempotency-key");

      // Create payment request
      const paymentRequest = await this.xenditService.createPaymentRequest({
        amount,
        currency,
        payment_method,
        customer_id,
        description,
        metadata,
        reference_id,
        idempotency_key: idempotencyKey,
      });

      // Return success response
      return new Response(
        JSON.stringify({
          success: true,
          data: paymentRequest,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error creating payment request:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create payment request",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async createCustomer(request) {
    try {
      console.log("Received customer creation request");

      // Log the raw request
      const rawBody = await request.text();
      console.log("Raw request body:", rawBody);

      // Parse the JSON manually
      let data;
      try {
        data = JSON.parse(rawBody);
      } catch (parseError) {
        console.error("Error parsing JSON:", parseError);
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid JSON format in request body",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      console.log("Parsed request data:", data);

      const {
        reference_id,
        type,
        email,
        given_names,
        surname,
        mobile_number,
        addresses,
        description,
        metadata,
      } = data;

      console.log("Extracted fields:", {
        reference_id,
        type,
        email,
        given_names,
        surname,
        mobile_number,
        description,
      });

      // Validate required fields according to Xendit API docs
      if (!reference_id || !email || !given_names) {
        console.log("Validation failed - missing required fields");
        return new Response(
          JSON.stringify({
            success: false,
            error: "reference_id, email, and given_names are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      console.log("Creating customer with Xendit service");
      // Create customer
      const customer = await this.xenditService.createCustomer({
        reference_id,
        type,
        email,
        given_names,
        surname,
        mobile_number,
        addresses,
        description,
        metadata,
      });

      console.log("Customer created successfully:", customer);
      // Return success response
      return new Response(
        JSON.stringify({
          success: true,
          data: customer,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error creating customer:", error);
      console.error("Error stack:", error.stack);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to create customer",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async validateDirectDebitOTP(request) {
    try {
      // Extract payment_method_id from URL parameters
      const requestUrl = new URL(request.url);
      const pathParts = requestUrl.pathname.split("/");
      const payment_method_id = pathParts[pathParts.length - 2]; // Get the ID from the URL path

      // Parse the request body
      const body = await request.json();
      const { otp_code } = body;

      if (!payment_method_id || !otp_code) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "payment_method_id and otp_code are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      console.log("Validating OTP for payment method:", payment_method_id);
      const xenditUrl = `${this.xenditService.baseUrl}/v2/payment_methods/${payment_method_id}/auth`;

      const response = await fetch(xenditUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Basic ${btoa(this.xenditService.secretKey + ":")}`,
          "api-version": "2020-05-19",
        },
        body: JSON.stringify({ otp_code }),
      });

      const responseData = await response.json();
      console.log("OTP validation response:", responseData);

      if (!response.ok) {
        return new Response(
          JSON.stringify({
            success: false,
            error: responseData.message || "Failed to validate OTP",
          }),
          {
            status: response.status,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: responseData,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error validating OTP:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to validate OTP",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async handleWebhook(request) {
    try {
      const data = await request.json();
      console.log("Received Xendit webhook event:", data);

      // Log webhook payload to file for debugging
      await this._logWebhookPayload(data, request);

      // Verify webhook authenticity with callback token
      const callbackToken = request.headers.get("x-callback-token");
      if (!callbackToken || callbackToken !== this.env.XENDIT_WEBHOOK_TOKEN) {
        console.error(
          "Invalid webhook token. Expected:",
          this.env.XENDIT_WEBHOOK_TOKEN,
          " but got:",
          callbackToken
        );
        return new Response(
          JSON.stringify({ success: false, error: "Invalid webhook token" }),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      } else {
        console.log("Valid webhook token received:", callbackToken);
      }

      // Process payment notification
      if (data.status === "PAID") {
        console.log(`Payment with ID ${data.id} has been completed`);

        // Extract payment information
        const {
          external_id,
          amount,
          payer_email,
          description,
          payment_method,
          payment_channel,
          payment_details,
        } = data;

        // First, check if we have existing payment data from /payment/store
        const existingPaymentKey = `payment:${external_id}`;
        const existingPaymentData = await this.env.USERS_KV.get(
          existingPaymentKey
        );

        let userEmail = payer_email;
        let tierToUpgrade = null;

        if (existingPaymentData) {
          console.log(`Found existing payment data for ${external_id}`);
          const parsedPaymentData = JSON.parse(existingPaymentData);

          // Use the user_email from the stored data if available
          userEmail = parsedPaymentData.user_email || payer_email;

          // Get the tier from the stored payment data
          if (
            parsedPaymentData.payment_data &&
            parsedPaymentData.payment_data.tier
          ) {
            tierToUpgrade = parsedPaymentData.payment_data.tier;
            console.log(
              `Using original tier from payment data: ${tierToUpgrade}`
            );
          }

          // Update the payment status in the stored data
          parsedPaymentData.payment_data.status = "PAID";

          // Add additional payment details from the webhook
          parsedPaymentData.payment_data.payment_id = data.id;
          parsedPaymentData.payment_data.paid_at = data.paid_at;
          parsedPaymentData.payment_data.payment_method = payment_method;
          parsedPaymentData.payment_data.payment_channel = payment_channel;
          parsedPaymentData.payment_data.payment_details = payment_details;

          // Update the stored payment data
          await this.env.USERS_KV.put(
            existingPaymentKey,
            JSON.stringify(parsedPaymentData)
          );

          console.log(
            `Updated payment data for ${external_id} with status PAID`
          );
        }

        // Check for purchase data first
        const purchaseData = await this.env.USERS_KV.get(
          `xendit_purchase:${external_id}`,
          "json"
        );

        if (purchaseData) {
          console.log("Found purchase data:", purchaseData);

          const tierService = new TierServiceD1(this.env);

          try {
            // Upgrade the user's tier with lifetime access
            await tierService.upgradeEmailTier(
              purchaseData.email,
              purchaseData.tier,
              {
                purchaseId: external_id,
                addon1: purchaseData.addons?.addon1 || false,
                addon2: purchaseData.addons?.addon2 || false,
                isPermanent: true, // Lifetime purchase
                purchaseDate: new Date().toISOString(),
                paymentMethod: "xendit",
                paymentChannel: payment_channel,
                paymentDetails: payment_details,
              }
            );

            // Update purchase status
            purchaseData.status = "COMPLETED";
            purchaseData.completed_at = new Date().toISOString();
            purchaseData.payment_id = data.id;
            purchaseData.paid_at = data.paid_at;
            purchaseData.payment_method = payment_method;
            purchaseData.payment_channel = payment_channel;
            purchaseData.payment_details = payment_details;

            await this.env.USERS_KV.put(
              `xendit_purchase:${external_id}`,
              JSON.stringify(purchaseData)
            );

            console.log(
              `Successfully completed purchase for ${purchaseData.email} - ${purchaseData.tier} tier`
            );

            // Send webhook notification to frontend
            await this._sendWebhookToFrontend(data);

            // Store payment record
            await this._storePaymentRecord(data);

            return new Response(JSON.stringify({ success: true, type: "purchase" }), {
              status: 200,
              headers: { "Content-Type": "application/json" },
            });
          } catch (tierError) {
            console.error(`Error upgrading tier for purchase: ${tierError.message}`);
            return new Response(
              JSON.stringify({
                success: false,
                error: tierError.message,
                type: "purchase",
                details: {
                  tier: purchaseData.tier,
                  userEmail: purchaseData.email,
                },
              }),
              {
                status: 400,
                headers: { "Content-Type": "application/json" },
              }
            );
          }
        }

        // Get subscription data from KV using external_id
        const subscriptionData = await this.env.USERS_KV.get(
          `xendit_ref:${external_id}`,
          "json"
        );

        if (subscriptionData) {
          console.log("Found subscription data:", subscriptionData);

          const tierService = new TierServiceD1(this.env);

          // Determine which tier to upgrade to based on the payment amount or stored subscription data
          // If we already have a tier from the existing payment data, use that
          tierToUpgrade =
            tierToUpgrade ||
            subscriptionData.tier ||
            this._determineTierFromAmount(amount);

          console.log(`Using tier for upgrade: ${tierToUpgrade}`);

          // Prepare addon options
          const addonOptions = {
            subscriptionId: external_id,
            addon1: subscriptionData.addons?.addon1 || false,
            addon2: subscriptionData.addons?.addon2 || false,
            paymentMethod: payment_method,
            paymentChannel: payment_channel,
            paymentDetails: payment_details,
          };

          try {
            // Upgrade the user's tier
            await tierService.upgradeEmailTier(
              userEmail,
              tierToUpgrade,
              addonOptions
            );

            console.log(
              `Successfully upgraded ${userEmail} to ${tierToUpgrade} tier`
            );

            // Send webhook notification to frontend
            await this._sendWebhookToFrontend(data);
          } catch (tierError) {
            console.error(`Error upgrading tier: ${tierError.message}`);
            return new Response(
              JSON.stringify({
                success: false,
                error: tierError.message,
                details: {
                  tier: tierToUpgrade,
                  userEmail,
                },
              }),
              {
                status: 400,
                headers: { "Content-Type": "application/json" },
              }
            );
          }

          // Store payment record if we didn't have existing data
          if (!existingPaymentData) {
            await this._storePaymentRecord(data);
          }

          // Store subscription data in payment record before cleanup
          const existingPaymentKey = `payment:${external_id}`;
          const existingPaymentData = await this.env.USERS_KV.get(existingPaymentKey);
          
          if (existingPaymentData) {
            const parsedPaymentData = JSON.parse(existingPaymentData);
            // Add subscription data to payment record
            parsedPaymentData.subscription_data = subscriptionData;
            await this.env.USERS_KV.put(existingPaymentKey, JSON.stringify(parsedPaymentData));
            console.log("Stored subscription data in payment record for future reference");
          }
          
          // Clean up the temporary reference (but keep data in payment record)
          await this.env.USERS_KV.delete(`xendit_ref:${external_id}`);
        } else {
          console.warn(
            `No subscription data found for external_id: ${external_id}`
          );

          // If we have a tier from existing payment data, use it
          if (tierToUpgrade) {
            const tierService = new TierServiceD1(this.env);

            try {
              // Upgrade the user's tier
              await tierService.upgradeEmailTier(userEmail, tierToUpgrade, {
                subscriptionId: external_id,
                paymentMethod: payment_method,
                paymentChannel: payment_channel,
                paymentDetails: payment_details,
              });

              console.log(
                `Upgraded ${userEmail} to ${tierToUpgrade} tier based on stored payment data`
              );
            } catch (tierError) {
              console.error(`Error upgrading tier: ${tierError.message}`);
              return new Response(
                JSON.stringify({
                  success: false,
                  error: tierError.message,
                  details: {
                    tier: tierToUpgrade,
                    userEmail,
                  },
                }),
                {
                  status: 400,
                  headers: { "Content-Type": "application/json" },
                }
              );
            }
          }
          // Even if we don't have subscription data, we can try to process the payment
          // based on the email and description
          else if (payer_email && description) {
            const tierMatch = description.match(/Upgrade to (\w+) Plan/i);
            if (tierMatch && tierMatch[1]) {
              const tierName = tierMatch[1].toLowerCase();

              const tierService = new TierServiceD1(this.env);

              try {
                // Upgrade the user's tier using the tier name directly from the description
                await tierService.upgradeEmailTier(payer_email, tierName, {
                  subscriptionId: external_id,
                  paymentMethod: payment_method,
                  paymentChannel: payment_channel,
                  paymentDetails: payment_details,
                });

                console.log(
                  `Upgraded ${payer_email} to ${tierName} tier based on payment description`
                );

                // Send webhook notification to frontend
                await this._sendWebhookToFrontend(data);
              } catch (tierError) {
                console.error(`Error upgrading tier: ${tierError.message}`);
                return new Response(
                  JSON.stringify({
                    success: false,
                    error: tierError.message,
                    details: {
                      tier: tierName,
                      userEmail: payer_email,
                    },
                  }),
                  {
                    status: 400,
                    headers: { "Content-Type": "application/json" },
                  }
                );
              }

              // Store payment record if we didn't have existing data
              if (!existingPaymentData) {
                await this._storePaymentRecord(data);
              }
            }
          }
        }
      }

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response(
        JSON.stringify({ success: false, error: error.message }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  // Helper method to determine tier from payment amount
  _determineTierFromAmount(amount) {
    if (amount >= 990000) { // 99 USD * 10000 IDR = 990,000 IDR
      return "enterprise";
    } else if (amount >= 400000) { // 40 USD * 10000 IDR = 400,000 IDR  
      return "pro";
    } else if (amount >= 150000) { // 15 USD * 10000 IDR = 150,000 IDR
      return "basic";
    } else {
      return "starter";
    }
  }

  // Helper method to log webhook payload to file
  async _logWebhookPayload(payload, request) {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        payload,
        headers: {
          'user-agent': request.headers.get('user-agent'),
          'x-callback-token': request.headers.get('x-callback-token') ? 'PRESENT' : 'MISSING',
          'content-type': request.headers.get('content-type'),
          'x-forwarded-for': request.headers.get('x-forwarded-for'),
          'cf-connecting-ip': request.headers.get('cf-connecting-ip')
        }
      };

      const logText = `
=== XENDIT WEBHOOK LOG (Controller) ===
Timestamp: ${timestamp}
External ID: ${payload.external_id || 'N/A'}
Status: ${payload.status || 'N/A'}
Amount: ${payload.amount || 'N/A'}
Payer Email: ${payload.payer_email || 'N/A'}
Payment Method: ${payload.payment_method || 'N/A'}
Payment Channel: ${payload.payment_channel || 'N/A'}
Payment ID: ${payload.id || 'N/A'}

Full Payload:
${JSON.stringify(payload, null, 2)}

Headers:
${JSON.stringify(logEntry.headers, null, 2)}

========================================

`;

      // Store in KV with timestamp key for easy retrieval
      const logKey = `xendit_webhook_controller_log:${timestamp}`;
      await this.env.USERS_KV.put(logKey, logText);

      // Also store latest webhook for quick access
      await this.env.USERS_KV.put('xendit_webhook_controller_latest', logText);

      // Save to file log in public folder
      await this._saveWebhookToLogFile(payload, request, timestamp);

      console.log(`Xendit webhook payload logged to controller with key: ${logKey}`);
    } catch (error) {
      console.error('Error logging webhook payload in controller:', error.message);
      // Don't throw error to avoid breaking webhook flow
    }
  }

  // Helper method to save webhook payload to log file
  async _saveWebhookToLogFile(payload, request, timestamp) {
    try {
      const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      const logFileName = `xendit-webhook-${date}.log`;
      
      const logEntry = {
        timestamp: timestamp,
        external_id: payload.external_id || 'N/A',
        status: payload.status || 'N/A',
        amount: payload.amount || 'N/A',
        payer_email: payload.payer_email || 'N/A',
        payment_method: payload.payment_method || 'N/A',
        payment_channel: payload.payment_channel || 'N/A',
        payment_id: payload.id || 'N/A',
        headers: {
          'user-agent': request.headers.get('user-agent'),
          'x-callback-token': request.headers.get('x-callback-token') ? 'PRESENT' : 'MISSING',
          'content-type': request.headers.get('content-type'),
          'x-forwarded-for': request.headers.get('x-forwarded-for'),
          'cf-connecting-ip': request.headers.get('cf-connecting-ip')
        },
        full_payload: payload
      };

      const logLine = `[${timestamp}] XENDIT_WEBHOOK | External_ID: ${payload.external_id || 'N/A'} | Status: ${payload.status || 'N/A'} | Amount: ${payload.amount || 'N/A'} | Email: ${payload.payer_email || 'N/A'} | Method: ${payload.payment_method || 'N/A'} | Channel: ${payload.payment_channel || 'N/A'} | Payment_ID: ${payload.id || 'N/A'} | Full_Payload: ${JSON.stringify(payload)}\n`;

      // Store log file content in KV (since we can't write to actual files in Cloudflare Workers)
      const existingLog = await this.env.USERS_KV.get(`webhook_log_file:${logFileName}`) || '';
      const updatedLog = existingLog + logLine;
      
      await this.env.USERS_KV.put(`webhook_log_file:${logFileName}`, updatedLog);

      // Also store in a general webhook log file
      const generalLogKey = 'webhook_log_file:xendit-webhook-all.log';
      const existingGeneralLog = await this.env.USERS_KV.get(generalLogKey) || '';
      const updatedGeneralLog = existingGeneralLog + logLine;
      
      await this.env.USERS_KV.put(generalLogKey, updatedGeneralLog);

      console.log(`Webhook payload saved to log file: ${logFileName}`);
    } catch (error) {
      console.error('Error saving webhook to log file:', error.message);
      // Don't throw error to avoid breaking webhook flow
    }
  }

  // Helper method to send webhook data to frontend
  async _sendWebhookToFrontend(webhookData) {
    try {
      const frontendUrl = this.env.FRONTEND_URL || "http://localhost:3001";
      const webhookEndpoint = `${frontendUrl}/api/xendit/webhook-listener`;
      
      console.log(`Sending webhook notification to frontend: ${webhookEndpoint}`);
      
      // Prepare payload for frontend with additional metadata
      const frontendPayload = {
        // Original webhook data
        id: webhookData.id,
        external_id: webhookData.external_id,
        status: webhookData.status,
        amount: webhookData.amount,
        paid_amount: webhookData.paid_amount,
        payer_email: webhookData.payer_email,
        payment_method: webhookData.payment_method,
        payment_channel: webhookData.payment_channel,
        payment_details: webhookData.payment_details,
        paid_at: webhookData.paid_at,
        created: webhookData.created,
        updated: webhookData.updated,
        description: webhookData.description,
        currency: webhookData.currency,
        
        // Additional metadata for frontend
        type: this._determineWebhookType(webhookData),
        timestamp: new Date().toISOString(),
        source: 'backend-webhook-handler',
        backend_processed_at: new Date().toISOString(),
        
        // Processing status
        processing_status: webhookData.status === 'PAID' ? 'completed' : 'pending',
        
        // User information if available
        user_email: webhookData.payer_email,
        
        // Payment summary
        payment_summary: {
          method: webhookData.payment_method,
          channel: webhookData.payment_channel,
          amount: webhookData.amount,
          currency: webhookData.currency || 'IDR',
          status: webhookData.status,
          paid_at: webhookData.paid_at
        }
      };
      
      console.log('Frontend payload prepared:', {
        id: frontendPayload.id,
        external_id: frontendPayload.external_id,
        status: frontendPayload.status,
        type: frontendPayload.type,
        amount: frontendPayload.amount
      });
      
      const response = await fetch(webhookEndpoint, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'User-Agent': 'Backend-Webhook-Forwarder/1.0',
          'X-Webhook-Source': 'xendit-backend',
          'X-Webhook-Timestamp': new Date().toISOString(),
          'X-External-ID': webhookData.external_id || 'unknown'
        },
        body: JSON.stringify(frontendPayload)
      });

      const responseText = await response.text();
      
      if (response.ok) {
        console.log('✅ Successfully sent webhook notification to frontend');
        console.log('Frontend response:', responseText);
        
        // Log successful forwarding
        await this._logWebhookForwarding(webhookData, frontendPayload, 'success', responseText);
      } else {
        console.error(`❌ Failed to send webhook to frontend: ${response.status} ${response.statusText}`);
        console.error('Frontend error response:', responseText);
        
        // Log failed forwarding
        await this._logWebhookForwarding(webhookData, frontendPayload, 'failed', responseText);
      }
    } catch (error) {
      console.error('❌ Error sending webhook to frontend:', error.message);
      
      // Log error
      await this._logWebhookForwarding(webhookData, null, 'error', error.message);
      
      // Don't throw error here to avoid breaking the main webhook flow
    }
  }

  // Helper method to determine webhook type
  _determineWebhookType(webhookData) {
    if (webhookData.payment_method) {
      return 'payment';
    } else if (webhookData.external_id && webhookData.external_id.includes('invoice')) {
      return 'invoice';
    } else if (webhookData.external_id && webhookData.external_id.includes('sub_')) {
      return 'subscription';
    } else {
      return 'payment'; // default
    }
  }

  // Helper method to log webhook forwarding attempts
  async _logWebhookForwarding(originalData, forwardedData, status, response) {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        status, // success, failed, error
        original_webhook: {
          id: originalData.id,
          external_id: originalData.external_id,
          status: originalData.status,
          amount: originalData.amount
        },
        forwarded_payload: forwardedData ? {
          id: forwardedData.id,
          type: forwardedData.type,
          status: forwardedData.status
        } : null,
        frontend_response: response,
        frontend_url: this.env.FRONTEND_URL || "http://localhost:3001"
      };

      const logText = `[${timestamp}] WEBHOOK_FORWARDING | Status: ${status} | External_ID: ${originalData.external_id || 'N/A'} | Payment_Status: ${originalData.status || 'N/A'} | Frontend_Response: ${response || 'N/A'}\n`;

      // Store forwarding log
      const date = timestamp.split('T')[0];
      const logFileName = `xendit-webhook-forwarding-${date}.log`;
      const existingLog = await this.env.USERS_KV.get(`webhook_forwarding_log:${logFileName}`) || '';
      const updatedLog = existingLog + logText;
      
      await this.env.USERS_KV.put(`webhook_forwarding_log:${logFileName}`, updatedLog);
      
      // Store latest forwarding log
      await this.env.USERS_KV.put('webhook_forwarding_latest', JSON.stringify(logEntry));

      console.log(`Webhook forwarding logged: ${status}`);
    } catch (error) {
      console.error('Error logging webhook forwarding:', error.message);
    }
  }

  // Helper method to store payment record
  async _storePaymentRecord(paymentData) {
    try {
      // Create a payment record
      const paymentRecord = {
        id: paymentData.id,
        external_id: paymentData.external_id,
        amount: paymentData.amount,
        status: paymentData.status,
        payer_email: paymentData.payer_email,
        payment_method: paymentData.payment_method,
        payment_channel: paymentData.payment_channel,
        payment_details: paymentData.payment_details,
        created_at: paymentData.created,
        paid_at: paymentData.paid_at,
        description: paymentData.description,
      };

      // Check if we already have a payment record with this external_id
      const existingPaymentKey = `payment:${paymentData.external_id}`;
      const existingPaymentData = await this.env.USERS_KV.get(
        existingPaymentKey
      );

      if (existingPaymentData) {
        // If we have existing data, we'll update it instead of creating a new record
        console.log(
          `Updating existing payment record for ${paymentData.external_id}`
        );
        const parsedPaymentData = JSON.parse(existingPaymentData);

        // Update the payment data with the new information
        if (parsedPaymentData.payment_data) {
          parsedPaymentData.payment_data.status = paymentData.status;
          parsedPaymentData.payment_data.payment_id = paymentData.id;
          parsedPaymentData.payment_data.paid_at = paymentData.paid_at;
          parsedPaymentData.payment_data.payment_method =
            paymentData.payment_method;
          parsedPaymentData.payment_data.payment_channel =
            paymentData.payment_channel;
          parsedPaymentData.payment_data.payment_details =
            paymentData.payment_details;

          // Update the stored payment data
          await this.env.USERS_KV.put(
            existingPaymentKey,
            JSON.stringify(parsedPaymentData)
          );
          return;
        }
      }

      // Store in KV
      const paymentKey = `payment:${paymentData.id}`;
      await this.env.USERS_KV.put(paymentKey, JSON.stringify(paymentRecord));

      // Also add to user's payment history
      if (paymentData.payer_email) {
        const userPaymentsKey = `user_payments:${paymentData.payer_email}`;
        const userPayments =
          (await this.env.USERS_KV.get(userPaymentsKey, "json")) || [];
        userPayments.push(paymentRecord);
        await this.env.USERS_KV.put(
          userPaymentsKey,
          JSON.stringify(userPayments)
        );
      }

      console.log(`Payment record stored for ${paymentData.id}`);
    } catch (error) {
      console.error("Error storing payment record:", error);
    }
  }

  async checkPaymentStatus(request) {
    try {
      const url = new URL(request.url);
      const invoiceId = url.searchParams.get("invoice_id");
      const externalId = url.searchParams.get("external_id");
      const referenceId = url.searchParams.get("reference_id");

      // Check if we have any identifier
      if (!invoiceId && !externalId && !referenceId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "invoice_id, external_id, or reference_id is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      let invoice;
      let searchMethod = "";

      // Try different methods to find the payment
      if (invoiceId) {
        // Method 1: Direct invoice lookup
        try {
          searchMethod = "direct_invoice_lookup";
          invoice = await this.xenditService.getInvoiceStatus(invoiceId);
        } catch (error) {
          console.log("Direct invoice lookup failed:", error.message);
        }
      }

      // Method 2: Search by external_id or reference_id in stored data
      if (!invoice && (externalId || referenceId)) {
        const searchId = externalId || referenceId;
        searchMethod = "stored_data_lookup";
        
        try {
          // Check stored payment data
          const paymentKey = `payment:${searchId}`;
          const storedPayment = await this.env.USERS_KV.get(paymentKey);
          
          if (storedPayment) {
            const paymentData = JSON.parse(storedPayment);
            
            // If we have payment_data with payment_id, try to get the actual invoice
            if (paymentData.payment_data && paymentData.payment_data.payment_id) {
              try {
                invoice = await this.xenditService.getInvoiceStatus(paymentData.payment_data.payment_id);
              } catch (error) {
                console.log("Failed to get invoice from stored payment_id:", error.message);
                
                // Return data from stored payment if we can't get fresh data
                return new Response(
                  JSON.stringify({
                    success: true,
                    data: {
                      invoice_id: paymentData.payment_data.payment_id || null,
                      external_id: searchId,
                      status: paymentData.payment_data.status || "UNKNOWN",
                      paid_at: paymentData.payment_data.paid_at || null,
                      paid_amount: paymentData.payment_data.amount || null,
                      is_paid: paymentData.payment_data.status === "PAID",
                      source: "stored_data"
                    },
                  }),
                  {
                    status: 200,
                    headers: { "Content-Type": "application/json" },
                  }
                );
              }
            }
          }

          // Check purchase data
          const purchaseData = await this.env.USERS_KV.get(`xendit_purchase:${searchId}`, "json");
          if (purchaseData) {
            return new Response(
              JSON.stringify({
                success: true,
                data: {
                  invoice_id: purchaseData.payment_id || null,
                  external_id: searchId,
                  status: purchaseData.status || "UNKNOWN",
                  paid_at: purchaseData.paid_at || purchaseData.completed_at || null,
                  paid_amount: purchaseData.amount || null,
                  is_paid: purchaseData.status === "COMPLETED",
                  source: "purchase_data"
                },
              }),
              {
                status: 200,
                headers: { "Content-Type": "application/json" },
              }
            );
          }

          // Check subscription reference data
          const subscriptionData = await this.env.USERS_KV.get(`xendit_ref:${searchId}`, "json");
          if (subscriptionData) {
            return new Response(
              JSON.stringify({
                success: true,
                data: {
                  invoice_id: null,
                  external_id: searchId,
                  status: "PENDING",
                  paid_at: null,
                  paid_amount: null,
                  is_paid: false,
                  source: "subscription_data",
                  subscription_info: subscriptionData
                },
              }),
              {
                status: 200,
                headers: { "Content-Type": "application/json" },
              }
            );
          }

        } catch (error) {
          console.error("Error searching stored data:", error);
        }
      }

      // Method 3: Search invoices by external_id using Xendit API
      if (!invoice && (externalId || referenceId)) {
        const searchId = externalId || referenceId;
        searchMethod = "xendit_api_search";
        
        try {
          // Get recent invoices and search for matching external_id
          const invoices = await this.xenditService.getInvoices({ limit: 100 });
          
          if (invoices && invoices.data) {
            const matchingInvoice = invoices.data.find(inv => inv.external_id === searchId);
            if (matchingInvoice) {
              invoice = matchingInvoice;
            }
          }
        } catch (error) {
          console.error("Error searching invoices by external_id:", error);
        }
      }

      if (!invoice) {
        return new Response(
          JSON.stringify({
            success: false,
            error: `Payment not found. Searched using: ${searchMethod}. Try using the actual invoice_id from Xendit instead of external_id/reference_id.`,
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const isPaid =
        (invoice.status === "PAID" || invoice.status === "SETTLED") &&
        invoice.paid_at &&
        invoice.paid_amount === invoice.amount;

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            invoice_id: invoice.id,
            external_id: invoice.external_id,
            status: invoice.status,
            paid_at: invoice.paid_at,
            paid_amount: invoice.paid_amount,
            is_paid: isPaid,
            search_method: searchMethod,
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error checking payment status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to check payment status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // Customer Management Methods
  async getCustomers(request) {
    try {
      const url = new URL(request.url);
      const limit = url.searchParams.get("limit") || 10;
      const afterId = url.searchParams.get("after_id");

      const customers = await this.xenditService.getCustomers({ limit, after_id: afterId });

      return new Response(
        JSON.stringify({
          success: true,
          data: customers,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting customers:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get customers",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getCustomer(request) {
    try {
      const url = new URL(request.url);
      const pathParts = url.pathname.split("/");
      const customerId = pathParts[pathParts.length - 1];

      if (!customerId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Customer ID is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const customer = await this.xenditService.getCustomer(customerId);

      return new Response(
        JSON.stringify({
          success: true,
          data: customer,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting customer:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get customer",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async updateCustomer(request) {
    try {
      const url = new URL(request.url);
      const pathParts = url.pathname.split("/");
      const customerId = pathParts[pathParts.length - 1];
      const data = await request.json();

      if (!customerId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Customer ID is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const customer = await this.xenditService.updateCustomer(customerId, data);

      return new Response(
        JSON.stringify({
          success: true,
          data: customer,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error updating customer:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to update customer",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // Invoice Management Methods
  async getInvoices(request) {
    try {
      const url = new URL(request.url);
      const limit = url.searchParams.get("limit") || 10;
      const afterId = url.searchParams.get("after_id");
      const status = url.searchParams.get("status");

      const invoices = await this.xenditService.getInvoices({ 
        limit, 
        after_id: afterId,
        statuses: status ? [status] : undefined
      });

      return new Response(
        JSON.stringify({
          success: true,
          data: invoices,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting invoices:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get invoices",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getInvoice(request) {
    try {
      const url = new URL(request.url);
      const pathParts = url.pathname.split("/");
      const invoiceId = pathParts[pathParts.length - 1];

      if (!invoiceId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invoice ID is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const invoice = await this.xenditService.getInvoiceStatus(invoiceId);

      return new Response(
        JSON.stringify({
          success: true,
          data: invoice,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting invoice:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get invoice",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async updateInvoice(request) {
    try {
      const url = new URL(request.url);
      const pathParts = url.pathname.split("/");
      const invoiceId = pathParts[pathParts.length - 1];
      const data = await request.json();

      if (!invoiceId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invoice ID is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const invoice = await this.xenditService.updateInvoice(invoiceId, data);

      return new Response(
        JSON.stringify({
          success: true,
          data: invoice,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error updating invoice:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to update invoice",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async expireInvoice(request) {
    try {
      const url = new URL(request.url);
      const pathParts = url.pathname.split("/");
      const invoiceId = pathParts[pathParts.length - 2]; // Because URL ends with /expire

      if (!invoiceId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invoice ID is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const invoice = await this.xenditService.expireInvoice(invoiceId);

      return new Response(
        JSON.stringify({
          success: true,
          data: invoice,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error expiring invoice:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to expire invoice",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // Balance & Account Methods
  async getBalance(request) {
    try {
      const balance = await this.xenditService.getBalance();

      return new Response(
        JSON.stringify({
          success: true,
          data: balance,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting balance:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get balance",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getAccount(request) {
    try {
      const account = await this.xenditService.getAccount();

      return new Response(
        JSON.stringify({
          success: true,
          data: account,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting account:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get account",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // Placeholder methods for other endpoints - to be implemented
  async getQRISPayments(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "QRIS list endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getQRISPayment(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "QRIS get endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getVirtualAccounts(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Virtual Account list endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getVirtualAccount(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Virtual Account get endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async updateVirtualAccount(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Virtual Account update endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getPaymentMethods(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Payment Methods list endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getPaymentMethod(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Payment Method get endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async createPaymentMethod(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Payment Method create endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getPaymentRequests(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Payment Requests list endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getPaymentRequest(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Payment Request get endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  async getTransactions(request) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Transactions list endpoint not yet implemented",
      }),
      {
        status: 501,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
