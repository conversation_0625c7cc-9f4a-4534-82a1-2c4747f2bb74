import { PayPalService } from "../services/paypalService.js";
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import { ResponseService } from "../services/responseService.js";

export class WebhookController {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierServiceD1(env);
    this.responseService = new ResponseService();
  }

  async handlePayPalWebhook(request) {
    try {
      // Verify PayPal webhook signature
      const paypalSignature = request.headers.get("paypal-transmission-sig");
      const paypalCertUrl = request.headers.get("paypal-cert-url");
      const paypalTransmissionId = request.headers.get(
        "paypal-transmission-id"
      );
      const paypalTransmissionTime = request.headers.get(
        "paypal-transmission-time"
      );

      if (
        !paypalSignature ||
        !paypalCertUrl ||
        !paypalTransmissionId ||
        !paypalTransmissionTime
      ) {
        throw new Error("Missing required PayPal webhook headers");
      }

      // Parse webhook data
      const webhookData = await request.json();
      console.log("Received PayPal webhook:", webhookData);

      const eventType = webhookData.event_type;
      const resourceId = webhookData.resource.id; // subscription ID

      switch (eventType) {
        case "BILLING.SUBSCRIPTION.ACTIVATED":
          await this._handleSubscriptionActivated(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.UPDATED":
          await this._handleSubscriptionUpdated(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.CANCELLED":
          await this._handleSubscriptionCancelled(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.EXPIRED":
          await this._handleSubscriptionExpired(
            resourceId,
            webhookData.resource
          );
          break;
        case "CHECKOUT.ORDER.APPROVED":
          await this._handleOrderApproved(
            resourceId,
            webhookData.resource
          );
          break;
        case "PAYMENT.CAPTURE.COMPLETED":
          await this._handlePaymentCaptureCompleted(
            resourceId,
            webhookData.resource
          );
          break;
        case "PAYMENT.SALE.COMPLETED":
          await this._handlePaymentSaleCompleted(
            resourceId,
            webhookData.resource
          );
          break;
        default:
          console.log("Unhandled webhook event type:", eventType);
      }

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Error handling PayPal webhook:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to process webhook",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async _handleSubscriptionActivated(subscriptionId, resource) {
    try {
      console.log("Handling subscription activated:", subscriptionId);

      // Get subscription details from KV
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }

      // Get user ID from subscription data
      const userId = subscriptionData.userId;
      if (!userId) {
        throw new Error("User ID not found in subscription data");
      }

      // Update user's tier
      await this.tierService.upgradeUserTier(userId, subscriptionData.tier, {
        subscription_id: subscriptionId,
        status: "active",
        start_date: new Date().toISOString(),
        expiration_date: resource.billing_info?.next_billing_time || null,
        addons: subscriptionData.addons,
      });

      console.log("Successfully activated subscription for user:", userId);
    } catch (error) {
      console.error("Error handling subscription activation:", error);
      throw error;
    }
  }

  async _handleSubscriptionUpdated(subscriptionId, resource) {
    try {
      console.log("Handling subscription updated:", subscriptionId);

      // Get subscription details from KV
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }

      const updatedData = {
        ...subscriptionData,
        status: resource.status,
        nextBillingTime: resource.billing_info?.next_billing_time || null,
        updatedAt: new Date().toISOString(),
      };

      // Update subscription status and next billing date in both storage keys
      await this.env.USERS_KV.put(
        `subscription:${subscriptionId}`,
        JSON.stringify(updatedData)
      );
      
      // Also update the user-based key
      if (subscriptionData.userId) {
        await this.env.USERS_KV.put(
          `subscription:${subscriptionData.userId}`,
          JSON.stringify(updatedData)
        );
      }

      console.log("Successfully updated subscription:", subscriptionId);
    } catch (error) {
      console.error("Error handling subscription update:", error);
      throw error;
    }
  }

  async _handleSubscriptionCancelled(subscriptionId, resource) {
    try {
      console.log("Handling subscription cancelled:", subscriptionId);

      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }

      // Reset user to starter tier
      await this.tierService.upgradeUserTier(subscriptionData.userId, "starter", {
        status: "cancelled",
        subscription_id: null,
        addons: { addon1: false, addon2: false },
      });

      // Clean up both subscription storage keys
      await this.env.USERS_KV.delete(`subscription:${subscriptionId}`);
      if (subscriptionData.userId) {
        await this.env.USERS_KV.delete(`subscription:${subscriptionData.userId}`);
      }

      console.log(
        "Successfully cancelled subscription for user:",
        subscriptionData.userId
      );
    } catch (error) {
      console.error("Error handling subscription cancellation:", error);
      throw error;
    }
  }

  async _handleSubscriptionExpired(subscriptionId, resource) {
    // Handle similar to cancellation
    await this._handleSubscriptionCancelled(subscriptionId, resource);
  }

  async _handleOrderApproved(orderId, resource) {
    try {
      console.log("Handling order approved:", orderId);
      
      // Get purchase data from KV
      const purchaseData = await this.env.USERS_KV.get(
        `purchase:${orderId}`,
        "json"
      );
      
      if (!purchaseData) {
        console.log("No purchase data found for order:", orderId);
        return; // Order might have been processed already via redirect
      }

      // Capture the payment automatically when order is approved
      try {
        await this.paypalService.capturePayment(orderId);
        console.log("Payment captured successfully for order:", orderId);
      } catch (error) {
        if (error.message.includes("ORDER_ALREADY_CAPTURED")) {
          console.log("Order already captured:", orderId);
        } else {
          console.error("Failed to capture payment:", error);
          throw error;
        }
      }

      console.log("Order approved webhook processed for:", orderId);
    } catch (error) {
      console.error("Error handling order approval:", error);
      throw error;
    }
  }

  async _handlePaymentCaptureCompleted(captureId, resource) {
    try {
      console.log("Handling payment capture completed:", captureId);
      
      // Extract order ID from the capture resource
      const orderId = resource.supplementary_data?.related_ids?.order_id;
      
      if (!orderId) {
        console.log("No order ID found in capture resource");
        return;
      }

      // Get purchase data from KV
      const purchaseData = await this.env.USERS_KV.get(
        `purchase:${orderId}`,
        "json"
      );
      
      if (!purchaseData) {
        console.log("No purchase data found for order:", orderId);
        return; // Order might have been processed already
      }

      // Update user's tier with lifetime access
      await this.tierService.upgradeEmailTier(
        purchaseData.email,
        purchaseData.tier,
        {
          purchaseId: orderId,
          captureId: captureId,
          addon1: purchaseData.addons?.addon1 || false,
          addon2: purchaseData.addons?.addon2 || false,
          isPermanent: true, // This indicates it's a lifetime purchase
          purchaseDate: new Date().toISOString(),
        }
      );

      // Clean up the temporary order data
      await this.env.USERS_KV.delete(`purchase:${orderId}`);

      console.log("Successfully processed payment capture for user:", purchaseData.email);
    } catch (error) {
      console.error("Error handling payment capture completion:", error);
      throw error;
    }
  }

  async _handlePaymentSaleCompleted(saleId, resource) {
    try {
      console.log("Handling payment sale completed:", saleId);
      
      // For subscription payments, the billing_agreement_id contains the subscription ID
      const subscriptionId = resource.billing_agreement_id;
      
      if (!subscriptionId) {
        console.log("No billing agreement ID found in sale resource");
        return;
      }

      console.log("Processing subscription payment for:", subscriptionId);
      
      // Get subscription data from KV
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      
      if (!subscriptionData) {
        console.log("No subscription data found for:", subscriptionId);
        return; // Subscription might not be in our system yet
      }

      // Update subscription with payment information
      const updatedData = {
        ...subscriptionData,
        lastPayment: {
          saleId: saleId,
          amount: resource.amount?.total,
          currency: resource.amount?.currency,
          paymentTime: resource.create_time,
          transactionFee: resource.transaction_fee?.value,
        },
        status: "active",
        updatedAt: new Date().toISOString(),
      };

      // Update both storage keys
      await this.env.USERS_KV.put(
        `subscription:${subscriptionId}`,
        JSON.stringify(updatedData)
      );
      
      if (subscriptionData.userId) {
        await this.env.USERS_KV.put(
          `subscription:${subscriptionData.userId}`,
          JSON.stringify(updatedData)
        );
      }

      console.log("Successfully processed subscription payment for:", subscriptionId);
    } catch (error) {
      console.error("Error handling payment sale completion:", error);
      throw error;
    }
  }

  async getWebhookStatus(request) {
    try {
      // Get PayPal access token to verify connection
      const accessToken = await this.paypalService.getAccessToken();

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            paypal: {
              connected: !!accessToken,
              sandbox: this.env.PAYPAL_SANDBOX === "true",
              webhookId: !!this.env.PAYPAL_WEBHOOK_ID,
            },
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get webhook status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getRecentEvents(request) {
    try {
      // For development/testing only
      const events =
        (await this.env.USERS_KV.get("webhook_events", "json")) || [];
      return new Response(
        JSON.stringify({
          success: true,
          data: events,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get recent events",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
