// src/controllers/debugController.js
export class DebugController {
  constructor(env) {
    this.env = env;
  }

  async listAllKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          try {
            // First try to get as JSON
            const value = await this.env.USERS_KV.get(key.name, "json");
            return {
              key: key.name,
              value,
            };
          } catch (e) {
            // If JSON parsing fails, get as text
            const textValue = await this.env.USERS_KV.get(key.name, "text");
            return {
              key: key.name,
              value: textValue,
              valueType: "text",
            };
          }
        })
      );

      // Group data by key prefix
      const groupedData = data.reduce((acc, item) => {
        const prefix = item.key.split(":")[0];
        if (!acc[prefix]) {
          acc[prefix] = [];
        }
        acc[prefix].push(item);
        return acc;
      }, {});

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            summary: {
              totalKeys: list.keys.length,
              prefixes: Object.keys(groupedData),
              countByPrefix: Object.fromEntries(
                Object.entries(groupedData).map(([k, v]) => [k, v.length])
              ),
            },
            grouped: groupedData,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getKVEntry(request) {
    try {
      const { key } = request.params;
      if (!key) {
        throw new Error("Key is required");
      }

      const value = await this.env.USERS_KV.get(key, "json");
      if (!value) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Key not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: value,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async deleteKVEntry(request) {
    try {
      const { key } = request.params;
      if (!key) {
        throw new Error("Key is required");
      }

      await this.env.USERS_KV.delete(key);
      return new Response(
        JSON.stringify({
          success: true,
          message: `Key ${key} deleted successfully`,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async searchKVEntries(request) {
    try {
      const url = new URL(request.url);
      const prefix = url.searchParams.get("prefix");
      const pattern = url.searchParams.get("pattern");

      if (!prefix && !pattern) {
        throw new Error("Either prefix or pattern is required");
      }

      const list = await this.env.USERS_KV.list({ prefix });
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value,
          };
        })
      );

      let filteredData = data;
      if (pattern) {
        const regex = new RegExp(pattern, "i");
        filteredData = data.filter(
          (item) =>
            regex.test(item.key) || regex.test(JSON.stringify(item.value))
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: filteredData,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getUserTierData(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this.env.USERS_KV.get(`api_key:${apiKey}`, "json");
      const tierData = await this.env.USERS_KV.get(
        `${TierService.KEYS.USER_TIER}:${user.id}`,
        "json"
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            user,
            tierData,
          },
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async truncateKVData(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const deletedKeys = [];
      const skippedKeys = [];

      for (const key of list.keys) {
        // Skip tier settings
        if (key.name.startsWith("t_setting:tiers")) {
          skippedKeys.push(key.name);
          continue;
        }

        await this.env.USERS_KV.delete(key.name);
        deletedKeys.push(key.name);
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            deletedCount: deletedKeys.length,
            skippedCount: skippedKeys.length,
            deletedKeys,
            skippedKeys,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async checkTruncateStatus(request) {
    try {
      const list = await this.env.USERS_KV.list();
      const data = await Promise.all(
        list.keys.map(async (key) => {
          const value = await this.env.USERS_KV.get(key.name, "json");
          return {
            key: key.name,
            value: key.name.startsWith("t_setting:tiers")
              ? "tier settings data..."
              : value,
          };
        })
      );

      const hasTierSettings = data.some(
        (item) => item.key === "t_setting:tiers"
      );
      const hasOtherData = data.some(
        (item) => !item.key.startsWith("t_setting:tiers")
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            isClean: !hasOtherData && hasTierSettings,
            totalKeys: list.keys.length,
            hasTierSettings,
            hasOtherData,
            keys: data.map((item) => item.key),
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async checkUserPassword(request) {
    try {
      const { email } = await request.json();

      if (!email) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Email is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // First get the user data by email
      const userData = await this.env.USERS_KV.get(`email:${email}`, "json");

      if (!userData) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "User not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get the user's credentials
      const credentials = await this.env.USERS_KV.get(
        `credentials:${userData.id}`,
        "json"
      );

      if (!credentials) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "User credentials not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          email: email,
          password: credentials.plainPassword,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
