// src/controllers/webhookTestController.js
export class WebhookTestController {
  constructor(env) {
    this.env = env;
  }

  async simulatePayPalWebhook(request) {
    try {
      const { eventType, subscriptionId } = await request.json();

      // Get the real subscription data
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:",
      });
      let subscriptionData = null;
      let userId = null;

      for (const key of keys) {
        const data = await this.env.USERS_KV.get(key.name, "json");
        if (data?.subscriptionId === subscriptionId) {
          subscriptionData = data;
          userId = key.name.split(":")[1];
          break;
        }
      }

      if (!subscriptionData || !userId) {
        throw new Error(`Subscription not found: ${subscriptionId}`);
      }

      // Get real user data
      const user = await this.env.USERS_KV.get(`user:${userId}`, "json");
      if (!user) {
        throw new Error(`User not found for subscription: ${subscriptionId}`);
      }

      // Generate event based on type
      let webhookEvent;
      if (eventType === "PAYMENT.SALE.COMPLETED") {
        webhookEvent = this.generatePaymentEvent(
          subscriptionId,
          subscriptionData,
          user
        );
      } else {
        webhookEvent = this.generateSubscriptionEvent(
          eventType,
          subscriptionId,
          subscriptionData,
          user
        );
      }

      // Generate real PayPal-style webhook headers
      const transmissionId = crypto.randomUUID();
      const webhookHeaders = {
        "paypal-auth-algo": "SHA256withRSA",
        "paypal-cert-url":
          "https://api.sandbox.paypal.com/v1/notifications/certs/CERT-360caa42-fca2a594-bc34f77b",
        "paypal-transmission-id": transmissionId,
        "paypal-transmission-sig": `mock_${transmissionId}`,
        "paypal-transmission-time": webhookEvent.create_time,
        "Content-Type": "application/json",
      };

      // Call webhook endpoint
      const response = await fetch(
        new URL("/api/webhooks/paypal", request.url),
        {
          method: "POST",
          headers: webhookHeaders,
          body: JSON.stringify(webhookEvent),
        }
      );

      const result = await response.json();

      return new Response(
        JSON.stringify({
          success: true,
          message: `PayPal ${eventType} webhook simulated`,
          details: {
            webhookId: webhookEvent.id,
            transmissionId: transmissionId,
            subscription: subscriptionId,
            user: user.email,
            timestamp: webhookEvent.create_time,
            amount: subscriptionData.price,
          },
          result,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Webhook simulation error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  generatePaymentEvent(subscriptionId, subscriptionData, user) {
    const now = new Date().toISOString();
    const paymentId = `PAY-${crypto.randomUUID()}`;

    return {
      id: `WH-${crypto.randomUUID()}`,
      create_time: now,
      resource_type: "sale",
      event_type: "PAYMENT.SALE.COMPLETED",
      summary: "Payment completed for subscription",
      resource: {
        id: paymentId,
        state: "completed",
        amount: {
          total: subscriptionData.price.toString(),
          currency: "USD",
          details: {
            subtotal: subscriptionData.price.toString(),
          },
        },
        payment_mode: "INSTANT_TRANSFER",
        protection_eligibility: "ELIGIBLE",
        protection_eligibility_type:
          "ITEM_NOT_RECEIVED_ELIGIBLE,UNAUTHORIZED_PAYMENT_ELIGIBLE",
        transaction_fee: {
          value: (subscriptionData.price * 0.029 + 0.3).toFixed(2),
          currency: "USD",
        },
        billing_agreement_id: subscriptionId,
        create_time: now,
        update_time: now,
        links: [
          {
            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}`,
            rel: "self",
            method: "GET",
          },
          {
            href: `https://api.sandbox.paypal.com/v1/payments/sale/${paymentId}/refund`,
            rel: "refund",
            method: "POST",
          },
        ],
      },
      links: [
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,
          rel: "self",
          method: "GET",
        },
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,
          rel: "resend",
          method: "POST",
        },
      ],
    };
  }

  generateSubscriptionEvent(eventType, subscriptionId, subscriptionData, user) {
    const now = new Date().toISOString();

    return {
      id: `WH-${crypto.randomUUID()}`,
      event_type: eventType,
      event_version: "1.0",
      create_time: now,
      resource_type: "subscription",
      resource_version: "2.0",
      summary: `Subscription ${eventType.split(".").pop().toLowerCase()}`,
      resource: {
        start_time: subscriptionData.createdAt,
        quantity: "1",
        subscriber: {
          name: {
            given_name: user.email.split("@")[0],
            surname: "",
          },
          email_address: user.email,
          payer_id: user.id,
        },
        status: "ACTIVE",
        status_update_time: now,
        id: subscriptionId,
        plan_id: subscriptionData.planId,
        billing_info: {
          outstanding_balance: {
            currency_code: "USD",
            value: "0.00",
          },
          cycle_executions: [
            {
              tenure_type: "REGULAR",
              sequence: 1,
              cycles_completed: 1,
              cycles_remaining: 0,
              current_pricing_scheme_version: 1,
            },
          ],
          last_payment: {
            amount: {
              currency_code: "USD",
              value: subscriptionData.price.toString(),
            },
            time: now,
          },
          next_billing_time: new Date(
            Date.now() + 30 * 24 * 60 * 60 * 1000
          ).toISOString(),
          failed_payments_count: 0,
        },
      },
      links: [
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}`,
          rel: "self",
          method: "GET",
        },
        {
          href: `https://api.sandbox.paypal.com/v1/notifications/webhooks-events/${crypto.randomUUID()}/resend`,
          rel: "resend",
          method: "POST",
        },
      ],
    };
  }
}
