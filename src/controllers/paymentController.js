export class PaymentController {
  constructor(env) {
    this.env = env;
  }

  async storePayment(request) {
    try {
      // Parse the request body
      const data = await request.json();
      const { payment_id, user_email, payment_data } = data;

      // Validate required fields
      if (!payment_id || !user_email || !payment_data) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "payment_id, user_email, and payment_data are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Validate payment_data fields
      const {
        external_id,
        amount,
        payer_email,
        description,
        currency,
        payment_methods,
        tier,
        status,
        created_at,
      } = payment_data;

      if (
        !external_id ||
        !amount ||
        !payer_email ||
        !description ||
        !currency ||
        !payment_methods ||
        !tier ||
        !status ||
        !created_at
      ) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "All payment_data fields are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Store payment data in existing KV store
      const paymentKey = `payment:${payment_id}`;
      await this.env.USERS_KV.put(paymentKey, JSON.stringify(data));

      // Create an index by user email for easier retrieval
      const userPaymentsKey = `user_payments:${user_email}`;
      let userPayments = [];

      try {
        const existingUserPayments = await this.env.USERS_KV.get(
          userPaymentsKey
        );
        if (existingUserPayments) {
          userPayments = JSON.parse(existingUserPayments);
        }
      } catch (error) {
        console.error("Error retrieving user payments:", error);
      }

      // Add the new payment ID to the user's payments list
      userPayments.push(payment_id);

      // Update the user's payments list
      await this.env.USERS_KV.put(
        userPaymentsKey,
        JSON.stringify(userPayments)
      );

      return new Response(
        JSON.stringify({
          success: true,
          message: "Payment data stored successfully",
          payment_id: payment_id,
        }),
        {
          status: 201,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error storing payment data:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to store payment data",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async listPayments(request) {
    try {
      // Get all keys with the payment: prefix
      const { keys } = await this.env.USERS_KV.list({ prefix: "payment:" });

      if (!keys || keys.length === 0) {
        return new Response(
          JSON.stringify({
            success: true,
            data: [],
            message: "No payment data found",
          }),
          {
            status: 200,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Fetch all payment data
      const payments = await Promise.all(
        keys.map(async (key) => {
          const paymentData = await this.env.USERS_KV.get(key.name);
          if (paymentData) {
            return JSON.parse(paymentData);
          }
          return null;
        })
      );

      // Filter out any null values
      const validPayments = payments.filter((payment) => payment !== null);

      return new Response(
        JSON.stringify({
          success: true,
          data: validPayments,
          count: validPayments.length,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error listing payments:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to list payments",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getPaymentStatus(request) {
    try {
      // Get the external_id from the URL query parameters
      const url = new URL(request.url);
      const external_id = url.searchParams.get("external_id");

      if (!external_id) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "external_id parameter is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get all keys with the payment: prefix
      const { keys } = await this.env.USERS_KV.list({ prefix: "payment:" });

      if (!keys || keys.length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "No payment data found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Fetch all payment data and find the one with matching external_id
      let matchingPayment = null;

      for (const key of keys) {
        const paymentDataStr = await this.env.USERS_KV.get(key.name);
        if (paymentDataStr) {
          const paymentData = JSON.parse(paymentDataStr);
          if (
            paymentData.payment_data &&
            paymentData.payment_data.external_id === external_id
          ) {
            matchingPayment = paymentData;
            break;
          }
        }
      }

      if (!matchingPayment) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Payment with the specified external_id not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      return new Response(JSON.stringify(matchingPayment), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Error getting payment status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message || "Failed to get payment status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
