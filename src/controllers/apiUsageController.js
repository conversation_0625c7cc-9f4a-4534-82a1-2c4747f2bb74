export class ApiUsageController {
  constructor(env) {
    this.env = env;
  }

  async trackUsage(request) {
    try {
      const headerApiKey = request.headers.get("x-sps-key");
      if (!headerApiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: "API Key is required in x-sps-key header",
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }

      const data = await request.json();
      const { apiKey, type, source, timestamp } = data;

      // Validate required fields
      if (!apiKey || !type || !source || !timestamp) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Missing required fields: apiKey, type, source, timestamp",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Validate type values
      const validTypes = ["images", "contents"];
      if (!validTypes.includes(type)) {
        return new Response(
          JSON.stringify({
            success: false,
            message: 'Invalid type. Must be either "images" or "contents"',
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Create a unique ID for this usage record
      const usageId = crypto.randomUUID();

      const usageData = {
        id: usageId,
        apiKey,
        type,
        source,
        timestamp,
        createdAt: new Date().toISOString(),
      };

      // Store in KV with prefix for easy querying
      await this.env.USERS_KV.put(
        `d_api_usage:${usageId}`,
        JSON.stringify(usageData)
      );

      // Also store by API key for quick user lookups
      await this.env.USERS_KV.put(
        `d_api_usage:${apiKey}:${usageId}`,
        JSON.stringify(usageData)
      );

      return new Response(
        JSON.stringify({
          success: true,
          message: "API usage tracked successfully",
          data: usageData,
        }),
        {
          status: 201,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getUserUsage(request) {
    try {
      const apiKey = request.headers.get("x-api-key");

      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API Key is required in x-sps-key header",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // List all usage records for this API key
      const { keys } = await this.env.USERS_KV.list({
        prefix: `d_api_usage:${apiKey}:`,
      });

      const usageData = await Promise.all(
        keys.map(async (key) => {
          const data = await this.env.USERS_KV.get(key.name, "json");
          return data;
        })
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: usageData,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
