// src/controllers/tierController.js
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import { ResponseService } from "../services/responseService";
import { ApiKeyService as ApiKeyServiceD1 } from "../services/apiKeyServiceD1.js";

export class TierController {
  constructor(env) {
    this.env = env;
    this.tierService = new TierServiceD1(env);
    this.responseService = new ResponseService();
    this.apiKeyService = new ApiKeyServiceD1(env);
  }

  async _getUserFromApiKey(apiKey) {
    if (!apiKey) {
      throw new Error("API Key is required");
    }

    // Use D1 to get user by API key
    const userId = await this.apiKeyService.db.getUserIdByApiKey(apiKey);
    if (!userId) {
      throw new Error("Invalid API Key");
    }

    const user = await this.apiKeyService.db.getUserWithDomains(userId);
    if (!user) {
      throw new Error("User not found");
    }

    return user;
  }

  async getTierSettings(request) {
    try {
      const settings = await this.tierService.getTierSettings();
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(settings)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getUserTierStatus(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const status = await this.tierService.getUserTierStatus(user.id, apiKey);

      return new Response(
        JSON.stringify(this.responseService.formatSuccess(status)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async upgradeTier(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "API key is required in x-sps-key header",
          }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const data = await request.json();
      const { tier, addon1, addon2 } = data;

      if (!tier) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Tier is required",
          }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      // Get user details from API key
      const apiKeyData = await this._getUserFromApiKey(apiKey);

      // Upgrade tier for the entire email
      const result = await this.tierService.upgradeEmailTier(
        apiKeyData.email,
        tier,
        {
          addon1,
          addon2,
        }
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            email: apiKeyData.email,
            tier: result.tier,
            previousTier: result.previousTier,
            updatedAt: result.updatedAt,
            quotas: result.currentStatus.quotas,
            addons: result.currentStatus.addons,
            features: result.currentStatus.features,
            price: result.currentStatus.price,
          },
          message: `Successfully upgraded from ${result.previousTier} to ${tier} tier`,
        }),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error upgrading tier:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Error upgrading tier",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async getQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const tierStatus = await this.tierService.getUserTierStatus(user.id);

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess({
            email: user.email,
            currentTier: tierStatus.currentTier,
            tierName: tierStatus.tierName,
            usage: tierStatus.usage,
            totalQuota: tierStatus.contentQuota, // Use contentQuota for totalQuota
            quotaPercentage: tierStatus.quotaPercentage,
            price: tierStatus.price,
            features: tierStatus.features,
            addons: tierStatus.addons,
          })
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async resetQuotaUsage(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const resetData = await this.tierService.resetQuotaUsage(user.id);

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            resetData,
            "Quota usage reset successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async setQuotaToZero(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      const user = await this._getUserFromApiKey(apiKey);
      const result = await this.tierService.setQuotaToZero(user.id);

      return new Response(
        JSON.stringify(this.responseService.formatSuccess(result)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async checkQuotaCron(request) {
    try {
      // Get all users first to get unique emails
      const userList = await this.env.USERS_KV.list({ prefix: "user:" });
      const processedEmails = new Set();
      const results = [];

      // Process each user
      for (const key of userList.keys) {
        const user = await this.env.USERS_KV.get(key.name, "json");
        if (user && user.email && !processedEmails.has(user.email)) {
          processedEmails.add(user.email);

          // Get email data
          const emailUser = await this.env.USERS_KV.get(
            `email:${user.email}`,
            "json"
          );
          const emailTier = await this.tierService.getEmailTier(user.email);
          const tierStatus = await this.tierService.getUserTierStatus(user.id);
          const domains = emailUser?.domains || [];

          // For paid tier users (basic/pro/enterprise)
          if (emailTier.tier !== TierService.TIERS.STARTER) {
            // Check if all quotas are zero
            if (
              tierStatus.remainingImagesQuota <= 0 &&
              tierStatus.remainingContentQuota <= 0
            ) {
              // Downgrade to starter tier and reset quota
              const result = await this.tierService.upgradeEmailTier(
                user.email,
                TierService.TIERS.STARTER
              );
              results.push({
                email: user.email,
                action: "downgraded",
                fromTier: emailTier.tier,
                toTier: TierService.TIERS.STARTER,
                domains: domains.map((d) => d.domain),
              });
            }
          }
          // For starter tier users
          else {
            const quotaUsage = await this.env.USERS_KV.get(
              `${TierService.KEYS.QUOTA_USAGE}:${user.email}:images`,
              "json"
            );

            if (!quotaUsage || !quotaUsage.resetDate) {
              // Initialize reset date if not set
              const resetData = {
                count: 0,
                lastReset: new Date().toISOString(),
                resetDate: new Date(
                  Date.now() + 30 * 24 * 60 * 60 * 1000
                ).toISOString(), // 30 days
              };

              await Promise.all([
                this.env.USERS_KV.put(
                  `${TierService.KEYS.QUOTA_USAGE}:${user.email}:images`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService.KEYS.QUOTA_USAGE}:${user.email}:content`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService.KEYS.QUOTA_USAGE}:${user.email}:title`,
                  JSON.stringify(resetData)
                ),
              ]);

              results.push({
                email: user.email,
                action: "initialized",
                nextResetDate: resetData.resetDate,
                domains: domains.map((d) => d.domain),
              });
            }
            // Check if it's time to reset quota
            else if (new Date() >= new Date(quotaUsage.resetDate)) {
              const resetData = {
                count: 0,
                lastReset: new Date().toISOString(),
                resetDate: new Date(
                  Date.now() + 30 * 24 * 60 * 60 * 1000
                ).toISOString(), // 30 days
              };

              await Promise.all([
                this.env.USERS_KV.put(
                  `${TierService.KEYS.QUOTA_USAGE}:${user.email}:images`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService.KEYS.QUOTA_USAGE}:${user.email}:content`,
                  JSON.stringify(resetData)
                ),
                this.env.USERS_KV.put(
                  `${TierService.KEYS.QUOTA_USAGE}:${user.email}:title`,
                  JSON.stringify(resetData)
                ),
              ]);

              results.push({
                email: user.email,
                action: "reset",
                nextResetDate: resetData.resetDate,
                domains: domains.map((d) => d.domain),
              });
            }
          }
        }
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            { processed: results.length, results },
            "Quota check completed"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error in quota check cron:", error);
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
