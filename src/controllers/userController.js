// src/controllers/userController.js
import { ResponseService } from "../services/responseService";
import { UserService as UserServiceD1 } from "../services/userServiceD1";
import { ApiKeyService as ApiKeyServiceD1 } from "../services/apiKeyServiceD1";
import { ValidationService } from "../services/validationService";
import { TokenService } from "../services/tokenService";

export class UserController {
  constructor(env) {
    this.env = env;
    this.userService = new UserServiceD1(env);
    this.apiKeyService = new ApiKeyServiceD1(env);
    this.responseService = new ResponseService();
    this.validationService = new ValidationService();
    this.tokenService = new TokenService(env);
  }

  async createUser(request) {
    try {
      // Parse the request body
      const userData = await request.json();
      console.log("Received user data:", userData); // Debug log

      // Validate input
      const validation = this.validationService.validateUserData(userData);
      if (!validation.isValid) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError(validation.errors.join(", "))
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }

      // Ensure domain and email are present
      if (!userData.domain || !userData.email) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Domain and email are required")
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }

      const user = await this.userService.createUser(userData);

      // Check if this is a domain already registered response
      if (user && user.success === false && user.registeredTo) {
        return new Response(
          JSON.stringify(this.responseService.formatError(user.message)),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            user,
            "User created successfully. Please check your email for credentials."
          )
        ),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("Error creating user:", error); // Debug log
      // Check if it's a domain registration error
      if (error.message.includes("Domain is already registered")) {
        return new Response(
          JSON.stringify({
            success: false,
            message: error.message,
            timestamp: new Date().toISOString(),
          }),
          {
            status: 200, // Keep 200 for domain registration error
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // For other errors, maintain the same structure but with 200 status
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 200, // Changed from 400/500 to 200
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async validateApiKey(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");

      if (!apiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: "API Key is required in x-sps-key header",
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }

      try {
        // Try to validate the API key
        await this.apiKeyService.validateApiKey(request);

        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: true,
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      } catch (error) {
        // If validation fails, return isValid: false
        return new Response(
          JSON.stringify(
            this.responseService.formatSuccess({
              isValid: false,
              message: error.message,
            })
          ),
          { headers: { "Content-Type": "application/json" } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify(
          this.responseService.formatError("Server error occurred")
        ),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async getUserDetail(request) {
    try {
      const apiKey = request.headers.get("x-sps-key");
      if (!apiKey) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("API Key is required")
          ),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }
      const user = await this.userService.getUserDetail(apiKey);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(user)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async activate(request) {
    try {
      const data = await request.json();
      const { license, domain } = data;

      console.log("🔧 Activation request received:", { license, domain });

      if (!license || !domain) {
        console.log("❌ Missing required fields:", { license: !!license, domain: !!domain });
        return new Response(
          JSON.stringify({
            status: false,
            message: "License key and domain are required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      console.log("🔍 Attempting to activate user with license:", license, "and domain:", domain);
      const activated = await this.userService.activateUser(license, domain);
      console.log("✅ Activation result:", activated);

      if (!activated) {
        console.log("❌ Activation failed for license:", license);
        return new Response(
          JSON.stringify({
            status: false,
            message: "Failed activate api key",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Get the user details from the service
      const apiKeyData = await this.env.USERS_KV.get(
        `api_key:${license}`,
        "json"
      );

      return new Response(
        JSON.stringify({
          status: true,
          message: "Success activate api key",
          domain: apiKeyData.domain,
          email: apiKeyData.email,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error activating user:", error);
      return new Response(
        JSON.stringify({
          status: false,
          message: "Failed activate api key",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async checkLicenseStatus(request) {
    try {
      const data = await request.json();
      const { license } = data;

      if (!license) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "License key is required",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const status = await this.userService.getLicenseStatus(license);
      return new Response(
        JSON.stringify({
          success: status.isValid,
          data: status,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error checking license status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to check license status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async login(request) {
    try {
      const userData = await request.json();

      // Validate required fields
      if (!userData.email || !userData.password) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Email and password are required")
          ),
          { status: 200, headers: { "Content-Type": "application/json" } }
        );
      }

      const user = await this.userService.loginUser(
        userData.email,
        userData.password
      );

      // Generate JWT token
      const token = this.tokenService.generateToken(user);

      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            { ...user, token },
            "Login successful"
          )
        ),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async getDomains(request) {
    try {
      const data = await request.json();
      const { email } = data;

      if (!email) {
        return new Response(
          JSON.stringify(this.responseService.formatError("Email is required")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      const domains = await this.userService.getDomains(email);
      return new Response(
        JSON.stringify(this.responseService.formatSuccess(domains)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  async removeDomain(request) {
    try {
      const data = await request.json();
      const { email, domain } = data;

      if (!email || !domain) {
        return new Response(
          JSON.stringify(
            this.responseService.formatError("Email and domain are required")
          ),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      await this.userService.removeDomain(email, domain);
      return new Response(
        JSON.stringify(
          this.responseService.formatSuccess(
            null,
            "Domain removed successfully"
          )
        ),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      return new Response(
        JSON.stringify(this.responseService.formatError(error.message)),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
  }

  // ...existing code...
  async verifyTokenEndpoint(request) {
    try {
      const authHeader = request.headers.get("Authorization");

      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "Authorization header with Bearer token is required",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      const token = authHeader.split(" ")[1];
      const decoded = await this.tokenService.verifyToken(token);

      // Get user details from KV store
      const user = await this.env.USERS_KV.get(`user:${decoded.id}`, "json");

      if (!user) {
        return new Response(
          JSON.stringify({
            success: false,
            message: "User not found",
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          valid: true,
          user: {
            email: user.email,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          valid: false,
          message: "Invalid token",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
