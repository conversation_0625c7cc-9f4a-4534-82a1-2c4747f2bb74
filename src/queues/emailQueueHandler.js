export default {
  async queue(batch, env) {
    // Process each message in the batch
    const messages = batch.messages;

    for (const message of messages) {
      try {
        const emailData = JSON.parse(message.body);

        // TODO: Implement your email sending logic here
        console.log("Processing email:", emailData);

        // Mark the message as successfully processed
        message.ack();
      } catch (error) {
        console.error("Error processing message:", error);
        // Mark the message as failed
        message.retry();
      }
    }
  },
};
