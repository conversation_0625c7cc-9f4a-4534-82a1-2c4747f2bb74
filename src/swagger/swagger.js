// src/swagger/swagger.js
export const swaggerDocument = {
  openapi: "3.0.0",
  info: {
    title: "API Documentation",
    version: "1.0.0",
  },
  tags: [
    {
      name: "Portal",
      description: "Portal user management endpoints",
    },
    {
      name: "Users",
      description: "User management and authentication endpoints",
    },
    {
      name: "Tiers",
      description: "Subscription tier management endpoints",
    },
    {
      name: "Usage",
      description: "API usage tracking and analytics endpoints",
    },
    {
      name: "Subscriptions",
      description: "Legacy subscription endpoints (deprecated)",
    },
    {
      name: "PayPal",
      description: "PayPal payment integration endpoints",
    },
    {
      name: "Xendit",
      description: "Xendit payment integration endpoints",
    },
    {
      name: "Debug",
      description: "Debug and development endpoints",
    },
  ],
  paths: {
    // PayPal Payment Integration
    "/api/paypal/subscriptions": {
      post: {
        summary: "Create PayPal Subscription",
        description:
          "Creates a new PayPal subscription with billing agreement. Email is extracted from the bearer token.",
        tags: ["PayPal"],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  tier: {
                    type: "string",
                    example: "pro",
                    description: "Subscription tier",
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: { type: "boolean", example: false },
                      addon2: { type: "boolean", example: true },
                    },
                  },
                },
                required: ["tier"],
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "sub-123456789",
                        },
                        approvalUrl: {
                          type: "string",
                          example: "https://paypal.com/approve/...",
                        },
                        tier: { type: "string", example: "pro" },
                        basePrice: { type: "number", example: 40 },
                        totalPrice: { type: "number", example: 50 },
                        status: { type: "string", example: "pending" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/paypal/purchases": {
      post: {
        summary: "Create PayPal One-Time Purchase",
        description:
          "Creates a PayPal one-time purchase order. Email is extracted from the bearer token.",
        tags: ["PayPal"],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  tier: { type: "string", example: "pro" },
                  amount: { type: "number", example: 99.99 },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: { type: "boolean", example: false },
                      addon2: { type: "boolean", example: true },
                    },
                  },
                },
                required: ["tier", "amount"],
              },
            },
          },
        },
        responses: {
          200: {
            description: "Purchase order created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        orderId: { type: "string", example: "order-123456789" },
                        approvalUrl: {
                          type: "string",
                          example: "https://paypal.com/approve/...",
                        },
                        tier: { type: "string", example: "pro" },
                        totalPrice: { type: "number", example: 109.99 },
                        status: { type: "string", example: "pending" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/paypal/purchases/status": {
      get: {
        summary: "Get PayPal Order Status",
        description:
          "Retrieves the current status of a PayPal order by order ID",
        tags: ["PayPal"],
        parameters: [
          {
            name: "orderId",
            in: "query",
            required: true,
            description: "PayPal order ID to check status for",
            schema: {
              type: "string",
              example: "0G662565F7463783F",
            },
          },
        ],
        responses: {
          200: {
            description: "Order status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        orderId: {
                          type: "string",
                          example: "0G662565F7463783F",
                        },
                        status: {
                          type: "string",
                          example: "APPROVED",
                          description:
                            "PayPal order status (CREATED, APPROVED, VOIDED, COMPLETED, etc.)",
                        },
                        intent: { type: "string", example: "CAPTURE" },
                        createdTime: {
                          type: "string",
                          example: "2024-01-15T10:30:00Z",
                        },
                        updateTime: {
                          type: "string",
                          example: "2024-01-15T10:35:00Z",
                        },
                        purchaseUnits: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              amount: {
                                type: "object",
                                properties: {
                                  currency_code: {
                                    type: "string",
                                    example: "USD",
                                  },
                                  value: { type: "string", example: "10.00" },
                                },
                              },
                              description: {
                                type: "string",
                                example: "Lifetime access to pro tier",
                              },
                            },
                          },
                        },
                        payer: {
                          type: "object",
                          properties: {
                            email_address: {
                              type: "string",
                              example: "<EMAIL>",
                            },
                            payer_id: { type: "string", example: "PAYERID123" },
                          },
                        },
                        links: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              href: {
                                type: "string",
                                example:
                                  "https://api.paypal.com/v2/checkout/orders/0G662565F7463783F",
                              },
                              rel: { type: "string", example: "self" },
                              method: { type: "string", example: "GET" },
                            },
                          },
                        },
                        localData: {
                          type: "object",
                          nullable: true,
                          properties: {
                            email: {
                              type: "string",
                              example: "<EMAIL>",
                            },
                            tier: { type: "string", example: "pro" },
                            addons: {
                              type: "object",
                              properties: {
                                addon1: { type: "boolean", example: false },
                                addon2: { type: "boolean", example: true },
                              },
                            },
                            amount: { type: "number", example: 10 },
                            createdAt: {
                              type: "string",
                              example: "2024-01-15T10:30:00.000Z",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request - Order ID is required",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "Order ID is required",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "Failed to get order status",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/paypal/webhooks": {
      post: {
        summary: "PayPal Webhook Handler",
        description: "Receives and processes PayPal webhook notifications",
        tags: ["PayPal"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                description: "PayPal webhook payload",
              },
            },
          },
        },
        responses: {
          200: {
            description: "Webhook processed successfully",
          },
        },
      },
    },
    "/api/paypal/purchases/success": {
      get: {
        summary: "PayPal Purchase Success Callback",
        description: "Handles the success callback from PayPal after a purchase is approved and redirects to the frontend dashboard",
        tags: ["PayPal"],
        parameters: [
          {
            name: "token",
            in: "query",
            required: true,
            description: "PayPal order token",
            schema: {
              type: "string",
              example: "5O190127TN364715T",
            },
          },
        ],
        responses: {
          302: {
            description: "Redirect to frontend dashboard with success status",
          },
        },
      },
    },
    "/api/paypal/subscriptions/success": {
      get: {
        summary: "PayPal Subscription Success Callback",
        description: "Handles the success callback from PayPal after subscription approval and upgrades user tier",
        tags: ["PayPal"],
        parameters: [
          {
            name: "ba_token",
            in: "query",
            required: true,
            description: "PayPal billing agreement token",
            schema: {
              type: "string",
              example: "B-5RT15012PY123456",
            },
          },
        ],
        responses: {
          302: {
            description: "Redirect to frontend dashboard with subscription status",
          },
        },
      },
    },
    "/api/paypal/subscriptions/cancel": {
      get: {
        summary: "PayPal Subscription Cancel Callback",
        description: "Handles the cancel callback from PayPal when user cancels subscription",
        tags: ["PayPal"],
        responses: {
          302: {
            description: "Redirect to frontend dashboard with cancelled status",
          },
        },
      },
    },
    "/api/paypal/subscriptions/{subscriptionId}/status": {
      get: {
        summary: "Get PayPal Subscription Status",
        description: "Retrieves the current status and details of a PayPal subscription",
        tags: ["PayPal"],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: "subscriptionId",
            in: "path",
            required: true,
            description: "PayPal subscription identifier",
            schema: {
              type: "string",
              example: "I-BW452GLLEP1G",
            },
          },
        ],
        responses: {
          200: {
            description: "Subscription status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: { type: "string", example: "I-BW452GLLEP1G" },
                        status: { type: "string", example: "ACTIVE" },
                        tier: { type: "string", example: "pro" },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: { type: "boolean", example: false },
                            addon2: { type: "boolean", example: true },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/paypal/subscriptions/{subscriptionId}/addons": {
      patch: {
        summary: "Update PayPal Subscription Addons",
        description: "Updates the addons for an existing PayPal subscription",
        tags: ["PayPal"],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: "subscriptionId",
            in: "path",
            required: true,
            description: "PayPal subscription identifier",
            schema: {
              type: "string",
              example: "I-BW452GLLEP1G",
            },
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  addons: {
                    type: "object",
                    properties: {
                      addon1: { type: "boolean", example: false },
                      addon2: { type: "boolean", example: true },
                    },
                  },
                },
                required: ["addons"],
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription addons updated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: { type: "string", example: "I-BW452GLLEP1G" },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: { type: "boolean", example: false },
                            addon2: { type: "boolean", example: true },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    // Xendit Payment Integration
    "/api/xendit/subscriptions": {
      post: {
        summary: "Create Xendit Subscription with Payment Method Pre-selection",
        description:
          "Creates a new Xendit subscription with invoice. Email is extracted from the bearer token. Add payment_methods array to pre-select specific payment method.",
        tags: ["Xendit"],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              examples: {
                "All Payment Methods": {
                  summary: "All Payment Methods (Default)",
                  description: "Show all available payment methods",
                  value: {
                    tier: "basic",
                    addons: {
                      addon1: true,
                      addon2: false,
                    },
                  },
                },
                "GoPay Only": {
                  summary: "GoPay Pre-selection",
                  description: "Direct to GoPay checkout page",
                  value: {
                    tier: "basic",
                    addons: {
                      addon1: true,
                      addon2: false,
                    },
                    payment_methods: ["GOPAY"],
                  },
                },
                "DANA Only": {
                  summary: "DANA Pre-selection",
                  description: "Direct to DANA checkout page",
                  value: {
                    tier: "pro",
                    payment_methods: ["DANA"],
                  },
                },
                "QRIS Only": {
                  summary: "QRIS Pre-selection",
                  description: "Direct to QRIS scanner",
                  value: {
                    tier: "enterprise",
                    payment_methods: ["QRIS"],
                  },
                },
                "OVO Only": {
                  summary: "OVO Pre-selection",
                  description: "Direct to OVO checkout page",
                  value: {
                    tier: "basic",
                    payment_methods: ["OVO"],
                  },
                },
                "BCA Virtual Account": {
                  summary: "BCA VA Pre-selection",
                  description: "Direct to BCA Virtual Account payment",
                  value: {
                    tier: "pro",
                    addons: {
                      addon1: false,
                      addon2: true,
                    },
                    payment_methods: ["BANK_TRANSFER"],
                    available_banks: ["BCA"],
                  },
                },
                "BNI Virtual Account": {
                  summary: "BNI VA Pre-selection",
                  description: "Direct to BNI Virtual Account payment",
                  value: {
                    tier: "basic",
                    payment_methods: ["BANK_TRANSFER"],
                    available_banks: ["BNI"],
                  },
                },
                "Mandiri Virtual Account": {
                  summary: "Mandiri VA Pre-selection",
                  description: "Direct to Mandiri Virtual Account payment",
                  value: {
                    tier: "enterprise",
                    payment_methods: ["BANK_TRANSFER"],
                    available_banks: ["MANDIRI"],
                  },
                },
                "Multiple Banks VA": {
                  summary: "Multiple Banks VA",
                  description:
                    "Show BCA, BNI, and Mandiri Virtual Account options",
                  value: {
                    tier: "pro",
                    payment_methods: ["BANK_TRANSFER"],
                    available_banks: ["BCA", "BNI", "MANDIRI"],
                  },
                },
              },
              schema: {
                type: "object",
                properties: {
                  tier: {
                    type: "string",
                    example: "basic",
                    description: "Subscription tier",
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: { type: "boolean", example: true },
                      addon2: { type: "boolean", example: false },
                    },
                  },
                  payment_methods: {
                    type: "array",
                    description:
                      "Optional: Pre-select specific payment methods. Use ['GOPAY'] for GoPay, ['DANA'] for DANA, ['QRIS'] for QRIS, ['BANK_TRANSFER'] for Virtual Account, etc.",
                    items: {
                      type: "string",
                      enum: [
                        "GOPAY",
                        "DANA",
                        "OVO",
                        "QRIS",
                        "EWALLET",
                        "BANK_TRANSFER",
                      ],
                    },
                    example: ["GOPAY"],
                  },
                  available_banks: {
                    type: "array",
                    description:
                      "Optional: When using BANK_TRANSFER, specify which banks to show. Use with payment_methods: ['BANK_TRANSFER']",
                    items: {
                      type: "string",
                      enum: ["BCA", "BNI", "BRI", "MANDIRI", "PERMATA", "BSI"],
                    },
                    example: ["BCA"],
                  },
                },
                required: ["tier"],
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        invoiceUrl: {
                          type: "string",
                          example: "https://checkout.xendit.co/...",
                        },
                        referenceId: {
                          type: "string",
                          example: "sub_123456789",
                        },
                        amount: { type: "number", example: 150000 },
                        status: { type: "string", example: "pending" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/webhooks": {
      post: {
        summary: "Xendit Webhook Handler with Frontend Forwarding",
        description:
          "Receives and processes Xendit webhook notifications. After successful payment processing, automatically forwards webhook data to frontend at http://localhost:3001/api/xendit/webhook-listener for real-time updates. Supports both subscription and purchase payments with automatic tier upgrades.",
        tags: ["Xendit"],
        parameters: [
          {
            name: "x-callback-token",
            in: "header",
            required: true,
            description:
              "Xendit webhook token for verifying the request authenticity",
            schema: {
              type: "string",
              example: "your-webhook-token",
            },
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: {
                    type: "string",
                    example: "67d53ae210b21173ca67d492",
                    description: "Xendit payment ID",
                  },
                  external_id: {
                    type: "string",
                    example: "sub_1752923558975_ipqcjp95y",
                    description:
                      "Reference ID from subscription/purchase creation",
                  },
                  status: {
                    type: "string",
                    example: "PAID",
                    enum: ["PENDING", "PAID", "EXPIRED", "FAILED"],
                    description: "Payment status",
                  },
                  amount: {
                    type: "number",
                    example: 374850,
                    description: "Payment amount in IDR (converted from USD)",
                  },
                  payer_email: {
                    type: "string",
                    format: "email",
                    example: "<EMAIL>",
                    description: "Customer email address",
                  },
                  paid_at: {
                    type: "string",
                    format: "date-time",
                    example: "2025-01-15T10:30:00Z",
                    description: "Payment completion timestamp",
                  },
                  payment_method: {
                    type: "string",
                    example: "QR_CODE",
                    description: "Payment method used",
                  },
                  payment_channel: {
                    type: "string",
                    example: "QRIS",
                    description: "Specific payment channel",
                  },
                  payment_details: {
                    type: "object",
                    properties: {
                      source: {
                        type: "string",
                        example: "DANA",
                        description: "Payment source (e.g., DANA, GoPay, OVO)",
                      },
                      receipt_id: {
                        type: "string",
                        example: "TXN123456789",
                        description: "Receipt ID from payment provider",
                      },
                    },
                  },
                  description: {
                    type: "string",
                    example: "Subscription for basic tier",
                    description: "Payment description",
                  },
                },
                required: [
                  "id",
                  "external_id",
                  "status",
                  "amount",
                  "payer_email",
                ],
              },
              examples: {
                "Successful Payment": {
                  summary: "Payment Completed Successfully",
                  description: "Example of successful payment webhook payload",
                  value: {
                    id: "67d53ae210b21173ca67d492",
                    external_id: "sub_1752923558975_ipqcjp95y",
                    status: "PAID",
                    amount: 374850,
                    payer_email: "<EMAIL>",
                    paid_at: "2025-01-15T10:30:00Z",
                    payment_method: "QR_CODE",
                    payment_channel: "QRIS",
                    payment_details: {
                      source: "DANA",
                      receipt_id: "TXN123456789",
                    },
                    description: "Subscription for basic tier",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Webhook processed successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    type: {
                      type: "string",
                      example: "subscription",
                      enum: ["subscription", "purchase"],
                      description: "Type of payment processed",
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Invalid webhook token",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Invalid webhook token",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Webhook processing failed",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to process webhook",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/webhook-logs": {
      get: {
        summary: "Get Xendit Webhook Logs",
        description:
          "Retrieve webhook logs for debugging purposes. Use ?latest=true to get the most recent logs, or no parameter to list all available logs.",
        tags: ["Xendit", "Debug"],
        parameters: [
          {
            name: "latest",
            in: "query",
            required: false,
            description: "Set to 'true' to get the latest webhook logs only",
            schema: {
              type: "string",
              enum: ["true", "false"],
              example: "true",
            },
          },
        ],
        responses: {
          200: {
            description: "Webhook logs retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      oneOf: [
                        {
                          type: "object",
                          description:
                            "Latest logs response (when ?latest=true)",
                          properties: {
                            latest_router_log: {
                              type: "string",
                              description: "Latest webhook log from router",
                              example:
                                "=== XENDIT WEBHOOK LOG ===\\nTimestamp: 2025-01-15T10:30:00.000Z\\nExternal ID: sub_123\\nStatus: PAID\\n...",
                            },
                            latest_controller_log: {
                              type: "string",
                              description: "Latest webhook log from controller",
                              example:
                                "=== XENDIT WEBHOOK LOG (Controller) ===\\nTimestamp: 2025-01-15T10:30:00.000Z\\n...",
                            },
                          },
                        },
                        {
                          type: "object",
                          description: "All logs list response (default)",
                          properties: {
                            router_logs: {
                              type: "array",
                              items: {
                                type: "string",
                              },
                              description: "List of router webhook log keys",
                              example: [
                                "xendit_webhook_log:2025-01-15T10:30:00.000Z",
                                "xendit_webhook_log:2025-01-15T10:31:00.000Z",
                              ],
                            },
                            controller_logs: {
                              type: "array",
                              items: {
                                type: "string",
                              },
                              description:
                                "List of controller webhook log keys",
                              example: [
                                "xendit_webhook_controller_log:2025-01-15T10:30:00.000Z",
                              ],
                            },
                            total_router_logs: {
                              type: "number",
                              description: "Total number of router logs",
                              example: 2,
                            },
                            total_controller_logs: {
                              type: "number",
                              description: "Total number of controller logs",
                              example: 1,
                            },
                          },
                        },
                      ],
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Error retrieving logs",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to retrieve logs",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/webhook-logs/{logKey}": {
      get: {
        summary: "Get Specific Webhook Log",
        description:
          "Retrieve a specific webhook log by its key. Returns the raw log content as plain text.",
        tags: ["Xendit", "Debug"],
        parameters: [
          {
            name: "logKey",
            in: "path",
            required: true,
            description:
              "The log key to retrieve (e.g., xendit_webhook_log:2025-01-15T10:30:00.000Z)",
            schema: {
              type: "string",
              example: "xendit_webhook_log:2025-01-15T10:30:00.000Z",
            },
          },
        ],
        responses: {
          200: {
            description: "Log content retrieved successfully",
            content: {
              "text/plain": {
                schema: {
                  type: "string",
                  example:
                    '=== XENDIT WEBHOOK LOG ===\\nTimestamp: 2025-01-15T10:30:00.000Z\\nExternal ID: sub_1752923558975_ipqcjp95y\\nStatus: PAID\\nAmount: 374850\\nPayer Email: <EMAIL>\\nPayment Method: QR_CODE\\nPayment Channel: QRIS\\nPayment ID: 67d53ae210b21173ca67d492\\n\\nFull Payload:\\n{\\n  \\"id\\": \\"67d53ae210b21173ca67d492\\",\\n  \\"external_id\\": \\"sub_1752923558975_ipqcjp95y\\",\\n  \\"status\\": \\"PAID\\",\\n  \\"amount\\": 374850,\\n  \\"payer_email\\": \\"<EMAIL>\\",\\n  \\"paid_at\\": \\"2025-01-15T10:30:00Z\\",\\n  \\"payment_method\\": \\"QR_CODE\\",\\n  \\"payment_channel\\": \\"QRIS\\"\\n}\\n\\nHeaders:\\n{\\n  \\"user-agent\\": \\"Xendit-Webhook/1.0\\",\\n  \\"x-callback-token\\": \\"PRESENT\\",\\n  \\"content-type\\": \\"application/json\\"\\n}\\n\\n========================\\n',
                },
              },
            },
          },
          404: {
            description: "Log not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Log not found",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Error retrieving log",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to retrieve log",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/webhook-listener": {
      post: {
        summary: "Frontend Webhook Listener (Frontend Implementation Required)",
        description:
          "This endpoint should be implemented in your frontend application (port 3001) to receive webhook notifications from the backend after successful payment processing. The backend automatically forwards webhook data here for real-time UI updates.",
        tags: ["Frontend Integration"],
        servers: [
          {
            url: "http://localhost:3001",
            description: "Frontend server (port 3001)",
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: {
                    type: "string",
                    example: "67d53ae210b21173ca67d492",
                    description: "Xendit payment ID",
                  },
                  external_id: {
                    type: "string",
                    example: "sub_1752923558975_ipqcjp95y",
                    description:
                      "Reference ID from subscription/purchase creation",
                  },
                  status: {
                    type: "string",
                    example: "PAID",
                    description: "Payment status",
                  },
                  amount: {
                    type: "number",
                    example: 374850,
                    description: "Payment amount in IDR",
                  },
                  payer_email: {
                    type: "string",
                    format: "email",
                    example: "<EMAIL>",
                    description: "Customer email address",
                  },
                  paid_at: {
                    type: "string",
                    format: "date-time",
                    example: "2025-01-15T10:30:00Z",
                    description: "Payment completion timestamp",
                  },
                  payment_method: {
                    type: "string",
                    example: "QR_CODE",
                    description: "Payment method used",
                  },
                  payment_channel: {
                    type: "string",
                    example: "QRIS",
                    description: "Specific payment channel",
                  },
                  payment_details: {
                    type: "object",
                    properties: {
                      source: {
                        type: "string",
                        example: "DANA",
                        description: "Payment source",
                      },
                      receipt_id: {
                        type: "string",
                        example: "TXN123456789",
                        description: "Receipt ID",
                      },
                    },
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    example: "2025-01-15T10:30:05Z",
                    description: "Backend processing timestamp",
                  },
                  source: {
                    type: "string",
                    example: "backend-webhook-handler",
                    description: "Source of the notification",
                  },
                },
                required: [
                  "id",
                  "external_id",
                  "status",
                  "amount",
                  "payer_email",
                  "timestamp",
                  "source",
                ],
              },
              examples: {
                "Payment Notification": {
                  summary: "Payment Completed Notification",
                  description:
                    "Notification sent from backend to frontend after successful payment",
                  value: {
                    id: "67d53ae210b21173ca67d492",
                    external_id: "sub_1752923558975_ipqcjp95y",
                    status: "PAID",
                    amount: 374850,
                    payer_email: "<EMAIL>",
                    paid_at: "2025-01-15T10:30:00Z",
                    payment_method: "QR_CODE",
                    payment_channel: "QRIS",
                    payment_details: {
                      source: "DANA",
                      receipt_id: "TXN123456789",
                    },
                    timestamp: "2025-01-15T10:30:05Z",
                    source: "backend-webhook-handler",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Notification received successfully by frontend",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    message: {
                      type: "string",
                      example: "Payment notification received",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/dashboard-by-email": {
      get: {
        summary: "Get Dashboard Data by Email",
        description:
          "<b>Mengambil semua data dashboard berdasarkan email</b>. <br><br>" +
          "Endpoint ini mengumpulkan semua data yang terkait dengan email tertentu dari berbagai sumber data dan mengirimkannya ke frontend untuk diproses lebih lanjut. <br><br>" +
          "Data yang dikumpulkan meliputi: Portal User, Regular User, Domains, API Keys, Subscriptions, Payments, Tier Info, Usage History, Daily Usage, Webhook Events, dan Email Queue. <br><br>" +
          "<b>Catatan:</b> Endpoint ini tidak memerlukan autentikasi token.",
        tags: ["Portal"],
        parameters: [
          {
            name: "email",
            in: "query",
            required: true,
            description: "Email address untuk mengambil semua data terkait",
            schema: {
              type: "string",
              format: "email",
              example: "<EMAIL>",
            },
          },
        ],
        responses: {
          200: {
            description: "Dashboard data berhasil diambil",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    message: {
                      type: "string",
                      example: "Dashboard data retrieved successfully",
                    },
                    data: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          format: "email",
                          example: "<EMAIL>",
                        },
                        portalUser: {
                          type: "object",
                          nullable: true,
                          properties: {
                            id: {
                              type: "string",
                              example: "portal_123456789_abc123",
                            },
                            email: {
                              type: "string",
                              example: "<EMAIL>",
                            },
                            type: { type: "string", example: "portal" },
                            createdAt: { type: "string", format: "date-time" },
                            isActive: { type: "number", example: 1 },
                            profile: {
                              type: "object",
                              properties: {
                                username: { type: "string", example: "user" },
                                displayName: {
                                  type: "string",
                                  example: "user",
                                },
                              },
                            },
                          },
                        },
                        regularUser: {
                          type: "object",
                          nullable: true,
                          properties: {
                            id: { type: "string", example: "user-uuid" },
                            email: {
                              type: "string",
                              example: "<EMAIL>",
                            },
                            createdAt: { type: "string", format: "date-time" },
                            updatedAt: { type: "string", format: "date-time" },
                          },
                        },
                        domains: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              domain: {
                                type: "string",
                                example: "example.com",
                              },
                              api_key: {
                                type: "string",
                                example: "api-key-uuid",
                              },
                              status: { type: "string", example: "active" },
                              createdAt: {
                                type: "string",
                                format: "date-time",
                              },
                              activatedAt: {
                                type: "string",
                                format: "date-time",
                              },
                              userId: { type: "string", example: "user-uuid" },
                            },
                          },
                        },
                        apiKeys: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              apiKey: {
                                type: "string",
                                example: "api-key-uuid",
                              },
                              domain: {
                                type: "string",
                                example: "example.com",
                              },
                              status: { type: "string", example: "active" },
                              createdAt: {
                                type: "string",
                                format: "date-time",
                              },
                              activatedAt: {
                                type: "string",
                                format: "date-time",
                              },
                            },
                          },
                        },
                        subscriptions: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              subscriptionId: {
                                type: "string",
                                example: "I-XXXXX",
                              },
                              planId: { type: "string", example: "P-XXXXX" },
                              userId: { type: "string", example: "user-uuid" },
                              tier: { type: "string", example: "medium" },
                              status: { type: "string", example: "ACTIVE" },
                              email: {
                                type: "string",
                                example: "<EMAIL>",
                              },
                              created_at: {
                                type: "string",
                                format: "date-time",
                              },
                            },
                          },
                        },
                        payments: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              id: { type: "string", example: "PAY-XXXXX" },
                              amount: {
                                type: "object",
                                properties: {
                                  currency_code: {
                                    type: "string",
                                    example: "USD",
                                  },
                                  value: { type: "string", example: "9.99" },
                                },
                              },
                              time: { type: "string", format: "date-time" },
                            },
                          },
                        },
                        tierInfo: {
                          type: "object",
                          nullable: true,
                          properties: {
                            tier: { type: "string", example: "medium" },
                            email: {
                              type: "string",
                              example: "<EMAIL>",
                            },
                            userId: { type: "string", example: "user-uuid" },
                            createdAt: { type: "string", format: "date-time" },
                            additionalQuotas: {
                              type: "object",
                              properties: {
                                images: { type: "number", example: 0 },
                                content: { type: "number", example: 0 },
                                title: { type: "number", example: 0 },
                              },
                            },
                          },
                        },
                        usageHistory: {
                          type: "object",
                          properties: {
                            entries: {
                              type: "array",
                              items: {
                                type: "object",
                                properties: {
                                  id: {
                                    type: "string",
                                    example: "history-uuid",
                                  },
                                  userId: {
                                    type: "string",
                                    example: "user-uuid",
                                  },
                                  type: { type: "string", example: "images" },
                                  source: { type: "string", example: "api" },
                                  timestamp: {
                                    type: "string",
                                    format: "date-time",
                                  },
                                  domain: {
                                    type: "string",
                                    example: "example.com",
                                  },
                                },
                              },
                            },
                            pagination: {
                              type: "object",
                              properties: {
                                total: { type: "number", example: 1 },
                                totalPages: { type: "number", example: 1 },
                                currentPage: { type: "number", example: 1 },
                                limit: { type: "number", example: 50 },
                              },
                            },
                          },
                        },
                        dailyUsage: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              date: { type: "string", example: "2024-01-20" },
                              images: { type: "number", example: 5 },
                              content: { type: "number", example: 3 },
                              details: {
                                type: "array",
                                items: { type: "object" },
                              },
                            },
                          },
                        },
                        webhookEvents: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              id: { type: "string", example: "WH-XXXXX" },
                              event_type: {
                                type: "string",
                                example: "BILLING.SUBSCRIPTION.ACTIVATED",
                              },
                              create_time: {
                                type: "string",
                                format: "date-time",
                              },
                              resource: {
                                type: "object",
                                properties: {
                                  subscriber: {
                                    type: "object",
                                    properties: {
                                      email_address: {
                                        type: "string",
                                        example: "<EMAIL>",
                                      },
                                    },
                                  },
                                },
                              },
                            },
                          },
                        },
                        emailQueue: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              id: { type: "string", example: "queue-uuid" },
                              email: {
                                type: "string",
                                example: "<EMAIL>",
                              },
                              type: { type: "string", example: "welcome" },
                              status: { type: "string", example: "sent" },
                              queued_at: {
                                type: "string",
                                format: "date-time",
                              },
                            },
                          },
                        },
                      },
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Email parameter tidak disediakan",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: {
                      type: "string",
                      example: "Email parameter is required",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/": {
      post: {
        summary: "Registrasi Email dan Domain",
        description:
          "<b>Registrasi email dan domain</b>. <br><br> Password akan dikirimkan melalu email, dan Password hanya digenerate sekali saat email belum pernah terdaftar. Apabila email sudah pernah terdaftar, maka yang dikirimkan selanjutnya adalah ApiKey.  <br> <br> Domain yang ditambahkan akan di-check apakah domain tersebut sudah terdaftar di API Key yang sudah ada. Apabila domain tersebut sudah terdaftar, maka akan dikirimkan pesan error. <br> <br> Jika domain belum terdaftar, maka akan dikirimkan pesan sukses dan ApiKey.",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "domain"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "User email where credentials will be sent",
                    example: "<EMAIL>",
                  },
                  domain: {
                    type: "string",
                    description: "User domain (must be unique)",
                    example: "example.com",
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "User created successfully with generated credentials",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                        domain: {
                          type: "string",
                          example: "example.com",
                        },
                        api_key: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        credentials: {
                          type: "object",
                          properties: {
                            password: {
                              type: "string",
                              description: "Auto-generated password",
                              example: "aB3$xK9#mP2&",
                            },
                            apiKey: {
                              type: "string",
                              description: "API key for authentication",
                              example: "550e8400-e29b-41d4-a716-446655440000",
                            },
                          },
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                        },
                      },
                    },
                    message: {
                      type: "string",
                      example:
                        "User created successfully. Please check your email for credentials.",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
                examples: {
                  success: {
                    value: {
                      success: true,
                      data: {
                        id: "550e8400-e29b-41d4-a716-446655440000",
                        email: "<EMAIL>",
                        domain: "example.com",
                        api_key: "550e8400-e29b-41d4-a716-446655440000",
                        credentials: {
                          password: "aB3$xK9#mP2&",
                          apiKey: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        createdAt: "2024-01-17T07:44:18.662Z",
                        updatedAt: "2024-01-17T07:44:18.662Z",
                      },
                      message:
                        "User created successfully. Please check your email for credentials.",
                      timestamp: "2024-01-17T07:44:19.010Z",
                    },
                    summary: "Successful user creation",
                  },
                  error: {
                    value: {
                      success: false,
                      message:
                        "Domain is already registered. Each domain can only have one API key.",
                      timestamp: "2024-01-17T07:44:19.010Z",
                    },
                    summary: "Domain already registered",
                  },
                },
              },
            },
          },
          400: {
            description: "Domain already registered or validation error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example:
                        "Domain is already registered. Each domain can only have one API key.",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/validatekey": {
      post: {
        summary: "Validasi API Key",
        description:
          "<b>Validasi API Key</b>. <br><br> Apabila API Key valid dan domain sesuai, maka akan dikirimkan pesan sukses. <br> <br> Apabila API Key tidak valid atau domain tidak sesuai, maka akan dikirimkan pesan error.",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: [],
          },
        ],
        responses: {
          200: {
            description: "API key validation response",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      description: "Whether the operation was successful",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        isValid: {
                          type: "boolean",
                          description:
                            "Whether the API key and domain are valid",
                          example: true,
                        },
                        message: {
                          type: "string",
                          description:
                            "Additional information about the validation result",
                          example: null,
                        },
                      },
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      description: "Timestamp of the response",
                      example: "2024-12-07T12:00:00.000Z",
                    },
                  },
                },
                examples: {
                  valid: {
                    value: {
                      success: true,
                      data: {
                        isValid: true,
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Valid API Key and Domain",
                  },
                  invalid: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "Invalid API Key",
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Invalid API Key",
                  },
                  missing_key: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "API Key is required in x-sps-key header",
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Missing API Key",
                  },
                  missing_domain: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message: "Domain is required in x-sps-domain header",
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Missing Domain",
                  },
                  domain_mismatch: {
                    value: {
                      success: true,
                      data: {
                        isValid: false,
                        message:
                          "Domain does not match the registered domain for this API key",
                      },
                      timestamp: "2024-12-07T12:00:00.000Z",
                    },
                    summary: "Domain Mismatch",
                  },
                },
              },
            },
          },
          500: {
            description: "Server error occurred",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Server error occurred",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/userdetail": {
      get: {
        summary: "Detail User by API Key",
        description:
          "<b>Detail User by API Key</b>. <br><br> Menampilkan data detail user berdasarkan API Key.",
        tags: ["Users"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        responses: {
          200: {
            description: "User details retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UserResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/users": {
      post: {
        summary: "Register Portal User",
        description:
          "<b>Register Portal User</b>. <br><br> Registrasi user portal dengan validasi username (email format) dan password. Username tidak boleh mengandung spasi dan harus format email yang valid. Password minimal 6 karakter dan tidak boleh mengandung spasi.",
        tags: ["Portal"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["username", "password"],
                properties: {
                  username: {
                    type: "string",
                    format: "email",
                    description:
                      "Username in email format (cannot contain spaces)",
                    example: "<EMAIL>",
                  },
                  password: {
                    type: "string",
                    minLength: 6,
                    description:
                      "Password (minimum 6 characters, cannot contain spaces)",
                    example: "securepass123",
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Portal user registered successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        userId: {
                          type: "string",
                          example: "portal_1234567890_abc123def456",
                        },
                        email: {
                          type: "string",
                          format: "email",
                          example: "<EMAIL>",
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-01-01T00:00:00.000Z",
                        },
                      },
                    },
                    message: {
                      type: "string",
                      example: "Portal user registered successfully",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-01-01T00:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Validation error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example:
                        "Username is required, Password must be at least 6 characters long",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-01-01T00:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
          409: {
            description: "User already exists",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "User already exists with this email",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-01-01T00:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Internal server error during registration",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-01-01T00:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/login": {
      post: {
        summary: "Portal User Login",
        description:
          "<b>Portal User Login</b>. <br><br> Authenticates a portal user and returns a JSON Web Token (JWT) if successful.",
        tags: ["Portal"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["username", "password"],
                properties: {
                  username: {
                    type: "string",
                    format: "email",
                    description: "User's email address.",
                    example: "<EMAIL>",
                  },
                  password: {
                    type: "string",
                    description: "User's password.",
                    example: "securepass123",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Login successful, returns JWT token.",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        token: {
                          type: "string",
                          description: "JSON Web Token for authentication.",
                          example:
                            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJwb3J0YWxfMTIzIiwiaWF0IjoxNjI4NjUwODAwfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                        },
                      },
                    },
                    message: {
                      type: "string",
                      example: "Login successful.",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Username and password are required.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          401: {
            description: "Invalid credentials.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          500: {
            description: "Internal server error.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/forgotpassword": {
      post: {
        summary: "Request Password Reset",
        description:
          "Initiates the password reset process for a portal user. It accepts an email address and sends a password reset link if the user exists.",
        tags: ["Portal"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description:
                      "The email address of the user who forgot their password.",
                    example: "<EMAIL>",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description:
              "If an account with that email exists, a password reset link has been sent.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          400: {
            description: "Invalid email format or email is required.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          500: {
            description: "Internal server error.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/reset-password": {
      post: {
        summary: "Reset Password",
        description: "Resets the user's password using a valid token.",
        tags: ["Portal"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["token", "newPassword"],
                properties: {
                  token: {
                    type: "string",
                    description:
                      "The password reset token from the email link.",
                  },
                  newPassword: {
                    type: "string",
                    description: "The new password for the user account.",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Password has been reset successfully.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          400: {
            description:
              "Token and new password are required, or the token is invalid/expired.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          500: {
            description: "Internal server error.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/auth/google": {
      get: {
        summary: "Redirect to Google for Authentication",
        description:
          "Initiates the Google OAuth 2.0 flow by redirecting the user to Google's consent screen.",
        tags: ["Portal"],
        responses: {
          302: {
            description: "Redirect to Google's OAuth consent screen.",
            headers: {
              Location: {
                schema: {
                  type: "string",
                },
                description: "The URL to Google's authentication service.",
              },
            },
          },
          500: {
            description:
              "Server configuration error (e.g., missing Google Client ID).",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/auth/google/callback": {
      get: {
        summary: "Handle Google OAuth Callback",
        description:
          "This is the callback endpoint for Google OAuth. It handles the authorization code, exchanges it for a token, fetches user info, and redirects to the frontend with a JWT.",
        tags: ["Portal"],
        parameters: [
          {
            name: "code",
            in: "query",
            required: true,
            description: "The authorization code returned from Google.",
            schema: {
              type: "string",
            },
          },
        ],
        responses: {
          302: {
            description:
              "Redirect to the frontend dashboard with a JWT token in the query parameters.",
            headers: {
              Location: {
                schema: {
                  type: "string",
                },
                description: "The frontend URL with the session token.",
                example:
                  "http://localhost:3000/dashboard?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
              },
            },
          },
          400: {
            description:
              "Bad Request, e.g., missing authorization code or invalid request from Google.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          500: {
            description:
              "Internal server error during the Google callback process.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/dashboard": {
      get: {
        summary: "Get Portal Dashboard Data",
        description:
          "<b>Get Portal Dashboard Data</b>. <br><br> Retrieves dashboard data for authenticated portal users including website count, credit usage, plan information, and website details.",
        tags: ["Portal"],
        security: [
          {
            bearerAuth: [],
          },
        ],
        responses: {
          200: {
            description: "Dashboard data retrieved successfully.",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        summary: {
                          type: "object",
                          properties: {
                            totalWebsites: {
                              type: "integer",
                              description:
                                "Total number of websites registered",
                              example: 3,
                            },
                            maxWebsites: {
                              oneOf: [
                                { type: "integer" },
                                { type: "string", enum: ["Unlimited"] },
                              ],
                              description:
                                "Maximum websites allowed in current plan",
                              example: 5,
                            },
                            totalCreditsUsed: {
                              type: "integer",
                              description:
                                "Total credits used across all websites",
                              example: 150,
                            },
                            maxCredits: {
                              type: "integer",
                              description:
                                "Maximum credits allowed in current plan",
                              example: 500,
                            },
                            remainingCredits: {
                              type: "integer",
                              description: "Remaining credits available",
                              example: 350,
                            },
                            totalPosts: {
                              type: "integer",
                              description: "Total posts/API calls made",
                              example: 45,
                            },
                            currentPlan: {
                              type: "string",
                              description: "Current subscription plan name",
                              example: "Pro",
                            },
                          },
                        },
                        websites: {
                          type: "array",
                          description: "Details of each registered website",
                          items: {
                            type: "object",
                            properties: {
                              domain: {
                                type: "string",
                                description: "Website domain",
                                example: "example.com",
                              },
                              status: {
                                type: "string",
                                description: "Website status",
                                enum: ["active", "pending", "inactive"],
                                example: "active",
                              },
                              creditsUsed: {
                                type: "integer",
                                description: "Credits used by this website",
                                example: 50,
                              },
                              remainingCredits: {
                                type: "integer",
                                description:
                                  "Remaining credits for this website",
                                example: 100,
                              },
                              totalScans: {
                                type: "integer",
                                description: "Total scans/API calls made",
                                example: 15,
                              },
                              lastScan: {
                                type: "string",
                                format: "date-time",
                                description: "Last scan timestamp",
                                example: "2024-01-15T10:30:00.000Z",
                              },
                              apiKey: {
                                type: "string",
                                description: "Partial API key for security",
                                example: "abc12345...",
                              },
                            },
                          },
                        },
                        planDetails: {
                          type: "object",
                          description: "Current plan details",
                          properties: {
                            tier: {
                              type: "string",
                              description: "Plan tier",
                              example: "pro",
                            },
                            name: {
                              type: "string",
                              description: "Plan name",
                              example: "Pro",
                            },
                            price: {
                              type: "number",
                              description: "Plan price",
                              example: 40,
                            },
                            features: {
                              type: "array",
                              description: "Plan features",
                              items: {
                                type: "string",
                              },
                              example: ["High quota", "Priority support"],
                            },
                            limits: {
                              type: "object",
                              description: "Plan limits",
                              properties: {
                                domains: {
                                  type: "integer",
                                  description: "Maximum domains",
                                  example: 15,
                                },
                                images: {
                                  type: "integer",
                                  description: "Images quota",
                                  example: 500,
                                },
                                content: {
                                  type: "integer",
                                  description: "Content quota",
                                  example: 500,
                                },
                                title: {
                                  type: "integer",
                                  description: "Title quota",
                                  example: 500,
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Authorization required or invalid token.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          500: {
            description: "Internal server error.",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/portal/activate": {
      get: {
        summary: "Activate Portal User",
        description:
          "<b>Activate Portal User</b>. <br><br> Activates a portal user account using the activation token from the URL query parameter.",
        tags: ["Portal"],
        parameters: [
          {
            name: "token",
            in: "query",
            required: true,
            description: "The activation token from the registration email.",
            schema: {
              type: "string",
            },
            example: "act_1672531200000_abcdef123456789",
          },
        ],
        responses: {
          200: {
            description: "Account activated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          format: "email",
                          example: "<EMAIL>",
                        },
                      },
                    },
                    message: {
                      type: "string",
                      example:
                        "Account activated successfully! You can now login.",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid or missing token",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/ErrorResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/quota/zero": {
      post: {
        summary: "Set quota to zero",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Sets Title, Image, Content Kuota ke 0",
        responses: {
          200: {
            description: "Quota set to zero successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        isQuotaZero: {
                          type: "boolean",
                          description: "Confirms if quota is set to zero",
                          example: true,
                        },
                        tierStatus: {
                          type: "object",
                          properties: {
                            remainingImages: {
                              type: "number",
                              description: "Remaining image quota",
                              example: 0,
                            },
                            remainingContent: {
                              type: "number",
                              description: "Remaining content quota",
                              example: 0,
                            },
                            totalImages: {
                              type: "number",
                              description: "Total image quota for tier",
                              example: 1000,
                            },
                            totalContent: {
                              type: "number",
                              description: "Total content quota for tier",
                              example: 1000,
                            },
                            currentTier: {
                              type: "string",
                              description: "Current tier name",
                              example: "starter",
                            },
                          },
                        },
                      },
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-12-08T12:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Error setting quota to zero",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Error setting quota to zero",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                      example: "2024-12-08T12:00:00.000Z",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/settings": {
      get: {
        summary: "Get tier settings",
        tags: ["Tiers"],
        description:
          "Retrieve all tier configurations including quotas and features",
        responses: {
          200: {
            description: "Tier settings retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        config: {
                          type: "object",
                          properties: {
                            starter: {
                              $ref: "#/components/schemas/TierConfig",
                            },
                            basic: { $ref: "#/components/schemas/TierConfig" },
                            pro: { $ref: "#/components/schemas/TierConfig" },
                            enterprise: {
                              $ref: "#/components/schemas/TierConfig",
                            },
                          },
                        },
                        updatedAt: { type: "string", format: "date-time" },
                        version: { type: "string" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/status": {
      get: {
        summary: "Get user tier status",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description:
          "Get current user tier status including usage and quota information",
        responses: {
          200: {
            description: "User tier status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/TierStatusResponse",
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/upgrade": {
      post: {
        summary: "Upgrade user tier",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Upgrade user to a different tier with optional add-ons",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["starter", "basic", "pro", "enterprise"],
                    description: "The tier to upgrade to",
                  },
                  addon1: {
                    type: "boolean",
                    description: "Enable addon1 features",
                    default: false,
                  },
                  addon2: {
                    type: "boolean",
                    description: "Enable addon2 features",
                    default: false,
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Tier upgraded successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        email: { type: "string" },
                        tier: { type: "string" },
                        previousTier: { type: "string" },
                        updatedAt: { type: "string", format: "date-time" },
                        quotas: {
                          type: "object",
                          properties: {
                            images: {
                              type: "object",
                              properties: {
                                base: { type: "number" },
                                additional: { type: "number" },
                                total: { type: "number" },
                                used: { type: "number" },
                                remaining: { type: "number" },
                              },
                            },
                            content: {
                              type: "object",
                              properties: {
                                base: { type: "number" },
                                additional: { type: "number" },
                                total: { type: "number" },
                                used: { type: "number" },
                                remaining: { type: "number" },
                              },
                            },
                            title: {
                              type: "object",
                              properties: {
                                base: { type: "number" },
                                additional: { type: "number" },
                                total: { type: "number" },
                                used: { type: "number" },
                                remaining: { type: "number" },
                              },
                            },
                          },
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean" },
                                price: { type: "number" },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                },
                              },
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean" },
                                price: { type: "number" },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                },
                              },
                            },
                          },
                        },
                        features: {
                          type: "array",
                          items: { type: "string" },
                        },
                        price: { type: "number" },
                      },
                    },
                    message: { type: "string" },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/quota": {
      get: {
        summary: "Get quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Get current quota usage information",
        responses: {
          200: {
            description: "Quota usage retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        usage: { type: "number" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/tiers/quota/reset": {
      post: {
        summary: "Reset quota usage",
        tags: ["Tiers"],
        security: [{ ApiKeyAuth: [] }],
        description: "Reset the quota usage counter to zero",
        responses: {
          200: {
            description: "Quota reset successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    data: {
                      type: "object",
                      properties: {
                        count: { type: "number" },
                        lastReset: { type: "string", format: "date-time" },
                      },
                    },
                    message: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/activate": {
      post: {
        tags: ["Users"],
        summary: "Aktivasi Domain by API Key",
        description: "Aktivasi License API Key untuk domain yang terdaftar.",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["license", "domain"],
                properties: {
                  license: {
                    type: "string",
                    example: "550e8400-e29b-41d4-a716-446655440000",
                  },
                  domain: {
                    type: "string",
                    example: "example.com",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Success",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "boolean",
                      example: true,
                    },
                    message: {
                      type: "string",
                      example: "Success activate api key",
                    },
                    domain: {
                      type: "string",
                      example: "example.com",
                    },
                    email: {
                      type: "string",
                      example: "<EMAIL>",
                    },
                  },
                },
                examples: {
                  success: {
                    value: {
                      status: true,
                      message: "Success activate api key",
                      domain: "example.com",
                      email: "<EMAIL>",
                    },
                    summary: "Successful activation",
                  },
                  failed: {
                    value: {
                      status: false,
                      message: "Failed activate api key",
                    },
                    summary: "Failed activation",
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request or activation failed",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "License key and domain are required",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: { type: "boolean", example: false },
                    message: {
                      type: "string",
                      example: "Failed activate api key",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/license/status": {
      post: {
        summary: "Check Status License API Key",
        description:
          "<b>Check Status License API Key</b>. <br><br> Melakukan Pengecekan Apakah API Key sudah aktif atau belum.",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["license"],
                properties: {
                  license: {
                    type: "string",
                    description: "License key to check",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "License status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                    },
                    data: {
                      type: "object",
                      properties: {
                        isValid: {
                          type: "boolean",
                        },
                        status: {
                          type: "string",
                          enum: ["active", "pending"],
                        },
                        activatedAt: {
                          type: "string",
                          format: "date-time",
                          nullable: true,
                        },
                        domain: {
                          type: "string",
                        },
                        message: {
                          type: "string",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/images": {
      post: {
        summary: "Track image usage",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: [],
          },
        ],
        description:
          "Increment the image generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the image generation",
                    example: "profile-picture",
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the image was generated",
                    example: "2024-12-08T02:30:00.000Z",
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      size: "1024x1024",
                      model: "stable-diffusion",
                    },
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "profile-picture",
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "product-image",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      size: "1024x1024",
                      model: "stable-diffusion",
                      style: "photorealistic",
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: { type: "object" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/content": {
      post: {
        summary: "Track content usage",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: [],
          },
        ],
        description:
          "Increment the content generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the content generation",
                    example: "product-description",
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the content was generated",
                    example: "2024-12-08T02:30:00.000Z",
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      wordCount: 500,
                      language: "en",
                    },
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "product-description",
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "blog-post",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      wordCount: 1500,
                      language: "en",
                      category: "technology",
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: { type: "object" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/stats": {
      get: {
        summary: "Get usage statistics",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        description:
          "Get detailed usage statistics for images, content, and title generation",
        responses: {
          200: {
            description: "Usage statistics retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        images: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 50 },
                            remaining: { type: "number", example: 950 },
                            total: { type: "number", example: 1000 },
                            percentageUsed: { type: "string", example: "5.00" },
                          },
                        },
                        content: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 30 },
                            remaining: { type: "number", example: 970 },
                            total: { type: "number", example: 1000 },
                            percentageUsed: { type: "string", example: "3.00" },
                          },
                        },
                        title: {
                          type: "object",
                          properties: {
                            used: { type: "number", example: 20 },
                            remaining: { type: "number", example: 980 },
                            total: { type: "number", example: 1000 },
                            percentageUsed: { type: "string", example: "2.00" },
                          },
                        },
                        tier: {
                          type: "object",
                          properties: {
                            name: { type: "string", example: "Basic Tier" },
                            current: { type: "string", example: "basic" },
                            expirationDate: {
                              type: "string",
                              example: "2024-12-31T23:59:59Z",
                            },
                            isExpired: { type: "boolean", example: false },
                            price: { type: "number", example: 15 },
                          },
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean", example: false },
                                price: { type: "number", example: 4.99 },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                  example: [
                                    "Enhanced resolution",
                                    "Advanced filters",
                                  ],
                                },
                              },
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: { type: "boolean", example: false },
                                price: { type: "number", example: 9.99 },
                                features: {
                                  type: "array",
                                  items: { type: "string" },
                                  example: [
                                    "Premium support",
                                    "1-on-1 training",
                                  ],
                                },
                              },
                            },
                          },
                        },
                        features: {
                          type: "array",
                          items: { type: "string" },
                          example: ["Basic API access", "Community support"],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/history": {
      get: {
        summary: "Get usage history",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        description:
          "Retrieve detailed usage history with filtering and pagination options",
        parameters: [
          {
            name: "startDate",
            in: "query",
            description: "Filter results from this date (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-01",
            },
          },
          {
            name: "endDate",
            in: "query",
            description: "Filter results until this date (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-31",
            },
          },
          {
            name: "type",
            in: "query",
            description: "Filter by usage type",
            schema: {
              type: "string",
              enum: ["images", "content"],
            },
          },
          {
            name: "limit",
            in: "query",
            description: "Number of results per page",
            schema: {
              type: "integer",
              minimum: 1,
              maximum: 100,
              default: 100,
            },
          },
          {
            name: "page",
            in: "query",
            description: "Page number",
            schema: {
              type: "integer",
              minimum: 1,
              default: 1,
            },
          },
        ],
        responses: {
          200: {
            description: "Usage history retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        entries: {
                          type: "array",
                          items: {
                            $ref: "#/components/schemas/UsageHistoryEntry",
                          },
                        },
                        pagination: {
                          type: "object",
                          properties: {
                            total: { type: "integer", example: 50 },
                            totalPages: { type: "integer", example: 5 },
                            currentPage: { type: "integer", example: 1 },
                            limit: { type: "integer", example: 10 },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/daily": {
      get: {
        summary: "Get daily usage aggregates",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        description: "Retrieve daily aggregated usage statistics",
        parameters: [
          {
            name: "startDate",
            in: "query",
            description: "Start date for daily stats (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-01",
            },
          },
          {
            name: "endDate",
            in: "query",
            description: "End date for daily stats (ISO 8601)",
            schema: {
              type: "string",
              format: "date",
              example: "2024-12-31",
            },
          },
        ],
        responses: {
          200: {
            description: "Daily usage statistics retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "array",
                      items: {
                        $ref: "#/components/schemas/DailyUsage",
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions": {
      post: {
        summary: "Create new PayPal subscription",
        description:
          "Creates a new PayPal subscription for the specified tier with automatic price fetching from tier settings",
        tags: ["Subscriptions"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["basic", "pro", "enterprise"],
                    description:
                      "The tier level to subscribe to. Price will be automatically fetched from tier settings.",
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic tier subscription",
                  value: {
                    tier: "basic",
                  },
                },
                enterprise: {
                  summary: "Enterprise tier subscription",
                  value: {
                    tier: "enterprise",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          description: "PayPal subscription identifier",
                          example: "I-BW452GLLEP1G",
                        },
                        approvalUrl: {
                          type: "string",
                          description: "PayPal URL for subscription approval",
                          example:
                            "https://www.sandbox.paypal.com/webapps/billing/subscriptions?token=...",
                        },
                        tier: {
                          type: "string",
                          description: "Selected subscription tier",
                          example: "basic",
                        },
                        price: {
                          type: "number",
                          description: "Monthly subscription price in USD",
                          example: 9.99,
                        },
                        status: {
                          type: "string",
                          description: "Initial subscription status",
                          enum: ["APPROVAL_PENDING", "APPROVED", "ACTIVE"],
                          example: "APPROVAL_PENDING",
                        },
                      },
                    },
                  },
                },
                examples: {
                  basicTier: {
                    summary: "Basic tier response",
                    value: {
                      success: true,
                      data: {
                        subscriptionId: "I-BW452GLLEP1G",
                        approvalUrl:
                          "https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=EC-5RT15012PY123456",
                        tier: "basic",
                        price: 15,
                        status: "APPROVAL_PENDING",
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Error creating subscription",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                    },
                  },
                },
                examples: {
                  invalidTier: {
                    summary: "Invalid tier specified",
                    value: {
                      success: false,
                      message: "Invalid tier specified",
                    },
                  },
                  missingApiKey: {
                    summary: "Missing API key",
                    value: {
                      success: false,
                      message: "API key is required in x-sps-key header",
                    },
                  },
                  paypalError: {
                    summary: "PayPal API error",
                    value: {
                      success: false,
                      message: "Failed to create PayPal subscription",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/success": {
      get: {
        summary: "PayPal subscription success callback",
        description:
          "Endpoint called by PayPal after successful subscription approval",
        tags: ["Subscriptions"],
        parameters: [
          {
            name: "subscription_id",
            in: "query",
            required: true,
            schema: {
              type: "string",
            },
            description: "PayPal subscription identifier",
          },
        ],
        responses: {
          302: {
            description: "Redirects to frontend success page",
            headers: {
              Location: {
                schema: {
                  type: "string",
                  example:
                    "https://your-app.com/subscription/success?subscription_id=I-BW452GLLEP1G",
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/webhook": {
      post: {
        summary: "PayPal webhook endpoint",
        description: "Handles PayPal subscription event notifications",
        tags: ["Subscriptions"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  event_type: {
                    type: "string",
                    description: "PayPal event type",
                    example: "BILLING.SUBSCRIPTION.ACTIVATED",
                  },
                  resource: {
                    type: "object",
                    description: "Event resource details",
                  },
                },
              },
              examples: {
                activated: {
                  summary: "Subscription activated",
                  value: {
                    event_type: "BILLING.SUBSCRIPTION.ACTIVATED",
                    resource: {
                      id: "I-BW452GLLEP1G",
                      status: "ACTIVE",
                    },
                  },
                },
                payment: {
                  summary: "Payment completed",
                  value: {
                    event_type: "PAYMENT.SALE.COMPLETED",
                    resource: {
                      id: "5RT15012PY123456",
                      amount: {
                        total: "9.99",
                        currency: "USD",
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Webhook processed successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    received: {
                      type: "boolean",
                      example: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/status/{subscriptionId}": {
      get: {
        summary: "Get subscription status",
        description:
          "Retrieves the current status and details of a PayPal subscription",
        tags: ["Subscriptions"],
        parameters: [
          {
            name: "subscriptionId",
            in: "path",
            required: true,
            schema: {
              type: "string",
            },
            description: "PayPal subscription identifier",
            example: "I-FBN91UE2890B",
          },
        ],
        responses: {
          200: {
            description: "Subscription status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-FBN91UE2890B",
                        },
                        status: {
                          type: "string",
                          example: "ACTIVE",
                        },
                        planId: {
                          type: "string",
                          example: "P-6L993232BE527384WM5Q53VA",
                        },
                        startTime: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:32:32Z",
                        },
                        nextBillingTime: {
                          type: "string",
                          format: "date-time",
                          example: "2025-01-17T08:00:00Z",
                        },
                        lastPaymentTime: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:33:23Z",
                        },
                        failedPayments: {
                          type: "integer",
                          example: 0,
                        },
                        tier: {
                          type: "string",
                          example: "basic",
                        },
                        price: {
                          type: "number",
                          example: 9.99,
                        },
                        localStatus: {
                          type: "string",
                          example: "ACTIVE",
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T07:32:35.281Z",
                        },
                        activatedAt: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T08:24.356Z",
                        },
                        lastUpdated: {
                          type: "string",
                          format: "date-time",
                          example: "2024-12-17T08:27.870Z",
                        },
                      },
                    },
                  },
                },
                example: {
                  success: true,
                  data: {
                    subscriptionId: "I-FBN91UE2890B",
                    status: "ACTIVE",
                    planId: "P-6L993232BE527384WM5Q53VA",
                    startTime: "2024-12-17T07:32:32Z",
                    nextBillingTime: "2025-01-17T08:00:00Z",
                    lastPaymentTime: "2024-12-17T07:33:23Z",
                    failedPayments: 0,
                    tier: "basic",
                    price: 9.99,
                    localStatus: "ACTIVE",
                    createdAt: "2024-12-17T07:32:35.281Z",
                    activatedAt: "2024-12-17T08:24.356Z",
                    lastUpdated: "2024-12-17T08:27.870Z",
                  },
                },
              },
            },
          },
          400: {
            description: "Error retrieving subscription status",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to get subscription status",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/create": {
      post: {
        summary: "Create subscription with optional add-ons",
        tags: ["Subscriptions"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["starter", "basic", "pro", "enterprise"],
                    description: "Subscription tier level",
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable addon 1 features",
                        default: false,
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable addon 2 features",
                        default: false,
                      },
                    },
                  },
                },
              },
              examples: {
                "Basic Subscription": {
                  value: {
                    tier: "basic",
                    addons: {
                      addon1: false,
                      addon2: false,
                    },
                  },
                },
                "Subscription with Add-ons": {
                  value: {
                    tier: "basic",
                    addons: {
                      addon1: true,
                      addon2: true,
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-BW452GLLEP1G",
                        },
                        approvalUrl: {
                          type: "string",
                          example:
                            "https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=EC-5Y366741JN879735H",
                        },
                        tier: {
                          type: "string",
                          example: "basic",
                        },
                        basePrice: {
                          type: "number",
                          example: 9.99,
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "object",
                              properties: {
                                enabled: {
                                  type: "boolean",
                                  example: true,
                                },
                                price: {
                                  type: "number",
                                  example: 9.99,
                                },
                                features: {
                                  type: "array",
                                  items: {
                                    type: "string",
                                  },
                                  example: [
                                    "Enhanced resolution",
                                    "Advanced filters",
                                    "Batch processing",
                                    "Priority processing",
                                  ],
                                },
                              },
                            },
                            addon2: {
                              type: "object",
                              properties: {
                                enabled: {
                                  type: "boolean",
                                  example: false,
                                },
                                price: {
                                  type: "number",
                                  example: 19.99,
                                },
                                features: {
                                  type: "array",
                                  items: {
                                    type: "string",
                                  },
                                },
                              },
                            },
                          },
                        },
                        totalPrice: {
                          type: "number",
                          example: 19.98,
                        },
                        status: {
                          type: "string",
                          example: "APPROVAL_PENDING",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Invalid tier specified",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/subscriptions/addons": {
      put: {
        summary: "Update subscription add-ons",
        tags: ["Subscriptions"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["subscriptionId", "addons"],
                properties: {
                  subscriptionId: {
                    type: "string",
                    description: "PayPal subscription ID",
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable/disable addon 1",
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable/disable addon 2",
                      },
                    },
                  },
                },
              },
              example: {
                subscriptionId: "I-BW452GLLEP1G",
                addons: {
                  addon1: true,
                  addon2: false,
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Add-ons updated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        subscriptionId: {
                          type: "string",
                          example: "I-BW452GLLEP1G",
                        },
                        newPrice: {
                          type: "number",
                          example: 19.98,
                        },
                        addons: {
                          type: "object",
                          properties: {
                            addon1: {
                              type: "boolean",
                              example: true,
                            },
                            addon2: {
                              type: "boolean",
                              example: false,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Invalid subscription ID",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/title": {
      post: {
        summary: "Track title usage",
        tags: ["Usage"],
        security: [
          {
            ApiKeyAuth: [],
            DomainAuth: [],
          },
        ],
        description:
          "Increment the title generation usage counter for the user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["source", "timestamp"],
                properties: {
                  source: {
                    type: "string",
                    description: "Source of the title generation",
                    example: "product-title",
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "When the title was generated",
                    example: "2024-12-08T02:30:00.000Z",
                  },
                  metadata: {
                    type: "object",
                    description: "Optional additional information",
                    example: {
                      language: "en",
                      category: "product",
                      length: "short",
                    },
                  },
                },
              },
              examples: {
                basic: {
                  summary: "Basic usage",
                  value: {
                    source: "product-title",
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
                withMetadata: {
                  summary: "With metadata",
                  value: {
                    source: "blog-title",
                    timestamp: "2024-12-08T02:30:00.000Z",
                    metadata: {
                      language: "en",
                      category: "blog",
                      length: "basic",
                      keywords: ["tech", "ai"],
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Usage tracked successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean" },
                    message: { type: "string" },
                    data: {
                      type: "object",
                      properties: {
                        tracked: { type: "boolean" },
                        currentUsage: { type: "number" },
                        remainingQuota: { type: "number" },
                        timestamp: {
                          type: "string",
                          format: "date-time",
                        },
                      },
                    },
                  },
                },
                example: {
                  success: true,
                  message: "Title generation usage tracked successfully",
                  data: {
                    tracked: true,
                    currentUsage: 51,
                    remainingQuota: 949,
                    timestamp: "2024-12-08T02:30:00.000Z",
                  },
                },
              },
            },
          },
          400: {
            description: "Error tracking usage",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    message: { type: "string" },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
                example: {
                  success: false,
                  message: "Invalid request format or quota exceeded",
                  timestamp: "2024-12-08T02:30:00.000Z",
                },
              },
            },
          },
        },
      },
    },
    "/api/users/login": {
      post: {
        summary: "User Login",
        description: "Authenticate user with email and password",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "password"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "User's email address",
                    example: "<EMAIL>",
                  },
                  password: {
                    type: "string",
                    description: "User's password",
                    example: "yourPassword123",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Login response",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                        domain: {
                          type: "string",
                          example: "example.com",
                        },
                        api_key: {
                          type: "string",
                          example: "550e8400-e29b-41d4-a716-446655440000",
                        },
                        credentials: {
                          type: "object",
                          properties: {
                            password: {
                              type: "string",
                              description: "User's password",
                              example: "yourPassword123",
                            },
                            apiKey: {
                              type: "string",
                              description: "API key for authentication",
                              example: "550e8400-e29b-41d4-a716-446655440000",
                            },
                          },
                        },
                        tier: {
                          type: "object",
                          properties: {
                            // ... existing tier properties
                          },
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                        },
                        token: {
                          type: "string",
                          description: "JWT token for authentication",
                          example:
                            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                        },
                      },
                    },
                    message: {
                      type: "string",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
                examples: {
                  success: {
                    value: {
                      success: true,
                      data: {
                        // ... existing data properties
                        token:
                          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                      },
                      message: "Login successful",
                      timestamp: "2024-01-17T07:44:19.010Z",
                    },
                    summary: "Successful login",
                  },
                  failed: {
                    value: {
                      success: false,
                      message: "Invalid email or password",
                      timestamp: "2024-01-17T07:44:19.010Z",
                    },
                    summary: "Failed login",
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/verifytoken": {
      get: {
        tags: ["Users"],
        summary: "Verify JWT token",
        description:
          "Verifies the provided JWT token and returns user information",
        security: [
          {
            bearerAuth: [], // Changed from ApiKeyAuth to bearerAuth
          },
        ],
        responses: {
          200: {
            description: "Token verification successful",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    valid: {
                      type: "boolean",
                      example: true,
                    },
                    user: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Invalid or missing token",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    valid: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Invalid token",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/users/domains": {
      post: {
        summary: "Get domains for an email",
        description: "Retrieve all domains associated with an email address",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address to get domains for",
                    example: "<EMAIL>",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "List of domains for the email",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          domain: {
                            type: "string",
                            example: "example.com",
                          },
                          api_key: {
                            type: "string",
                            example: "550e8400-e29b-41d4-a716-446655440000",
                          },
                          status: {
                            type: "string",
                            example: "active",
                          },
                          createdAt: {
                            type: "string",
                            format: "date-time",
                          },
                        },
                      },
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
                examples: {
                  success: {
                    value: {
                      success: true,
                      data: [
                        {
                          domain: "example1.com",
                          api_key: "550e8400-e29b-41d4-a716-446655440000",
                          status: "active",
                          createdAt: "2024-01-17T07:44:18.662Z",
                        },
                        {
                          domain: "example2.com",
                          api_key: "660e8400-e29b-41d4-a716-446655440000",
                          status: "active",
                          createdAt: "2024-01-18T07:44:18.662Z",
                        },
                      ],
                      timestamp: "2024-01-17T07:44:19.010Z",
                    },
                    summary: "Successful domains retrieval",
                  },
                },
              },
            },
          },
          400: {
            description: "Email not found or invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Email is required",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
        },
      },
      delete: {
        summary: "Remove a domain from an email",
        description:
          "Remove a domain and its associated API key from an email address",
        tags: ["Users"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "domain"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address to remove domain from",
                    example: "<EMAIL>",
                  },
                  domain: {
                    type: "string",
                    description: "Domain to remove",
                    example: "example.com",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Domain successfully removed",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    message: {
                      type: "string",
                      example: "Domain removed successfully",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Email or domain not found, or invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Email and domain are required",
                    },
                    timestamp: {
                      type: "string",
                      format: "date-time",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/test/webhooks/simulate/paypal/activate": {
      post: {
        tags: ["Test Webhooks"],
        summary: "Simulate PayPal subscription activation webhook",
        description:
          "For development only: Simulates a PayPal webhook notification for subscription activation",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["subscriptionId", "userId", "tier"],
                properties: {
                  subscriptionId: {
                    type: "string",
                    description: "PayPal subscription ID",
                    example: "I-GC8W90MU28NW",
                  },
                  userId: {
                    type: "string",
                    description: "User's email address",
                    example: "<EMAIL>",
                  },
                  tier: {
                    type: "string",
                    description:
                      "Subscription tier (starter, basic, pro, enterprise)",
                    enum: ["starter", "basic", "pro", "enterprise"],
                    example: "basic",
                  },
                },
              },
              example: {
                subscriptionId: "I-GC8W90MU28NW",
                userId: "<EMAIL>",
                tier: "basic",
              },
            },
          },
        },
        responses: {
          200: {
            description: "Subscription activated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    message: {
                      type: "string",
                      example: "Subscription activated successfully",
                    },
                    data: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                        subscriptionId: {
                          type: "string",
                          example: "I-GC8W90MU28NW",
                        },
                        tier: {
                          type: "string",
                          example: "basic",
                        },
                        timestamp: {
                          type: "string",
                          format: "date-time",
                          example: "2024-01-20T06:30:00.000Z",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request - missing required fields",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "subscriptionId, userId, and tier are required",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "User not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "User not found",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Internal server error",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/debug/kv/list": {
      get: {
        tags: ["Debug"],
        summary: "List all KV entries",
        description: "Lists all KV entries grouped by their key patterns",
        security: [{ ApiKeyAuth: [] }],
        responses: {
          200: {
            description: "Successfully retrieved KV data",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        summary: {
                          type: "object",
                          properties: {
                            totalKeys: {
                              type: "integer",
                              example: 42,
                            },
                            prefixes: {
                              type: "array",
                              items: {
                                type: "string",
                              },
                              example: [
                                "email",
                                "api_key",
                                "domain",
                                "user",
                                "subscription",
                              ],
                            },
                            countByPrefix: {
                              type: "object",
                              example: {
                                email: 10,
                                api_key: 15,
                                domain: 15,
                                user: 10,
                                subscription: 5,
                              },
                            },
                          },
                        },
                        grouped: {
                          type: "object",
                          properties: {
                            email: {
                              type: "array",
                              items: {
                                type: "object",
                                properties: {
                                  key: { type: "string" },
                                  value: { type: "object" },
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Unauthorized",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Unauthorized" },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/debug/kv/{key}": {
      get: {
        tags: ["Debug"],
        summary: "Get KV entry by key",
        description: "Retrieves a specific KV entry by its key",
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "key",
            in: "path",
            required: true,
            schema: {
              type: "string",
            },
            description:
              "The full key to retrieve (e.g., 'email:<EMAIL>')",
          },
        ],
        responses: {
          200: {
            description: "Successfully retrieved KV entry",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: { type: "object" },
                  },
                },
              },
            },
          },
          404: {
            description: "Key not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Key not found" },
                  },
                },
              },
            },
          },
        },
      },
      delete: {
        tags: ["Debug"],
        summary: "Delete KV entry",
        description: "Deletes a specific KV entry",
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "key",
            in: "path",
            required: true,
            schema: {
              type: "string",
            },
            description: "The full key to delete",
          },
        ],
        responses: {
          200: {
            description: "Successfully deleted KV entry",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    message: {
                      type: "string",
                      example: "Key deleted successfully",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Key not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Key not found" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/debug/kv/search": {
      get: {
        tags: ["Debug"],
        summary: "Search KV entries",
        description: "Searches KV entries by prefix or pattern",
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "prefix",
            in: "query",
            schema: {
              type: "string",
            },
            description:
              "Key prefix to search for (e.g., 'email:', 'api_key:')",
          },
          {
            name: "pattern",
            in: "query",
            schema: {
              type: "string",
            },
            description: "Pattern to match in keys or values",
          },
        ],
        responses: {
          200: {
            description: "Successfully retrieved matching KV entries",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          key: { type: "string" },
                          value: { type: "object" },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/debug/kv/truncate": {
      post: {
        tags: ["Debug"],
        summary: "Truncate KV data",
        description: "Deletes all KV data except tier settings configuration",
        security: [{ ApiKeyAuth: [] }],
        responses: {
          200: {
            description: "Successfully truncated KV data",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        deletedCount: { type: "integer", example: 42 },
                        skippedCount: { type: "integer", example: 1 },
                        deletedKeys: {
                          type: "array",
                          items: { type: "string" },
                          example: ["user:123", "email:<EMAIL>"],
                        },
                        skippedKeys: {
                          type: "array",
                          items: { type: "string" },
                          example: ["t_setting:tiers"],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/debug/kv/truncate/status": {
      get: {
        tags: ["Debug"],
        summary: "Check KV truncate status",
        description:
          "Checks if the KV store is in a clean state with only tier settings remaining",
        security: [{ ApiKeyAuth: [] }],
        responses: {
          200: {
            description: "Successfully checked truncate status",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        isClean: {
                          type: "boolean",
                          description: "True if only tier settings remain",
                          example: true,
                        },
                        totalKeys: {
                          type: "integer",
                          description: "Total number of keys in the KV store",
                          example: 1,
                        },
                        hasTierSettings: {
                          type: "boolean",
                          description: "True if tier settings exist",
                          example: true,
                        },
                        hasOtherData: {
                          type: "boolean",
                          description: "True if any non-tier data exists",
                          example: false,
                        },
                        keys: {
                          type: "array",
                          description: "List of all keys in the KV store",
                          items: { type: "string" },
                          example: ["t_setting:tiers"],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Internal server error" },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/qris": {
      post: {
        summary: "Create QRIS Payment",
        description:
          "Generate a QRIS code for payment using Xendit. The response includes the QR code string that can be scanned using any QRIS-compatible mobile banking or e-wallet app.",
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["external_id", "amount"],
                properties: {
                  external_id: {
                    type: "string",
                    description: "Your unique reference ID for this payment",
                    example: "qris-payment-001",
                  },
                  amount: {
                    type: "number",
                    description: "Payment amount in IDR",
                    example: 10000,
                  },
                  description: {
                    type: "string",
                    description: "Description of the payment",
                    example: "Payment for order #123",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "QRIS payment created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    id: {
                      type: "string",
                      example: "qr_e93b8fef-aa55-4c27-b129-fc88335cf789",
                    },
                    reference_id: {
                      type: "string",
                      example: "qris-payment-001",
                    },
                    type: {
                      type: "string",
                      example: "DYNAMIC",
                    },
                    currency: {
                      type: "string",
                      example: "IDR",
                    },
                    channel_code: {
                      type: "string",
                      example: "ID_XENDIT",
                    },
                    amount: {
                      type: "number",
                      example: 10000,
                    },
                    description: {
                      type: "string",
                      example: "Payment for order #123",
                    },
                    metadata: {
                      type: "object",
                      nullable: true,
                      example: null,
                    },
                    business_id: {
                      type: "string",
                      example: "679e3e04efb31029478754c",
                    },
                    created: {
                      type: "string",
                      format: "date-time",
                      example: "2025-02-04T14:22:04.056894Z",
                    },
                    updated: {
                      type: "string",
                      format: "date-time",
                      example: "2025-02-04T14:22:04.056894Z",
                    },
                    qr_string: {
                      type: "string",
                      description: "The QRIS code string that can be scanned",
                      example:
                        "00020101021226670016ID.CO.QRIS.WWW0215ID10200324152880303UME51440014ID.CO.QRIS.WWW0215ID1020032415288020303UME5204581453033605405100005802ID5914XENDIT QR TEST6013JAKARTA PUSAT61051234562070703A016304E667",
                    },
                    status: {
                      type: "string",
                      example: "ACTIVE",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad Request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "string",
                      example: "error",
                    },
                    message: {
                      type: "string",
                      example: "external_id and amount are required",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: {
                      type: "string",
                      example: "error",
                    },
                    message: {
                      type: "string",
                      example: "Failed to create QRIS payment",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/virtual-account": {
      post: {
        summary: "Create Virtual Account Payment",
        description:
          "<b>Create a Virtual Account payment</b>. <br><br> Creates a new virtual account for bank transfer payment using Xendit. Different banks have different requirements and supported features. <br><br> <b>Bank-specific notes:</b> <br> - Description field is only supported by BRI and BSI <br> - Min/max amounts are only supported by BCA, BNC, and CIMB <br> - BCA requires minimum amount of 50,000 IDR <br> - Suggested amounts for open VAs are only supported by BRI, BJB, and MANDIRI <br> - Each bank has different amount limits and currency support <br><br> <b>Supported Banks:</b> <br> - BCA (Bank Central Asia) - Min amount: 50,000 IDR <br> - BNI (Bank Negara Indonesia) <br> - BRI (Bank Rakyat Indonesia) <br> - BJB (Bank Jabar Banten) <br> - BSI (Bank Syariah Indonesia) <br> - BNC (Bank Neo Commerce) <br> - CIMB (CIMB Niaga) <br> - DBS (Bank DBS) <br> - MANDIRI (Bank Mandiri) <br> - PERMATA (Bank Permata) <br> - SAHABAT_SAMPOERNA (Bank Sahabat Sampoerna)",
        tags: ["Xendit"],
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["external_id", "bank_code", "name", "amount"],
                properties: {
                  external_id: {
                    type: "string",
                    description: "Unique ID for the virtual account",
                    example: "va-*************",
                  },
                  bank_code: {
                    type: "string",
                    description: "Bank code for the virtual account",
                    enum: [
                      "BCA",
                      "BNI",
                      "BRI",
                      "BJB",
                      "BSI",
                      "BNC",
                      "CIMB",
                      "DBS",
                      "MANDIRI",
                      "PERMATA",
                      "SAHABAT_SAMPOERNA",
                    ],
                    example: "BRI",
                  },
                  name: {
                    type: "string",
                    description:
                      "Name that will be displayed in the virtual account",
                    example: "John Doe",
                  },
                  amount: {
                    type: "number",
                    description:
                      "Payment amount. For closed VAs, this will be the expected amount. For open VAs, this will be the suggested amount.",
                    example: 50000,
                  },
                  is_closed: {
                    type: "boolean",
                    description:
                      "Whether the virtual account has a fixed payment amount. If true, customer can only pay the exact expected_amount. If false, customer can pay any amount within min_amount and max_amount range.",
                    default: true,
                  },
                  expected_amount: {
                    type: "number",
                    description:
                      "Required for closed virtual accounts. Must match the payment amount",
                    example: 50000,
                  },
                  description: {
                    type: "string",
                    description:
                      "Payment description (supported by some banks)",
                    example: "Payment for order #123",
                  },
                  expiration_date: {
                    type: "string",
                    format: "date-time",
                    description:
                      "VA expiration time in UTC+0 (default: +31 years)",
                    example: "2025-02-05T14:51:18Z",
                  },
                },
              },
              examples: {
                "BNI Open VA": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BNI",
                    name: "Michael Chen",
                    amount: 50000,
                    is_closed: false,
                    expiration_date: "2025-02-05T14:51:18Z",
                  },
                  summary: "Open Virtual Account with BNI",
                },
                "BRI Closed VA": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BRI",
                    name: "Michael Chen",
                    amount: 50000,
                    is_closed: true,
                    expected_amount: 50000,
                    description: "Payment for order #123",
                    expiration_date: "2025-02-05T14:51:18Z",
                  },
                  summary: "Closed Virtual Account with BRI (with description)",
                },
                "BCA VA with Amount Limits": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BCA",
                    name: "Michael Chen",
                    amount: 50000,
                    is_closed: false,
                    min_amount: 10000,
                    max_amount: 1000000,
                    expiration_date: "2025-02-05T14:51:18Z",
                  },
                  summary:
                    "Open Virtual Account with BCA (with amount limits, minimum 10,000 IDR)",
                },
                "MANDIRI VA with Suggested Amount": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "MANDIRI",
                    name: "Michael Chen",
                    amount: 50000,
                    is_closed: false,
                    expiration_date: "2025-02-05T14:51:18Z",
                  },
                  summary:
                    "Open Virtual Account with MANDIRI (with suggested amount)",
                },
                "BSI VA with Description": {
                  value: {
                    external_id: "va-*************",
                    bank_code: "BSI",
                    name: "Michael Chen",
                    amount: 50000,
                    is_closed: true,
                    expected_amount: 50000,
                    description: "Payment for order #123",
                    expiration_date: "2025-02-05T14:51:18Z",
                  },
                  summary: "Closed Virtual Account with BSI (with description)",
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Virtual Account created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "57f6fbf26b9f064272622aa6",
                        },
                        external_id: {
                          type: "string",
                          example: "va-*************",
                        },
                        owner_id: {
                          type: "string",
                          example: "57b4e5181473eeb61c11f9b9",
                        },
                        bank_code: {
                          type: "string",
                          example: "BNI",
                        },
                        merchant_code: {
                          type: "string",
                          example: "8808",
                        },
                        account_number: {
                          type: "string",
                          example: "****************",
                        },
                        name: {
                          type: "string",
                          example: "Michael Chen",
                        },
                        is_single_use: {
                          type: "boolean",
                          example: false,
                        },
                        is_closed: {
                          type: "boolean",
                          example: false,
                        },
                        expected_amount: {
                          type: "number",
                          example: 50000,
                        },
                        suggested_amount: {
                          type: "number",
                          example: 50000,
                        },
                        expiration_date: {
                          type: "string",
                          format: "date-time",
                          example: "2025-02-05T14:51:18Z",
                        },
                        status: {
                          type: "string",
                          example: "PENDING",
                        },
                        currency: {
                          type: "string",
                          example: "IDR",
                        },
                        country: {
                          type: "string",
                          example: "ID",
                        },
                        description: {
                          type: "string",
                          example: "Payment for order #123",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad Request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "external_id, bank_code, and name are required",
                    },
                  },
                },
                examples: {
                  "Missing Required Fields": {
                    value: {
                      success: false,
                      error: "external_id, bank_code, and name are required",
                    },
                  },
                  "Invalid Amount": {
                    value: {
                      success: false,
                      error: "The expected amount is below the minimum limit",
                    },
                  },
                  "Bank Not Supported": {
                    value: {
                      success: false,
                      error: "That bank code is not currently supported",
                    },
                  },
                  "Feature Not Supported": {
                    value: {
                      success: false,
                      error: "description is not supported for the bank chosen",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to create virtual account",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/customer": {
      post: {
        summary: "Create Xendit Customer",
        description:
          "Create a new customer in Xendit for direct debit payments",
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["reference_id", "email", "given_names"],
                properties: {
                  reference_id: {
                    type: "string",
                    description: "Your unique reference ID for this customer",
                    example: "demo_1475801962607",
                  },
                  email: {
                    type: "string",
                    format: "email",
                    description: "Customer's email address",
                    example: "<EMAIL>",
                  },
                  given_names: {
                    type: "string",
                    description: "Customer's given names",
                    example: "John",
                  },
                  surname: {
                    type: "string",
                    description: "Customer's surname",
                    example: "Doe",
                  },
                  mobile_number: {
                    type: "string",
                    description: "Customer's mobile number in E.164 format",
                    example: "+628123456789",
                  },
                  description: {
                    type: "string",
                    description: "Description for this customer",
                    example: "Test customer for direct debit",
                  },
                  metadata: {
                    type: "object",
                    description: "Additional metadata",
                    example: {
                      city: "Jakarta",
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Customer created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "74bb3048-0bf3-4875-8938-c2c9d02e996e",
                        },
                        reference_id: {
                          type: "string",
                          example: "demo_1475801962607",
                        },
                        given_names: {
                          type: "string",
                          example: "John",
                        },
                        email: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "reference_id, email, and given_names are required",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/payment-request": {
      post: {
        summary: "Create Direct Debit Payment Request",
        description: `Create a new direct debit payment request using BRI Direct Debit.
          
          The response will include an authentication URL that requires an OTP verification:
          1. Customer will receive an OTP on their registered mobile number
          2. Send the OTP to the authentication URL provided in the response
          3. For testing, use OTP code: 333000
          
          Example authentication request:
          \`\`\`
          curl -X POST "https://api.xendit.co/v2/payment_methods/{payment_method_id}/auth" \\
            -H "Authorization: Basic {base64_encoded_api_key}" \\
            -H "Content-Type: application/json" \\
            -d '{"otp_code":"333000"}'
          \`\`\`
          `,
        tags: ["Xendit"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: [
                  "amount",
                  "currency",
                  "payment_method",
                  "customer_id",
                  "reference_id",
                ],
                properties: {
                  amount: {
                    type: "number",
                    description: "Payment amount",
                    example: 50000,
                  },
                  currency: {
                    type: "string",
                    description: "Payment currency",
                    example: "IDR",
                    enum: ["IDR"],
                  },
                  payment_method: {
                    type: "object",
                    description: "Payment method details",
                    required: ["type", "direct_debit", "reusability"],
                    properties: {
                      type: {
                        type: "string",
                        description: "Payment method type",
                        example: "DIRECT_DEBIT",
                        enum: ["DIRECT_DEBIT"],
                      },
                      direct_debit: {
                        type: "object",
                        required: ["channel_code", "channel_properties"],
                        properties: {
                          channel_code: {
                            type: "string",
                            description: "Bank channel code",
                            example: "BRI",
                            enum: ["BRI"],
                          },
                          channel_properties: {
                            type: "object",
                            required: [
                              "success_return_url",
                              "failure_return_url",
                              "mobile_number",
                              "email",
                              "card_last_four",
                              "card_expiry",
                            ],
                            properties: {
                              success_return_url: {
                                type: "string",
                                description:
                                  "URL to redirect after successful payment",
                                example: "https://redirect.me/success",
                              },
                              failure_return_url: {
                                type: "string",
                                description:
                                  "URL to redirect after failed payment",
                                example: "https://redirect.me/failure",
                              },
                              mobile_number: {
                                type: "string",
                                description:
                                  "Customer's mobile number in E.164 format",
                                example: "+628123456789",
                              },
                              email: {
                                type: "string",
                                description: "Customer's email address",
                                example: "<EMAIL>",
                              },
                              card_last_four: {
                                type: "string",
                                description: "Last 4 digits of the debit card",
                                example: "8888",
                              },
                              card_expiry: {
                                type: "string",
                                description: "Card expiry in MM/YY format",
                                example: "11/25",
                              },
                            },
                          },
                        },
                      },
                      reusability: {
                        type: "string",
                        description: "Whether the payment method can be reused",
                        example: "ONE_TIME_USE",
                        enum: ["ONE_TIME_USE"],
                      },
                    },
                  },
                  customer_id: {
                    type: "string",
                    description:
                      "Xendit customer ID (from create customer response)",
                    example: "74bb3048-0bf3-4875-8938-c2c9d02e996e",
                  },
                  description: {
                    type: "string",
                    description: "Payment description",
                    example: "Pembayaran untuk Order #123",
                  },
                  metadata: {
                    type: "object",
                    description: "Additional metadata",
                    example: {
                      order_id: "ORDER-123",
                      product_name: "Premium Package",
                    },
                  },
                  reference_id: {
                    type: "string",
                    description: "Your unique reference ID for this payment",
                    example: "order-123-abc",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description:
              "Payment request created successfully. Requires OTP authentication.",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "pr-e64ccb50-7b75-4c65-a909-958852c61386",
                        },
                        status: {
                          type: "string",
                          example: "REQUIRES_ACTION",
                          description: "Payment requires OTP authentication",
                        },
                        actions: {
                          type: "array",
                          description:
                            "Authentication actions required to complete the payment",
                          items: {
                            type: "object",
                            properties: {
                              action: {
                                type: "string",
                                example: "AUTH",
                                description:
                                  "Action type - AUTH for OTP authentication",
                              },
                              url: {
                                type: "string",
                                example:
                                  "https://api.xendit.co/v2/payment_methods/pm-xxx/auth",
                                description:
                                  "URL to send the OTP for authentication",
                              },
                              method: {
                                type: "string",
                                example: "POST",
                                description:
                                  "HTTP method to use for authentication",
                              },
                            },
                          },
                        },
                        payment_method: {
                          type: "object",
                          properties: {
                            id: {
                              type: "string",
                              description:
                                "Payment method ID needed for authentication",
                              example:
                                "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3",
                            },
                          },
                        },
                      },
                    },
                  },
                },
                example: {
                  success: true,
                  data: {
                    id: "pr-6fc5c020-e6a4-4d0a-8a23-d46d49f2427d",
                    status: "REQUIRES_ACTION",
                    actions: [
                      {
                        action: "AUTH",
                        url: "https://api.xendit.co/v2/payment_methods/pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3/auth",
                        method: "POST",
                      },
                    ],
                    payment_method: {
                      id: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3",
                      status: "PENDING",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "amount, currency, payment_method, and customer_id are required",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/payment-methods/{payment_method_id}/auth": {
      post: {
        summary: "Validate Direct Debit OTP",
        description: `Validate the OTP for a direct debit payment.
          
          For testing, use OTP code: 333000
          `,
        tags: ["Xendit"],
        parameters: [
          {
            name: "payment_method_id",
            in: "path",
            required: true,
            description:
              "Payment method ID received from the payment request response",
            schema: {
              type: "string",
            },
            example: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3",
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["otp_code"],
                properties: {
                  otp_code: {
                    type: "string",
                    description:
                      "OTP code received by the customer (use 333000 for testing)",
                    example: "333000",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "OTP validated successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        id: {
                          type: "string",
                          example: "pm-e15b255b-7bba-4658-8e67-6fd8bc587fe3",
                        },
                        status: {
                          type: "string",
                          example: "SUCCESS",
                          description:
                            "Status of the payment method after OTP validation",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Invalid request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "payment_method_id and otp_code are required",
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Invalid OTP",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Invalid OTP code",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/webhook": {
      post: {
        summary: "Receive Xendit Webhook Event",
        description:
          "Endpoint for receiving webhook events from Xendit. When a payment event occurs, Xendit will POST a JSON payload to this endpoint. Optionally, the x-callback-token header can be used for verifying the request.",
        tags: ["Xendit"],
        parameters: [
          {
            name: "x-callback-token",
            in: "header",
            required: false,
            description: "Optional webhook token for verifying the request.",
            schema: { type: "string" },
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  event: {
                    type: "string",
                    description: "The webhook event type",
                    example: "payment_paid",
                  },
                  data: {
                    type: "object",
                    properties: {
                      id: {
                        type: "string",
                        description: "Payment identifier",
                        example: "p001",
                      },
                      amount: {
                        type: "number",
                        description: "The payment amount",
                        example: 100000,
                      },
                      status: {
                        type: "string",
                        description: "Payment status",
                        example: "PAID",
                      },
                    },
                  },
                },
              },
              example: {
                event: "payment_paid",
                data: {
                  id: "p001",
                  amount: 100000,
                  status: "PAID",
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Webhook processed successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad Request",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: { type: "string", example: "Invalid request" },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: {
                      type: "string",
                      example: "Failed to process webhook",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/customers": {
      get: {
        summary: "Get Customers List",
        description: "Retrieve a list of customers from Xendit",
        tags: ["Xendit"],
        parameters: [
          {
            name: "limit",
            in: "query",
            description: "Number of customers to retrieve (default: 10)",
            schema: { type: "integer", example: 10 },
          },
          {
            name: "after_id",
            in: "query",
            description: "Customer ID for pagination",
            schema: { type: "string" },
          },
        ],
        responses: {
          200: {
            description: "Customers retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        has_more: { type: "boolean" },
                        data: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              id: { type: "string" },
                              reference_id: { type: "string" },
                              email: { type: "string" },
                              given_names: { type: "string" },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/customers/{id}": {
      get: {
        summary: "Get Customer Details",
        description: "Retrieve details of a specific customer",
        tags: ["Xendit"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            description: "Customer ID",
            schema: { type: "string" },
          },
        ],
        responses: {
          200: {
            description: "Customer details retrieved successfully",
          },
        },
      },
      patch: {
        summary: "Update Customer",
        description: "Update customer information",
        tags: ["Xendit"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            description: "Customer ID",
            schema: { type: "string" },
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: { type: "string", format: "email" },
                  given_names: { type: "string" },
                  surname: { type: "string" },
                  mobile_number: { type: "string" },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Customer updated successfully",
          },
        },
      },
    },
    "/api/xendit/invoices": {
      get: {
        summary: "Get Invoices List",
        description: "Retrieve a list of invoices from Xendit",
        tags: ["Xendit"],
        parameters: [
          {
            name: "limit",
            in: "query",
            description: "Number of invoices to retrieve (default: 10)",
            schema: { type: "integer", example: 10 },
          },
          {
            name: "after_id",
            in: "query",
            description: "Invoice ID for pagination",
            schema: { type: "string" },
          },
          {
            name: "status",
            in: "query",
            description: "Filter by invoice status",
            schema: { type: "string", enum: ["PENDING", "PAID", "EXPIRED"] },
          },
        ],
        responses: {
          200: {
            description: "Invoices retrieved successfully",
          },
        },
      },
    },
    "/api/xendit/invoices/{id}": {
      get: {
        summary: "Get Invoice Details",
        description: "Retrieve details of a specific invoice",
        tags: ["Xendit"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            description: "Invoice ID",
            schema: { type: "string" },
          },
        ],
        responses: {
          200: {
            description: "Invoice details retrieved successfully",
          },
        },
      },
      patch: {
        summary: "Update Invoice",
        description: "Update invoice information",
        tags: ["Xendit"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            description: "Invoice ID",
            schema: { type: "string" },
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  description: { type: "string" },
                  amount: { type: "number" },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Invoice updated successfully",
          },
        },
      },
    },
    "/api/xendit/invoices/{id}/expire": {
      post: {
        summary: "Expire Invoice",
        description: "Manually expire an invoice",
        tags: ["Xendit"],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            description: "Invoice ID",
            schema: { type: "string" },
          },
        ],
        responses: {
          200: {
            description: "Invoice expired successfully",
          },
        },
      },
    },
    "/api/xendit/balance": {
      get: {
        summary: "Get Account Balance",
        description: "Retrieve current account balance from Xendit",
        tags: ["Xendit"],
        responses: {
          200: {
            description: "Balance retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        balance: { type: "number", example: 1000000 },
                        currency: { type: "string", example: "IDR" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/account": {
      get: {
        summary: "Get Account Information",
        description: "Retrieve account information from Xendit",
        tags: ["Xendit"],
        responses: {
          200: {
            description: "Account information retrieved successfully",
          },
        },
      },
    },
    "/api/xendit/transactions": {
      get: {
        summary: "Get Transactions List",
        description:
          "Retrieve a list of all transactions (Not yet implemented)",
        tags: ["Xendit"],
        responses: {
          501: {
            description: "Endpoint not yet implemented",
          },
        },
      },
    },
    "/api/xendit/purchases": {
      post: {
        summary: "Create Xendit Purchase",
        description:
          "Create a one-time purchase using Xendit payment gateway (similar to PayPal purchases)",
        tags: ["Xendit Purchases"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email", "tier", "amount"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Customer email address",
                    example: "<EMAIL>",
                  },
                  tier: {
                    type: "string",
                    description: "Subscription tier to purchase",
                    enum: ["starter", "basic", "pro", "enterprise"],
                    example: "pro",
                  },
                  amount: {
                    type: "number",
                    description: "Purchase amount in IDR",
                    example: 150000,
                  },
                  payment_method: {
                    type: "string",
                    description: "Payment method to use",
                    enum: ["QRIS", "VIRTUAL_ACCOUNT", "INVOICE"],
                    default: "QRIS",
                    example: "QRIS",
                  },
                  bank_code: {
                    type: "string",
                    description:
                      "Bank code for Virtual Account (required if payment_method is VIRTUAL_ACCOUNT)",
                    enum: ["BCA", "BNI", "BRI", "MANDIRI", "PERMATA", "BSI"],
                    example: "BCA",
                  },
                  addons: {
                    type: "object",
                    description: "Additional features",
                    properties: {
                      addon1: { type: "boolean", default: false },
                      addon2: { type: "boolean", default: false },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Purchase created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        external_id: {
                          type: "string",
                          example: "purchase_1642680000000_abc123",
                        },
                        payment_method: { type: "string", example: "QRIS" },
                        tier: { type: "string", example: "pro" },
                        amount: { type: "number", example: 150000 },
                        status: { type: "string", example: "PENDING" },
                        payment_id: { type: "string", example: "qr_123456789" },
                        qr_string: {
                          type: "string",
                          example: "00020101021226670016ID.CO.QRIS.WWW...",
                        },
                        qr_code_url: {
                          type: "string",
                          example:
                            "https://your-domain.com/api/xendit/qris/qr-code",
                        },
                        expires_at: { type: "string", format: "date-time" },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description:
              "Bad request - missing required fields or invalid payment method",
          },
          500: {
            description: "Internal server error",
          },
        },
      },
    },
    "/api/xendit/purchases/success": {
      get: {
        summary: "Handle Purchase Success",
        description: "Callback endpoint for successful Xendit purchases",
        tags: ["Xendit Purchases"],
        parameters: [
          {
            name: "external_id",
            in: "query",
            required: true,
            description: "Purchase external ID",
            schema: { type: "string" },
          },
        ],
        responses: {
          302: {
            description: "Redirect to frontend with success status",
          },
        },
      },
    },
    "/api/xendit/purchases/failed": {
      get: {
        summary: "Handle Purchase Failure",
        description: "Callback endpoint for failed Xendit purchases",
        tags: ["Xendit Purchases"],
        parameters: [
          {
            name: "external_id",
            in: "query",
            required: true,
            description: "Purchase external ID",
            schema: { type: "string" },
          },
        ],
        responses: {
          302: {
            description: "Redirect to frontend with failure status",
          },
        },
      },
    },
    "/api/xendit/purchases/status": {
      get: {
        summary: "Get Purchase Status",
        description: "Check the status of a Xendit purchase",
        tags: ["Xendit Purchases"],
        parameters: [
          {
            name: "external_id",
            in: "query",
            required: true,
            description: "Purchase external ID",
            schema: { type: "string" },
          },
        ],
        responses: {
          200: {
            description: "Purchase status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: {
                      type: "object",
                      properties: {
                        external_id: { type: "string" },
                        email: { type: "string" },
                        tier: { type: "string" },
                        amount: { type: "number" },
                        payment_method: { type: "string" },
                        status: {
                          type: "string",
                          enum: ["PENDING", "COMPLETED", "FAILED"],
                        },
                        created_at: { type: "string", format: "date-time" },
                        completed_at: { type: "string", format: "date-time" },
                      },
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Purchase not found",
          },
        },
      },
    },
    "/api/xendit/purchases/qr-code": {
      post: {
        summary: "Generate QR Code for Purchase",
        description: "Generate QR code for QRIS purchase",
        tags: ["Xendit Purchases"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["external_id"],
                properties: {
                  external_id: {
                    type: "string",
                    description: "Purchase external ID",
                    example: "purchase_1642680000000_abc123",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "QR code information retrieved successfully",
          },
          404: {
            description: "QRIS purchase not found",
          },
        },
      },
    },
    "/api/xendit/invoice": {
      post: {
        summary: "Create Xendit Invoice for Tier Upgrade",
        description:
          "Create a new invoice for upgrading the subscription tier using Xendit payment gateway",
        tags: ["Xendit Payments"],
        security: [
          {
            bearerAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["external_id", "amount", "payer_email"],
                properties: {
                  external_id: {
                    type: "string",
                    description: "Unique identifier for the invoice",
                    example: "invoice-123",
                  },
                  amount: {
                    type: "number",
                    description: "Amount to be paid in the specified currency",
                    example: 100000,
                  },
                  payer_email: {
                    type: "string",
                    format: "email",
                    description: "Email of the payer",
                    example: "<EMAIL>",
                  },
                  description: {
                    type: "string",
                    description: "Description of the payment",
                    example: "Upgrade to Premium Tier",
                  },
                  currency: {
                    type: "string",
                    description: "Payment currency",
                    example: "IDR",
                    default: "IDR",
                  },
                  payment_methods: {
                    type: "array",
                    items: {
                      type: "string",
                    },
                    description: "Allowed payment methods",
                    example: ["QRIS"],
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Invoice created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: true },
                    data: { type: "object" },
                  },
                },
              },
            },
          },
          401: {
            description: "Unauthorized - Invalid or missing bearer token",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: { type: "boolean", example: false },
                    error: {
                      type: "string",
                      example: "Unauthorized - Bearer token required",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/debug/check-password": {
      post: {
        summary: "Check User Password (Development Only)",
        description:
          "Development endpoint to check a user's password by email. WARNING: This endpoint should not be used in production!",
        tags: ["Debug"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["email"],
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email of the user to check",
                    example: "<EMAIL>",
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Password retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    email: {
                      type: "string",
                      example: "<EMAIL>",
                    },
                    password: {
                      type: "string",
                      example: "password123",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request - Email is required",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Email is required",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "User not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "User not found",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/dashboard": {
      post: {
        summary: "Get Dashboard Data by Email",
        description:
          "Retrieves user's dashboard data including account details, subscription status, usage metrics, and website information for all domains associated with the email",
        tags: ["Dashboard"],
        security: [
          {
            bearerAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address of the user",
                    example: "<EMAIL>",
                  },
                },
                required: ["email"],
              },
            },
          },
        },
        responses: {
          200: {
            description: "Successfully retrieved dashboard data",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        user: {
                          type: "object",
                          properties: {
                            accountDetails: {
                              type: "object",
                              properties: {
                                subscription: {
                                  type: "object",
                                  properties: {
                                    currentPlan: {
                                      type: "string",
                                      example: "STARTER",
                                    },
                                  },
                                },
                                limits: {
                                  type: "object",
                                  properties: {
                                    currentUsage: {
                                      type: "object",
                                      properties: {
                                        websites: {
                                          type: "integer",
                                          example: 0,
                                        },
                                      },
                                    },
                                    restrictions: {
                                      type: "object",
                                      properties: {
                                        websiteLimit: {
                                          type: "integer",
                                          example: 5,
                                        },
                                        creditLimit: {
                                          type: "integer",
                                          example: 100,
                                        },
                                      },
                                    },
                                  },
                                },
                              },
                            },
                            metrics: {
                              type: "object",
                              properties: {
                                totalCreditsUsed: {
                                  type: "integer",
                                  example: 0,
                                },
                                totalScans: {
                                  type: "integer",
                                  example: 0,
                                },
                              },
                            },
                            websites: {
                              type: "array",
                              items: {
                                type: "object",
                                properties: {
                                  domain: {
                                    type: "string",
                                    example: "example.com",
                                  },
                                  status: {
                                    type: "string",
                                    example: "active",
                                  },
                                  credits: {
                                    type: "integer",
                                    example: 10,
                                  },
                                  lastScan: {
                                    type: "string",
                                    format: "date-time",
                                    example: "2024-02-11T12:00:00Z",
                                  },
                                  key: {
                                    type: "string",
                                    example: "ws_123abc",
                                  },
                                  totalCreditsUsed: {
                                    type: "integer",
                                    example: 50,
                                  },
                                  totalScans: {
                                    type: "integer",
                                    example: 25,
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  error: {
                    type: "null",
                    example: null,
                  },
                },
              },
            },
          },
          400: {
            description: "Bad Request - Missing email",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    data: {
                      type: "null",
                      example: null,
                    },
                    error: {
                      type: "string",
                      example: "Email is required",
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Unauthorized - Invalid or missing token",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Invalid token or token expired",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Not Found - User not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    data: {
                      type: "null",
                      example: null,
                    },
                    error: {
                      type: "string",
                      example: "User not found",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    data: {
                      type: "null",
                      example: null,
                    },
                    error: {
                      type: "string",
                      example: "Failed to fetch dashboard data",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/activity": {
      post: {
        summary: "Get User Activity Data by Email",
        description:
          "Retrieves user's activity data including scan history, usage metrics, and account details for all domains associated with the email",
        tags: ["Dashboard"],
        security: [
          {
            bearerAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address of the user",
                    example: "<EMAIL>",
                  },
                },
                required: ["email"],
              },
            },
          },
        },
        responses: {
          200: {
            description: "Successfully retrieved activity data",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        user: {
                          type: "object",
                          properties: {
                            id: {
                              type: "string",
                              format: "uuid",
                              example: "21e188bd-f804-48d3-9727-440630af00ea",
                            },
                            email: {
                              type: "string",
                              format: "email",
                              example: "<EMAIL>",
                            },
                            websites: {
                              type: "array",
                              items: {
                                type: "object",
                                properties: {
                                  domain: {
                                    type: "string",
                                    example: "example.com",
                                  },
                                  scanHistory: {
                                    type: "array",
                                    items: {
                                      type: "object",
                                      properties: {
                                        id: {
                                          type: "string",
                                          example: "scan-123",
                                        },
                                        date: {
                                          type: "string",
                                          format: "date-time",
                                          example: "2025-02-21T17:00:00Z",
                                        },
                                        status: {
                                          type: "string",
                                          enum: ["completed", "failed"],
                                          example: "completed",
                                        },
                                      },
                                    },
                                  },
                                },
                              },
                            },
                            metrics: {
                              type: "object",
                              properties: {
                                totalScans: {
                                  type: "integer",
                                  example: 7,
                                },
                                lastScanTime: {
                                  type: "string",
                                  format: "date-time",
                                  example: "2025-02-21T18:00:00Z",
                                },
                              },
                            },
                            accountDetails: {
                              type: "object",
                              properties: {
                                limits: {
                                  type: "object",
                                  properties: {
                                    currentUsage: {
                                      type: "object",
                                      properties: {
                                        websites: {
                                          type: "integer",
                                          example: 2,
                                        },
                                      },
                                    },
                                    restrictions: {
                                      type: "object",
                                      properties: {
                                        websiteLimit: {
                                          type: "integer",
                                          example: 10,
                                        },
                                      },
                                    },
                                  },
                                },
                                subscription: {
                                  type: "object",
                                  properties: {
                                    currentPlan: {
                                      type: "string",
                                      example: "BASIC",
                                    },
                                    startDate: {
                                      type: "string",
                                      format: "date-time",
                                      example: "2024-02-21T00:00:00Z",
                                    },
                                    endDate: {
                                      type: "string",
                                      format: "date-time",
                                      example: "2025-02-21T00:00:00Z",
                                    },
                                    status: {
                                      type: "string",
                                      example: "active",
                                    },
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad Request - Missing email",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    data: {
                      type: "null",
                      example: null,
                    },
                    error: {
                      type: "string",
                      example: "Email is required",
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Unauthorized - Invalid or missing token",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Invalid token or token has expired",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Not Found - User not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    data: {
                      type: "null",
                      example: null,
                    },
                    error: {
                      type: "string",
                      example: "User not found",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    data: {
                      type: "null",
                      example: null,
                    },
                    error: {
                      type: "string",
                      example: "Failed to fetch activity data",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/usage/email": {
      post: {
        summary: "Get Usage History by Email",
        description:
          "Retrieves usage history for all domains associated with an email address",
        tags: ["Usage"],
        security: [
          {
            bearerAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    description: "Email address of the user",
                    example: "<EMAIL>",
                  },
                },
                required: ["email"],
              },
            },
          },
        },
        parameters: [
          {
            in: "query",
            name: "type",
            schema: {
              type: "string",
              enum: ["images", "content", "title"],
            },
            description: "Filter results by usage type",
            required: false,
          },
          {
            in: "query",
            name: "limit",
            schema: {
              type: "integer",
              default: 100,
              minimum: 1,
            },
            description: "Number of items per page",
            required: false,
          },
          {
            in: "query",
            name: "page",
            schema: {
              type: "integer",
              default: 1,
              minimum: 1,
            },
            description: "Page number",
            required: false,
          },
        ],
        responses: {
          200: {
            description: "Successful response with usage history",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        history: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              type: {
                                type: "string",
                                enum: ["images", "content", "title"],
                                example: "images",
                              },
                              source: {
                                type: "string",
                                example: "api",
                              },
                              timestamp: {
                                type: "string",
                                format: "date-time",
                                example: "2024-03-21T10:00:00Z",
                              },
                              metadata: {
                                type: "object",
                                example: {},
                              },
                              domain: {
                                type: "string",
                                example: "example.com",
                              },
                            },
                          },
                        },
                        pagination: {
                          type: "object",
                          properties: {
                            currentPage: {
                              type: "integer",
                              example: 1,
                            },
                            totalPages: {
                              type: "integer",
                              example: 5,
                            },
                            totalItems: {
                              type: "integer",
                              example: 450,
                            },
                            itemsPerPage: {
                              type: "integer",
                              example: 100,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad Request - Email is required",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Email is required",
                    },
                  },
                },
              },
            },
          },
          401: {
            description: "Unauthorized - Invalid or missing token",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example:
                        "Authorization header with Bearer token is required",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Not Found - User or domains not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "User or domains not found",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Internal server error",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    // Tambahkan setelah endpoint terakhir di dalam `paths`
    "/api/purchases": {
      post: {
        summary: "Create One-Time Purchase",
        description:
          "Create a one-time purchase order for a specified tier with optional add-ons using PayPal",
        tags: ["Purchases"],
        security: [
          {
            ApiKeyAuth: [],
          },
        ],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["tier", "email", "amount", "addons"],
                properties: {
                  tier: {
                    type: "string",
                    enum: ["basic", "enterprise"],
                    description: "The tier level to purchase",
                    example: "basic",
                  },
                  email: {
                    type: "string",
                    format: "email",
                    description: "User's email address",
                    example: "<EMAIL>",
                  },
                  amount: {
                    type: "number",
                    description:
                      "Total amount to be paid including tier price and addons",
                    example: 29.99,
                  },
                  addons: {
                    type: "object",
                    properties: {
                      addon1: {
                        type: "boolean",
                        description: "Enable addon1 features",
                        default: false,
                        example: false,
                      },
                      addon2: {
                        type: "boolean",
                        description: "Enable addon2 features",
                        default: false,
                        example: false,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: "Purchase order created successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        orderId: {
                          type: "string",
                          description: "PayPal order identifier",
                          example: "5O190127TN364715T",
                        },
                        approvalUrl: {
                          type: "string",
                          description: "PayPal approval URL for the order",
                          example:
                            "https://www.sandbox.paypal.com/checkoutnow?token=5O190127TN364715T",
                        },
                        tier: {
                          type: "string",
                          description: "Selected tier level",
                          example: "basic",
                        },
                        totalPrice: {
                          type: "number",
                          description: "Total price of the purchase",
                          example: 29.99,
                        },
                        status: {
                          type: "string",
                          description: "Initial status of the purchase",
                          example: "CREATED",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description:
              "Bad request - Invalid input or missing required fields",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Email is required",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    message: {
                      type: "string",
                      example: "Failed to create purchase",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/purchases/success": {
      get: {
        summary: "Handle Purchase Success Callback",
        description:
          "Handles the success callback from PayPal after a purchase is approved and redirects to the frontend dashboard",
        tags: ["Purchases"],
        parameters: [
          {
            name: "token",
            in: "query",
            required: true,
            schema: {
              type: "string",
            },
            description: "PayPal order token",
            example: "5O190127TN364715T",
          },
        ],
        responses: {
          302: {
            description:
              "Redirects to the frontend dashboard with purchase status",
            headers: {
              Location: {
                schema: {
                  type: "string",
                  example:
                    "http://localhost:3000/dashboard?purchase_status=success&order_id=5O190127TN364715T",
                },
              },
            },
          },
        },
      },
    },
    "/api/payment/store": {
      post: {
        summary: "Store Payment Data",
        description: "Store payment data in the system",
        tags: ["Payments"],
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                required: ["payment_id", "user_email", "payment_data"],
                properties: {
                  payment_id: {
                    type: "string",
                    description: "Unique payment identifier",
                    example: "invoice-1686123456789",
                  },
                  user_email: {
                    type: "string",
                    format: "email",
                    description: "User email associated with the payment",
                    example: "<EMAIL>",
                  },
                  payment_data: {
                    type: "object",
                    required: [
                      "external_id",
                      "amount",
                      "payer_email",
                      "description",
                      "currency",
                      "payment_methods",
                      "tier",
                      "status",
                      "created_at",
                    ],
                    properties: {
                      external_id: {
                        type: "string",
                        description: "External payment identifier",
                        example: "invoice-1686123456789",
                      },
                      amount: {
                        type: "number",
                        description: "Payment amount",
                        example: 300000,
                      },
                      payer_email: {
                        type: "string",
                        format: "email",
                        description: "Email of the payer",
                        example: "<EMAIL>",
                      },
                      description: {
                        type: "string",
                        description: "Payment description",
                        example: "Upgrade to Professional Plan",
                      },
                      currency: {
                        type: "string",
                        description: "Payment currency",
                        example: "IDR",
                      },
                      payment_methods: {
                        type: "array",
                        description: "Available payment methods",
                        items: {
                          type: "string",
                        },
                        example: ["QRIS"],
                      },
                      tier: {
                        type: "string",
                        description: "Subscription tier",
                        example: "professional",
                      },
                      status: {
                        type: "string",
                        description: "Payment status",
                        example: "PENDING",
                      },
                      created_at: {
                        type: "string",
                        format: "date-time",
                        description: "Payment creation timestamp",
                        example: "2023-06-07T08:30:56.789Z",
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          201: {
            description: "Payment data stored successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    message: {
                      type: "string",
                      example: "Payment data stored successfully",
                    },
                    payment_id: {
                      type: "string",
                      example: "invoice-1686123456789",
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request - missing required fields",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "payment_id, user_email, and payment_data are required",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to store payment data",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/payment/list_payment": {
      get: {
        summary: "List All Payments",
        description: "Retrieve a list of all stored payment data",
        tags: ["Payments"],
        responses: {
          200: {
            description: "List of payments retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          payment_id: {
                            type: "string",
                            example: "invoice-1686123456789",
                          },
                          user_email: {
                            type: "string",
                            format: "email",
                            example: "<EMAIL>",
                          },
                          payment_data: {
                            type: "object",
                            properties: {
                              external_id: {
                                type: "string",
                                example: "invoice-1686123456789",
                              },
                              amount: {
                                type: "number",
                                example: 300000,
                              },
                              payer_email: {
                                type: "string",
                                format: "email",
                                example: "<EMAIL>",
                              },
                              description: {
                                type: "string",
                                example: "Upgrade to Professional Plan",
                              },
                              currency: {
                                type: "string",
                                example: "IDR",
                              },
                              payment_methods: {
                                type: "array",
                                items: {
                                  type: "string",
                                },
                                example: ["QRIS"],
                              },
                              tier: {
                                type: "string",
                                example: "professional",
                              },
                              status: {
                                type: "string",
                                example: "PENDING",
                              },
                              created_at: {
                                type: "string",
                                format: "date-time",
                                example: "2023-06-07T08:30:56.789Z",
                              },
                            },
                          },
                        },
                      },
                    },
                    count: {
                      type: "integer",
                      example: 1,
                      description: "Total number of payments",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to list payments",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/payment/status": {
      get: {
        summary: "Get Payment Status",
        description: "Retrieve payment status and details by external_id",
        tags: ["Payments"],
        parameters: [
          {
            name: "external_id",
            in: "query",
            required: true,
            description: "External ID of the payment to retrieve",
            schema: {
              type: "string",
            },
            example: "invoice-1742054157160",
          },
        ],
        responses: {
          200: {
            description: "Payment details retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    payment_id: {
                      type: "string",
                      example: "invoice-1742054157160",
                    },
                    user_email: {
                      type: "string",
                      format: "email",
                      example: "<EMAIL>",
                    },
                    payment_data: {
                      type: "object",
                      properties: {
                        external_id: {
                          type: "string",
                          example: "invoice-1742054157160",
                        },
                        amount: {
                          type: "number",
                          example: 750000,
                        },
                        payer_email: {
                          type: "string",
                          format: "email",
                          example: "<EMAIL>",
                        },
                        description: {
                          type: "string",
                          example: "Upgrade to Enterprise Plan",
                        },
                        currency: {
                          type: "string",
                          example: "IDR",
                        },
                        payment_methods: {
                          type: "array",
                          items: {
                            type: "string",
                          },
                          example: ["QRIS"],
                        },
                        tier: {
                          type: "string",
                          example: "enterprise",
                        },
                        status: {
                          type: "string",
                          example: "PAID",
                        },
                        created_at: {
                          type: "string",
                          format: "date-time",
                          example: "2025-03-15T15:55:57.160Z",
                        },
                        payment_id: {
                          type: "string",
                          example: "67d5a30d10b21173ca6886d3",
                        },
                        paid_at: {
                          type: "string",
                          format: "date-time",
                          example: "2025-03-15T15:56:01.109Z",
                        },
                        payment_method: {
                          type: "string",
                          example: "QR_CODE",
                        },
                        payment_channel: {
                          type: "string",
                          example: "QRIS",
                        },
                        payment_details: {
                          type: "object",
                          properties: {
                            receipt_id: {
                              type: "string",
                              example: "",
                            },
                            source: {
                              type: "string",
                              example: "DANA",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request - missing required parameter",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "external_id parameter is required",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Payment not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "Payment with the specified external_id not found",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to get payment status",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/payment-status": {
      get: {
        tags: ["Xendit Payment"],
        summary:
          "Check payment status by invoice_id, external_id, or reference_id",
        description:
          "Check the status of a Xendit payment using various identifiers. Supports multiple search methods with fallback options.",
        parameters: [
          {
            name: "invoice_id",
            in: "query",
            description: "Xendit invoice ID (e.g., 67d53ae210b21173ca67d492)",
            required: false,
            schema: {
              type: "string",
              example: "67d53ae210b21173ca67d492",
            },
          },
          {
            name: "external_id",
            in: "query",
            description:
              "External ID / Reference ID sent to Xendit (e.g., sub_1752937775866_ac51okhb2)",
            required: false,
            schema: {
              type: "string",
              example: "sub_1752937775866_ac51okhb2",
            },
          },
          {
            name: "reference_id",
            in: "query",
            description: "Reference ID (alias for external_id)",
            required: false,
            schema: {
              type: "string",
              example: "invoice-1742027490163",
            },
          },
        ],
        responses: {
          200: {
            description: "Payment status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        invoice_id: {
                          type: "string",
                          description: "Xendit invoice ID",
                          example: "67d53ae210b21173ca67d492",
                        },
                        external_id: {
                          type: "string",
                          description: "External/Reference ID",
                          example: "sub_1752937775866_ac51okhb2",
                        },
                        status: {
                          type: "string",
                          description: "Payment status",
                          enum: [
                            "PENDING",
                            "PAID",
                            "SETTLED",
                            "EXPIRED",
                            "FAILED",
                          ],
                          example: "PAID",
                        },
                        paid_at: {
                          type: "string",
                          format: "date-time",
                          description: "Payment completion timestamp",
                          example: "2025-01-12T10:30:00Z",
                        },
                        paid_amount: {
                          type: "number",
                          description: "Amount paid in IDR",
                          example: 150000,
                        },
                        is_paid: {
                          type: "boolean",
                          description: "Whether payment is completed",
                          example: true,
                        },
                        search_method: {
                          type: "string",
                          description: "Method used to find the payment",
                          enum: [
                            "direct_invoice_lookup",
                            "stored_data_lookup",
                            "xendit_api_search",
                          ],
                          example: "stored_data_lookup",
                        },
                        source: {
                          type: "string",
                          description: "Data source (only for stored data)",
                          enum: [
                            "stored_data",
                            "purchase_data",
                            "subscription_data",
                          ],
                          example: "purchase_data",
                        },
                        subscription_info: {
                          type: "object",
                          description:
                            "Subscription details (only for subscription_data source)",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: "Bad request - missing required parameters",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "invoice_id, external_id, or reference_id is required",
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Payment not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "Payment not found. Searched using: stored_data_lookup. Try using the actual invoice_id from Xendit instead of external_id/reference_id.",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to check payment status",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/api/xendit/payment-status/{external_id}": {
      get: {
        tags: ["Xendit Payment"],
        summary: "Check payment status by external_id (path parameter)",
        description:
          "Alternative endpoint to check payment status using external_id as path parameter",
        parameters: [
          {
            name: "external_id",
            in: "path",
            description: "External ID / Reference ID sent to Xendit",
            required: true,
            schema: {
              type: "string",
              example: "sub_1752937775866_ac51okhb2",
            },
          },
        ],
        responses: {
          200: {
            description: "Payment status retrieved successfully",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: true,
                    },
                    data: {
                      type: "object",
                      properties: {
                        invoice_id: {
                          type: "string",
                          description: "Xendit invoice ID",
                          example: "67d53ae210b21173ca67d492",
                        },
                        external_id: {
                          type: "string",
                          description: "External/Reference ID",
                          example: "sub_1752937775866_ac51okhb2",
                        },
                        status: {
                          type: "string",
                          description: "Payment status",
                          enum: [
                            "PENDING",
                            "PAID",
                            "SETTLED",
                            "EXPIRED",
                            "FAILED",
                          ],
                          example: "PAID",
                        },
                        paid_at: {
                          type: "string",
                          format: "date-time",
                          description: "Payment completion timestamp",
                          example: "2025-01-12T10:30:00Z",
                        },
                        paid_amount: {
                          type: "number",
                          description: "Amount paid in IDR",
                          example: 150000,
                        },
                        is_paid: {
                          type: "boolean",
                          description: "Whether payment is completed",
                          example: true,
                        },
                        search_method: {
                          type: "string",
                          description: "Method used to find the payment",
                          enum: [
                            "direct_invoice_lookup",
                            "stored_data_lookup",
                            "xendit_api_search",
                          ],
                          example: "stored_data_lookup",
                        },
                        source: {
                          type: "string",
                          description: "Data source (only for stored data)",
                          enum: [
                            "stored_data",
                            "purchase_data",
                            "subscription_data",
                          ],
                          example: "purchase_data",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          404: {
            description: "Payment not found",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example:
                        "Payment not found. Searched using: stored_data_lookup.",
                    },
                  },
                },
              },
            },
          },
          500: {
            description: "Internal server error",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    success: {
                      type: "boolean",
                      example: false,
                    },
                    error: {
                      type: "string",
                      example: "Failed to check payment status",
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    schemas: {
      UserResponse: {
        type: "object",
        properties: {
          id: { type: "string" },
          username: { type: "string" },
          email: { type: "string" },
          api_key: { type: "string" },
        },
      },
      TierConfig: {
        type: "object",
        properties: {
          name: { type: "string" },
          maxQuota: { type: "number" },
          price: { type: "number" },
          features: {
            type: "array",
            items: { type: "string" },
          },
        },
      },
      TierStatusResponse: {
        type: "object",
        properties: {
          success: { type: "boolean" },
          data: {
            type: "object",
            properties: {
              currentTier: { type: "string" },
              tierName: { type: "string" },
              usage: { type: "number" },
              imagesUsage: { type: "number" },
              contentUsage: { type: "number" },
              titleUsage: { type: "number" },
              maxQuota: { type: "number" },
              imagesQuota: { type: "number" },
              contentQuota: { type: "number" },
              titleQuota: { type: "number" },
              remainingImagesQuota: { type: "number" },
              remainingContentQuota: { type: "number" },
              remainingTitleQuota: { type: "number" },
              price: { type: "number" },
              features: {
                type: "array",
                items: { type: "string" },
              },
              quotaPercentage: { type: "string" },
            },
          },
        },
      },
      UsageHistoryEntry: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          type: { type: "string", enum: ["images", "content"] },
          source: { type: "string" },
          timestamp: { type: "string", format: "date-time" },
          metadata: {
            type: "object",
            additionalProperties: true,
            example: {
              size: "1024x1024",
              model: "stable-diffusion",
            },
          },
        },
      },
      DailyUsage: {
        type: "object",
        properties: {
          date: { type: "string", format: "date" },
          images: { type: "integer" },
          content: { type: "integer" },
          title: { type: "integer" },
          details: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "string", format: "uuid" },
                type: { type: "string", enum: ["images", "content", "title"] },
                source: { type: "string" },
                time: { type: "string", format: "date-time" },
              },
            },
          },
        },
        example: {
          date: "2024-12-08",
          images: 15,
          content: 8,
          title: 12,
          details: [
            {
              id: "123e4567-e89b-12d3-a456-************",
              type: "title",
              source: "product-title",
              time: "2024-12-08T02:30:00.000Z",
            },
          ],
        },
      },
      PayPalSubscription: {
        type: "object",
        properties: {
          subscriptionId: {
            type: "string",
            description: "PayPal subscription identifier",
          },
          status: {
            type: "string",
            enum: [
              "APPROVAL_PENDING",
              "APPROVED",
              "ACTIVE",
              "SUSPENDED",
              "CANCELLED",
              "EXPIRED",
            ],
          },
          tier: {
            type: "string",
            enum: ["basic", "enterprise"],
          },
          price: {
            type: "number",
            description: "Monthly subscription price in USD",
          },
          createdAt: {
            type: "string",
            format: "date-time",
          },
          nextBillingDate: {
            type: "string",
            format: "date-time",
          },
        },
      },
      Addons: {
        type: "object",
        properties: {
          addon1: {
            type: "boolean",
            description: "Enhanced features add-on",
          },
          addon2: {
            type: "boolean",
            description: "Premium support add-on",
          },
        },
      },
      User: {
        type: "object",
        properties: {
          id: {
            type: "string",
            example: "user123",
          },
          email: {
            type: "string",
            format: "email",
            example: "<EMAIL>",
          },
          tier: {
            type: "string",
            example: "pro",
          },
          createdAt: {
            type: "string",
            format: "date-time",
          },
        },
      },
      ErrorResponse: {
        type: "object",
        properties: {
          success: {
            type: "boolean",
            example: false,
          },
          message: {
            type: "string",
            example: "Error message",
          },
          timestamp: {
            type: "string",
            format: "date-time",
            example: "2024-01-01T00:00:00.000Z",
          },
        },
        required: ["success", "message", "timestamp"],
      },
    },
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "x-sps-key",
        description: "API key for authentication",
      },
      DomainAuth: {
        type: "apiKey",
        in: "header",
        name: "x-sps-domain",
        description: "Domain associated with the API key",
      },
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "Enter your JWT token",
      },
    },
  },

};

// Function to serve Swagger UI HTML
export function getSwaggerHTML(swaggerSpec) {
  return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>API Documentation</title>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui.min.css" />
      </head>
      <body>
          <div id="swagger-ui"></div>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.11.0/swagger-ui-bundle.min.js"></script>
          <script>
              window.onload = () => {
                  window.ui = SwaggerUIBundle({
                      spec: ${JSON.stringify(swaggerSpec)},
                      dom_id: '#swagger-ui',
                      deepLinking: true,
                      presets: [
                          SwaggerUIBundle.presets.apis,
                          SwaggerUIBundle.SwaggerUIStandalonePreset
                      ],
                  });
              };
          </script>
      </body>
      </html>
    `;
}
