import { Router } from "itty-router";
import { PaymentController } from "../controllers/paymentController";

export function createPaymentRouter(env) {
  const router = Router({ base: "/api/payment" });
  const controller = new PaymentController(env);

  // Store payment data
  router.post("/store", (request) => controller.storePayment(request));

  // List all payments
  router.get("/list_payment", (request) => controller.listPayments(request));

  // Get payment status by external_id
  router.get("/status", (request) => controller.getPaymentStatus(request));

  // Add error handling for unmatched routes
  router.all("*", () => {
    console.log("Payment Router: Route not found");
    return new Response(
      JSON.stringify({
        success: false,
        error: "Endpoint not found",
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" },
      }
    );
  });

  return router;
}
