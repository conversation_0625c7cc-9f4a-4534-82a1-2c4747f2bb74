import { Router } from "itty-router";
import { XenditController } from "../controllers/xenditController";
import { XenditPurchaseController } from "../controllers/xenditPurchaseController.js";
import { SubscriptionController } from "../controllers/subscriptionController.js";
import { WebhookController } from "../controllers/webhookController.js";
import { TokenService } from "../services/tokenService.js";

export function createXenditRouter(env) {
  const router = Router({ base: "/api/xendit" });
  const controller = new XenditController(env);
  const purchaseController = new XenditPurchaseController(env);
  const subscriptionController = new SubscriptionController(env);
  const webhookController = new WebhookController(env);

  // Token validation middleware
  async function validateToken(request) {
    const authHeader = request.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Unauthorized - Bearer token required",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const token = authHeader.split(" ")[1];
    if (!token) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Unauthorized - Invalid token format",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    try {
      const tokenService = new TokenService(env);
      try {
        // First, try to verify as a standard user token
        await tokenService.verifyToken(token);
        return null; // Standard user token is valid
      } catch (userTokenError) {
        // If it fails, try to verify as a portal user token
        const portalTokenResult = await tokenService.verifyPortalToken(token);
        if (portalTokenResult.success) {
          return null; // Portal user token is valid
        }
        // If both fail, throw the original error for the standard token
        throw userTokenError;
      }
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Unauthorized - Invalid or expired token",
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  // Customer Management - Protected
  router.post("/customer", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    console.log("Xendit Router: Handling customer creation request");
    return controller.createCustomer(request);
  });
  router.get("/customers", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getCustomers(request);
  });
  router.get("/customers/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getCustomer(request);
  });
  router.patch("/customers/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.updateCustomer(request);
  });
  
  // Invoice Management - Protected
  router.post("/invoice", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.createInvoice(request);
  });
  router.get("/invoices", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getInvoices(request);
  });
  router.get("/invoices/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getInvoice(request);
  });
  router.patch("/invoices/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.updateInvoice(request);
  });
  router.post("/invoices/:id/expire", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.expireInvoice(request);
  });
  
  // QRIS Management - Protected
  router.post("/qris", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.createQRISPayment(request);
  });
  router.get("/qris", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getQRISPayments(request);
  });
  router.get("/qris/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getQRISPayment(request);
  });
  router.post("/qris/qr-code", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.generateQRCode(request);
  });
  
  // Virtual Account Management - Protected
  router.post("/virtual-account", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.createVirtualAccount(request);
  });
  router.get("/virtual-accounts", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getVirtualAccounts(request);
  });
  router.get("/virtual-accounts/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getVirtualAccount(request);
  });
  router.patch("/virtual-accounts/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.updateVirtualAccount(request);
  });
  
  // Payment Methods - Protected
  router.get("/payment-methods", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getPaymentMethods(request);
  });
  router.get("/payment-methods/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getPaymentMethod(request);
  });
  router.post("/payment-methods", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.createPaymentMethod(request);
  });
  router.post("/payment-methods/:payment_method_id/auth", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.validateDirectDebitOTP(request);
  });
  
  // Payment Requests - Protected
  router.post("/payment-request", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.createPaymentRequest(request);
  });
  router.get("/payment-requests", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getPaymentRequests(request);
  });
  router.get("/payment-requests/:id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getPaymentRequest(request);
  });
  
  // Balance & Account - Protected
  router.get("/balance", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getBalance(request);
  });
  router.get("/account", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getAccount(request);
  });
  
  // Transactions & Reports - Protected
  router.get("/transactions", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.getTransactions(request);
  });
  
  // Webhooks
  router.post("/webhook", async (request) => controller.handleWebhook(request));
  router.post("/payment-webhook", async (request) =>
    controller.handleWebhook(request)
  );
  
  // Payment Status - Protected
  router.get("/payment-status", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return controller.checkPaymentStatus(request);
  });

  // Alternative endpoint for checking by external_id/reference_id - Protected
  router.get("/payment-status/:external_id", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const externalId = pathParts[pathParts.length - 1];
    
    // Create a new request with external_id as query parameter
    const newUrl = new URL(url);
    newUrl.searchParams.set("external_id", externalId);
    newUrl.pathname = "/api/xendit/payment-status";
    
    const newRequest = new Request(newUrl, {
      method: request.method,
      headers: request.headers,
    });
    
    return controller.checkPaymentStatus(newRequest);
  });

  // Webhook Forwarding Logs - Protected
  router.get("/webhook-forwarding-logs", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    try {
      const url = new URL(request.url);
      const latest = url.searchParams.get("latest");
      const date = url.searchParams.get("date");
      const download = url.searchParams.get("download");
      
      if (latest === "true") {
        const latestLog = await env.USERS_KV.get('webhook_forwarding_latest');
        
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              latest_forwarding_log: latestLog ? JSON.parse(latestLog) : "No forwarding logs found"
            }
          }),
          {
            status: 200,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      
      if (date) {
        const logFileName = `xendit-webhook-forwarding-${date}.log`;
        const logContent = await env.USERS_KV.get(`webhook_forwarding_log:${logFileName}`);
        
        if (!logContent) {
          return new Response(
            JSON.stringify({
              success: false,
              error: `No forwarding log found for date: ${date}`
            }),
            {
              status: 404,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        
        if (download === "true") {
          return new Response(logContent, {
            status: 200,
            headers: {
              "Content-Type": "text/plain",
              "Content-Disposition": `attachment; filename="${logFileName}"`,
            },
          });
        }
        
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              filename: logFileName,
              content: logContent,
              lines: logContent.split('\n').length - 1
            }
          }),
          {
            status: 200,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      
      // List all forwarding log files
      const logFilesResult = await env.USERS_KV.list({
        prefix: "webhook_forwarding_log:",
        limit: 100
      });
      
      const logFiles = logFilesResult.keys.map(key => ({
        filename: key.name.replace('webhook_forwarding_log:', ''),
        key: key.name,
        modified: key.metadata?.modified || 'Unknown'
      }));
      
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            forwarding_log_files: logFiles,
            total_files: logFiles.length
          }
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Webhook Logs (for debugging) - Protected
  router.get("/webhook-logs", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    try {
      const url = new URL(request.url);
      const latest = url.searchParams.get("latest");
      const date = url.searchParams.get("date"); // YYYY-MM-DD format
      const download = url.searchParams.get("download"); // true/false
      
      if (latest === "true") {
        // Get latest webhook log
        const latestLog = await env.USERS_KV.get('xendit_webhook_latest');
        const latestControllerLog = await env.USERS_KV.get('xendit_webhook_controller_latest');
        
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              latest_router_log: latestLog || "No webhook logs found",
              latest_controller_log: latestControllerLog || "No controller logs found"
            }
          }),
          {
            status: 200,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      
      // Get log file by date
      if (date) {
        const logFileName = `xendit-webhook-${date}.log`;
        const logContent = await env.USERS_KV.get(`webhook_log_file:${logFileName}`);
        
        if (!logContent) {
          return new Response(
            JSON.stringify({
              success: false,
              error: `No webhook log found for date: ${date}`
            }),
            {
              status: 404,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        
        // Return as downloadable file if requested
        if (download === "true") {
          return new Response(logContent, {
            status: 200,
            headers: {
              "Content-Type": "text/plain",
              "Content-Disposition": `attachment; filename="${logFileName}"`,
            },
          });
        }
        
        return new Response(
          JSON.stringify({
            success: true,
            data: {
              filename: logFileName,
              content: logContent,
              lines: logContent.split('\n').length - 1
            }
          }),
          {
            status: 200,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      
      // Get all available log files
      const logFilesResult = await env.USERS_KV.list({
        prefix: "webhook_log_file:",
        limit: 100
      });
      
      const logFiles = logFilesResult.keys.map(key => ({
        filename: key.name.replace('webhook_log_file:', ''),
        key: key.name,
        modified: key.metadata?.modified || 'Unknown'
      }));
      
      // List all webhook log keys
      const list = await env.USERS_KV.list({ prefix: "xendit_webhook_log:" });
      const controllerList = await env.USERS_KV.list({ prefix: "xendit_webhook_controller_log:" });
      
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            log_files: logFiles,
            router_logs: list.keys.map(key => key.name),
            controller_logs: controllerList.keys.map(key => key.name),
            total_log_files: logFiles.length,
            total_router_logs: list.keys.length,
            total_controller_logs: controllerList.keys.length
          }
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Test webhook logging manually - Protected
  router.post("/webhook-test", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    try {
      console.log("=== MANUAL WEBHOOK TEST ===");
      const payload = await request.json();
      
      console.log("Test payload received:", JSON.stringify(payload, null, 2));
      
      // Test logging function
      try {
        await logWebhookPayload(payload, env, request);
        console.log("✅ Test webhook payload logged successfully");
        
        // Verify it was stored
        const latestLog = await env.USERS_KV.get('xendit_webhook_latest');
        
        return new Response(
          JSON.stringify({
            success: true,
            message: "Test webhook logged successfully",
            log_stored: !!latestLog,
            log_preview: latestLog ? latestLog.substring(0, 200) + "..." : "No log found"
          }),
          {
            status: 200,
            headers: { "Content-Type": "application/json" },
          }
        );
      } catch (logError) {
        console.error("❌ Error in test logging:", logError);
        return new Response(
          JSON.stringify({
            success: false,
            error: "Logging failed",
            details: logError.message
          }),
          {
            status: 500,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
    } catch (error) {
      console.error("❌ Test webhook error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Get specific webhook log - Protected
  router.get("/webhook-logs/:logKey", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    try {
      const url = new URL(request.url);
      const pathParts = url.pathname.split("/");
      const logKey = pathParts[pathParts.length - 1];
      
      const log = await env.USERS_KV.get(logKey);
      
      if (!log) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Log not found"
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
      
      return new Response(log, {
        status: 200,
        headers: { "Content-Type": "text/plain" },
      });
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Xendit Purchase Routes - Protected
  router.post("/purchases", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return purchaseController.createPurchase(request);
  });

  router.get("/purchases/success", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return purchaseController.handlePurchaseSuccess(request);
  });

  router.get("/purchases/failed", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return purchaseController.handlePurchaseFailed(request);
  });

  router.get("/purchases/status", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return purchaseController.getPurchaseStatus(request);
  });

  router.post("/purchases/qr-code", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return purchaseController.generateQRCode(request);
  });

  // Xendit Subscription Routes - Protected
  router.post("/subscriptions", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return subscriptionController.createXenditSubscription(request);
  });

  router.get("/subscriptions/success", async (request) => {
    const tokenError = await validateToken(request);
    if (tokenError) return tokenError;
    return subscriptionController.handleXenditSuccess(request);
  });

  // Xendit Webhook Routes (moved from webhookRoutes.js)
  router.post("/webhooks", async (request) => {
    try {
      console.log("=== WEBHOOK RECEIVED ===");
      const payload = await request.json();
      const { external_id, status } = payload;
      
      console.log("Payload received:", JSON.stringify(payload, null, 2));
      console.log("External ID:", external_id);
      console.log("Status:", status);

      // Simple test - store directly to KV
      try {
        console.log("🔄 Testing direct KV storage...");
        const timestamp = new Date().toISOString();
        const simpleLog = `WEBHOOK TEST LOG - ${timestamp}\nPayload: ${JSON.stringify(payload, null, 2)}`;
        
        await env.USERS_KV.put('xendit_webhook_latest', simpleLog);
        console.log("✅ Direct KV storage successful");
        
        // Also try with timestamp key
        await env.USERS_KV.put(`xendit_webhook_log:${timestamp}`, simpleLog);
        console.log("✅ Timestamped log stored");
        
      } catch (kvError) {
        console.error("❌ KV storage error:", kvError);
        console.error("❌ KV error stack:", kvError.stack);
      }

      if (status === "PAID") {
        // Get subscription data from KV
        const subscriptionData = await env.USERS_KV.get(
          `xendit_ref:${external_id}`,
          "json"
        );

        console.log("Looking for subscription data with key:", `xendit_ref:${external_id}`);
        
        if (subscriptionData) {
          console.log("Found subscription data:", JSON.stringify(subscriptionData, null, 2));

          if (subscriptionData.status === 'processed') {
            console.log(`Webhook for ${external_id} already processed. Skipping tier upgrade.`);
            return new Response(JSON.stringify({ success: true, message: 'Already processed' }), {
              status: 200,
              headers: { "Content-Type": "application/json" },
            });
          }

          // Get the email to use for tier upgrade
          const userEmail = subscriptionData.email || subscriptionData.userId;
          
          if (!userEmail) {
            console.error("No email found in subscription data:", subscriptionData);
            return new Response(
              JSON.stringify({ 
                success: false, 
                error: "No email found in subscription data" 
              }),
              {
                status: 400,
                headers: { "Content-Type": "application/json" },
              }
            );
          }
          
          console.log("Upgrading tier for email:", userEmail);
          
          // Upgrade the user's tier using upgradeEmailTier
          const tierService = subscriptionController.tierService;
          await tierService.upgradeEmailTier(
            userEmail,
            subscriptionData.tier,
            {
              subscriptionId: external_id,
              addon1: subscriptionData.addons?.addon1 || false,
              addon2: subscriptionData.addons?.addon2 || false,
            }
          );
          
          console.log("✅ Tier upgrade completed successfully for:", userEmail);

          // Send webhook notification to frontend
          await sendWebhookToFrontend(payload, env);

          // Mark as processed instead of deleting
          console.log("Marking subscription as processed.");
          subscriptionData.status = 'processed';
          subscriptionData.processedAt = new Date().toISOString();
          await env.USERS_KV.put(
            `xendit_ref:${external_id}`,
            JSON.stringify(subscriptionData),
            { expirationTtl: 86400 } // Keep record for 24 hours
          );
        } else {
          console.error("❌ No subscription data found for external_id:", external_id);
          console.log("This could mean:");
          console.log("1. Data was not stored during subscription creation");
          console.log("2. External ID mismatch between frontend and webhook");
          console.log("3. Data was already processed and deleted");
          
          // Try to find any xendit_ref keys for debugging
          const xenditRefKeys = await env.USERS_KV.list({ prefix: "xendit_ref:" });
          console.log("Available xendit_ref keys:", xenditRefKeys.keys.map(k => k.name));
        }
      }

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Xendit webhook error:", error);
      return new Response(
        JSON.stringify({ success: false, error: error.message }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Helper function to log webhook payload to file
  async function logWebhookPayload(payload, env, request) {
    try {
      console.log("📝 Logging webhook payload...");
      const timestamp = new Date().toISOString();
      
      // Safely get headers
      const headers = {};
      try {
        headers['user-agent'] = request?.headers?.get('user-agent') || 'N/A';
        headers['x-callback-token'] = request?.headers?.get('x-callback-token') ? 'PRESENT' : 'MISSING';
        headers['content-type'] = request?.headers?.get('content-type') || 'N/A';
        headers['x-forwarded-for'] = request?.headers?.get('x-forwarded-for') || 'N/A';
        headers['cf-connecting-ip'] = request?.headers?.get('cf-connecting-ip') || 'N/A';
      } catch (headerError) {
        console.error("❌ Error reading headers:", headerError);
        headers['error'] = 'Failed to read headers';
      }
      
      const logEntry = {
        timestamp,
        payload,
        headers
      };

      const logText = `
=== XENDIT WEBHOOK LOG ===
Timestamp: ${timestamp}
External ID: ${payload.external_id || 'N/A'}
Status: ${payload.status || 'N/A'}
Amount: ${payload.amount || 'N/A'}
Payer Email: ${payload.payer_email || 'N/A'}
Payment Method: ${payload.payment_method || 'N/A'}
Payment Channel: ${payload.payment_channel || 'N/A'}

Full Payload:
${JSON.stringify(payload, null, 2)}

Headers:
${JSON.stringify(logEntry.headers, null, 2)}

========================

`;

      // Store in KV with timestamp key for easy retrieval
      const logKey = `xendit_webhook_log:${timestamp}`;
      console.log(`💾 Storing log with key: ${logKey}`);
      
      await env.USERS_KV.put(logKey, logText);
      console.log(`✅ Log stored with key: ${logKey}`);

      // Also store latest webhook for quick access
      await env.USERS_KV.put('xendit_webhook_latest', logText);
      console.log(`✅ Latest log updated`);

      console.log(`✅ Xendit webhook payload logged with key: ${logKey}`);
      console.log(`✅ Latest log stored as: xendit_webhook_latest`);
    } catch (error) {
      console.error('❌ Error logging webhook payload:', error.message);
      console.error('❌ Error stack:', error.stack);
      // Don't throw error to avoid breaking webhook flow
    }
  }

  // Helper function to send webhook data to frontend
  async function sendWebhookToFrontend(webhookData, env) {
    try {
      const frontendUrl = env.FRONTEND_URL || "http://localhost:3001";
      const webhookEndpoint = `${frontendUrl}/api/xendit/webhook-listener`;
      
      console.log(`Sending webhook notification to frontend: ${webhookEndpoint}`);
      
      const response = await fetch(webhookEndpoint, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'User-Agent': 'Backend-Webhook-Forwarder/1.0'
        },
        body: JSON.stringify({
          ...webhookData,
          timestamp: new Date().toISOString(),
          source: 'backend-webhook-handler'
        })
      });

      if (response.ok) {
        console.log('Successfully sent webhook notification to frontend');
      } else {
        console.error(`Failed to send webhook to frontend: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error sending webhook to frontend:', error.message);
      // Don't throw error here to avoid breaking the main webhook flow
    }
  }

  // Add error handling for unmatched routes
  router.all("*", () => {
    console.log("Xendit Router: Route not found");
    return new Response(
      JSON.stringify({
        success: false,
        error: "Endpoint not found",
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" },
      }
    );
  });

  return router;
}
