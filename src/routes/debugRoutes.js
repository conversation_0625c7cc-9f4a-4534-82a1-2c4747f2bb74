// src/routes/debugRoutes.js
import { Router } from "itty-router";
import { EmailQueueService as EmailQueueServiceD1 } from "../services/emailQueueServiceD1";
import { DebugController } from "../controllers/debugController";

export function createDebugRouter(env) {
  const router = Router({ base: "/api/debug" });
  const emailQueueService = new EmailQueueServiceD1(env);
  const controller = new DebugController(env);

  router.post("/email-queue/cleanup", async () => {
    try {
      const results = await emailQueueService.cleanupQueue();
      const newStatus = await emailQueueService.getQueueStatus();

      return new Response(
        JSON.stringify({
          success: true,
          message: "Queue cleanup completed",
          results,
          currentStatus: newStatus,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Get queue status with detailed information
  router.get("/email-queue", async () => {
    try {
      const status = await emailQueueService.getQueueStatus();
      return new Response(
        JSON.stringify({
          success: true,
          data: status,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  router.get("/cron-debug", async (request) => {
    const emailQueueService = new EmailQueueServiceD1(env);
    const debugInfo = await emailQueueService.getDebugInfo();

    return new Response(
      JSON.stringify({
        success: true,
        data: debugInfo,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  });

  // Process queue with results
  router.post("/process-queue", async () => {
    try {
      const results = await emailQueueService.processQueue();
      const newStatus = await emailQueueService.getQueueStatus();

      return new Response(
        JSON.stringify({
          success: true,
          message: "Queue processing completed",
          results,
          currentStatus: newStatus,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Clear queue
  router.delete("/email-queue", async () => {
    try {
      const clearedCount = await emailQueueService.clearQueue();
      return new Response(
        JSON.stringify({
          success: true,
          message: `Cleared ${clearedCount} items from queue`,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  router.get("/email-queue/status", async (request, env) => {
    try {
      const emailQueueService = new EmailQueueServiceD1(env);
      const status = await emailQueueService.getQueueStatus();

      return new Response(
        JSON.stringify({
          success: true,
          data: status,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      console.error("Error getting queue status:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Add debug endpoint for environment variables
  router.get("/email-queue/debug", async (request, env) => {
    try {
      const emailQueueService = new EmailQueueServiceD1(env);
      const debugInfo = await emailQueueService.getDebugInfo();

      return new Response(
        JSON.stringify({
          success: true,
          data: debugInfo,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // List all KV data
  router.get("/kv/list", (request) => controller.listAllKVData(request));

  // Get specific KV entry
  router.get("/kv/:key", (request) => controller.getKVEntry(request));

  // Delete KV entry
  router.delete("/kv/:key", (request) => controller.deleteKVEntry(request));

  // Search KV entries
  router.get("/kv/search", (request) => controller.searchKVEntries(request));

  // Truncate KV data (delete all except tier settings)
  router.post("/kv/truncate", (request) => controller.truncateKVData(request));

  // Check truncate status
  router.get("/kv/truncate/status", (request) =>
    controller.checkTruncateStatus(request)
  );

  router.post("/check-password", (request) =>
    controller.checkUserPassword(request)
  );

  // Add endpoint to monitor email queue and trigger processing if needed
  router.get("/email-monitor", async () => {
    try {
      const emailQueueService = new EmailQueueServiceD1(env);
      const status = await emailQueueService.getQueueStatus();
      const debugInfo = await emailQueueService.getDebugInfo();

      // If there are pending emails, process them
      let processResults = null;
      if (status.pending > 0) {
        console.log(`📧 Found ${status.pending} pending emails, processing...`);
        processResults = await emailQueueService.processQueue(true);
      }

      return new Response(
        JSON.stringify({
          success: true,
          queueStatus: status,
          debugInfo: debugInfo,
          processResults: processResults,
          timestamp: new Date().toISOString(),
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  return router;
}
