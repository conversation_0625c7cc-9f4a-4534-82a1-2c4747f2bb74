import { Router } from "itty-router";
import { WebhookController } from "../controllers/webhookController.js";

export function createWebhookRouter(env) {
  const router = Router({ base: "/api/webhooks" });
  const controller = new WebhookController(env);

  // Note: PayPal webhooks moved to /api/paypal/webhooks
  // Note: Xendit webhooks moved to /api/xendit/webhooks

  // Webhook status endpoint (for testing)
  router.get("/status", (request) => controller.getWebhookStatus(request));

  // Debug endpoint for webhook events (development only)
  router.get("/events", (request) => controller.getRecentEvents(request));

  // Generic webhook endpoint for other payment providers (if needed in future)
  router.post("/generic", async (request) => {
    try {
      const payload = await request.json();
      console.log("Generic webhook received:", payload);
      
      return new Response(JSON.stringify({ 
        success: true, 
        message: "Generic webhook received" 
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Generic webhook error:", error);
      return new Response(
        JSON.stringify({ success: false, error: error.message }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  });

  // Add error handling for unmatched routes
  router.all("*", () => {
    console.log("Webhook Router: Route not found");
    return new Response(
      JSON.stringify({
        success: false,
        error: "Webhook endpoint not found. Use /api/paypal/webhooks or /api/xendit/webhooks for payment-specific webhooks.",
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" },
      }
    );
  });

  return router;
}
