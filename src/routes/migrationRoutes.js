// src/routes/migrationRoutes.js
// Routes for handling KV to D1 migration

import { Router } from "itty-router";
import { runMigration } from "../../migrate-kv-to-d1.js";
import { DatabaseService } from "../services/databaseService.js";

export function createMigrationRouter(env) {
  const router = Router({ base: "/api/migration" });

  // Run migration from KV to D1
  router.post("/run", async (request) => {
    try {
      console.log("🚀 Starting KV to D1 migration...");
      
      await runMigration(env);
      
      return new Response(
        JSON.stringify({
          success: true,
          message: "Migration completed successfully",
          timestamp: new Date().toISOString()
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Migration error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });

  // Check migration status
  router.get("/status", async (request) => {
    try {
      const db = new DatabaseService(env);
      
      // Count records in D1
      const userCount = await db.db.prepare("SELECT COUNT(*) as count FROM users").first();
      const domainCount = await db.db.prepare("SELECT COUNT(*) as count FROM domains").first();
      const subscriptionCount = await db.db.prepare("SELECT COUNT(*) as count FROM subscriptions").first();
      const emailQueueCount = await db.db.prepare("SELECT COUNT(*) as count FROM email_queue").first();
      
      // Count records in KV
      const kvUserKeys = await env.USERS_KV.list({ prefix: "user:" });
      const kvEmailKeys = await env.USERS_KV.list({ prefix: "email:" });
      const kvDomainKeys = await env.USERS_KV.list({ prefix: "domain:" });
      
      return new Response(
        JSON.stringify({
          success: true,
          d1_records: {
            users: userCount.count,
            domains: domainCount.count,
            subscriptions: subscriptionCount.count,
            email_queue: emailQueueCount.count
          },
          kv_records: {
            users: kvUserKeys.keys.length,
            emails: kvEmailKeys.keys.length,
            domains: kvDomainKeys.keys.length
          },
          migration_recommended: userCount.count === 0 && kvUserKeys.keys.length > 0,
          timestamp: new Date().toISOString()
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Migration status error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });

  // Test D1 connection
  router.get("/test-d1", async (request) => {
    try {
      const db = new DatabaseService(env);
      
      // Test basic D1 operations
      const result = await db.db.prepare("SELECT 1 as test").first();
      
      return new Response(
        JSON.stringify({
          success: true,
          message: "D1 connection successful",
          test_result: result,
          timestamp: new Date().toISOString()
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("D1 test error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });

  // Initialize D1 schema
  router.post("/init-schema", async (request) => {
    try {
      const db = new DatabaseService(env);
      
      // Test if tables exist
      const tables = await db.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all();
      
      return new Response(
        JSON.stringify({
          success: true,
          message: "Schema check completed",
          tables: tables.results.map(t => t.name),
          timestamp: new Date().toISOString()
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Schema init error:", error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  });

  return router;
}