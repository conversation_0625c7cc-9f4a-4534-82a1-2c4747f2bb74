import { Router } from "itty-router";
import { DashboardController } from "../controllers/dashboardController.js";

export function createDashboardRouter(env) {
  const router = Router({ base: "/api" });
  const controller = new DashboardController(env);

  // supply data untuk halaman /dashboard di frontend
  router.post("/dashboard", (request) =>
    controller.getDashboardDataByEmail(request)
  );

  return router;
}
