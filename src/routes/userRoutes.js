import { Router } from "itty-router";
import { UserController } from "../controllers/userController";
import { DebugController } from "../controllers/debugController";
import { ApiUsageController } from "../controllers/apiUsageController";

export function createUserRouter(env) {
  const router = Router({ base: "/api/users" });
  const userController = new UserController(env);
  const debugController = new DebugController(env);
  const apiUsageController = new ApiUsageController(env);

  router.post("/validatekey", (request) =>
    userController.validateApiKey(request)
  );

  // Login route
  router.post("/login", (request) => userController.login(request));

  // activate api key
  router.post("/activate", (request) => userController.activate(request));
  router.post("/license/status", (request) =>
    userController.checkLicenseStatus(request)
  );

  // Existing routes
  router.get("/userdetail", (request) => userController.getUserDetail(request));
  router.post("/", (request) => userController.createUser(request));

  // Domain management routes
  router.post("/domains", (request) => userController.getDomains(request));
  router.delete("/domains", (request) => userController.removeDomain(request));

  router.post("/usage/track", (request) =>
    apiUsageController.trackUsage(request)
  );
  router.get("/usage", (request) => apiUsageController.getUserUsage(request));

  // Debug routes
  router.get("/debug/kv", (request) => debugController.listAllKVData(request));
  router.post("/debug/kv/delete", (request) =>
    debugController.deleteKVData(request)
  );

  router.get("/verifytoken", (request) =>
    userController.verifyTokenEndpoint(request)
  );

  return router;
}
