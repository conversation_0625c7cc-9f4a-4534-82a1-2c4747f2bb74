import { Router } from "itty-router";
import { UsageController } from "../controllers/usageController";

export function createUsageRouter(env) {
  const router = Router({ base: "/api/usage" });
  const controller = new UsageController(env);

  router.post("/images", (request) => controller.trackImageUsage(request));
  router.post("/content", (request) => controller.trackContentUsage(request));
  router.post("/title", (request) => controller.trackTitleUsage(request));
  router.get("/stats", (request) => controller.getUsageStats(request));
  router.get("/history", (request) => controller.getUsageHistory(request));
  router.get("/daily", (request) => controller.getDailyUsage(request));
  router.post("/email", (request) => controller.getEmailUsageHistory(request));
  return router;
}
