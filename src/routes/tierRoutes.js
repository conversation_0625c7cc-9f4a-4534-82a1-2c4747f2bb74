// src/routes/tierRoutes.js
import { Router } from "itty-router";
import { TierController } from "../controllers/tierController";
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";

export function createTierRouter(env) {
  const router = Router({ base: "/api/tiers" });
  const tierController = new TierController(env);

  // Public endpoint to view tier settings
  router.get("/settings", (request) => tierController.getTierSettings(request));

  // Protected endpoints requiring API key
  router.get("/status", (request) => tierController.getUserTierStatus(request));

  router.post("/upgrade", (request) => tierController.upgradeTier(request));

  router.get("/quota", (request) => tierController.getQuotaUsage(request));

  router.post("/quota/reset", (request) =>
    tierController.resetQuotaUsage(request)
  );

  router.post("/quota/zero", (request) =>
    tierController.setQuotaToZero(request)
  );

  // Internal cron endpoint - no authentication required
  router.post("/internal/cron/check-quota", (request) =>
    tierController.checkQuotaCron(request)
  );

  router.post("/settings/force-update", async (request) => {
    const tierService = new TierServiceD1(env);
    // Force reinitialize tier settings in D1
    await tierService.updateTierSettings(TierServiceD1.DEFAULT_TIERS);
    const settings = await tierService.getTierSettings();
    return new Response(JSON.stringify({ success: true, data: settings }), {
      headers: { "Content-Type": "application/json" },
    });
  });

  return router;
}
