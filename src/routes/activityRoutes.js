import { Router } from "itty-router";
import { ActivityController } from "../controllers/activityController.js";

export function createActivityRouter(env) {
  const router = Router({ base: "/api" }); // Set base path to /api
  const controller = new ActivityController(env);

  // Mount activity routes
  router.post("/activity", (request) =>
    controller.getActivityDataByEmail(request)
  );

  return router;
}
