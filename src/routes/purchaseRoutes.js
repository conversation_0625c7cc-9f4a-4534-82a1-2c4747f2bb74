import { Router } from "itty-router";
import { PurchaseController } from "../controllers/purchaseController.js";

export function createPurchaseRouter(env) {
  const router = Router({ base: "/api/purchases" });
  const controller = new PurchaseController(env);

  // Routes are relative to base path
  router.post("/", (request) => controller.createPurchase(request));

  router.get("/success", (request) =>
    controller.handlePurchaseSuccess(request)
  );

  // Add 404 handler
  router.all("*", () => {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Endpoint not found",
      }),
      {
        status: 404,
        headers: { "Content-Type": "application/json" },
      }
    );
  });

  return router;
}
