export class CronHandler {
  constructor(env) {
    this.env = env;
    this.emailQueueService = new EmailQueueServiceD1(env);
  }

  async processCron(event) {
    console.log("🔄 Cron triggered at:", new Date().toISOString());
    console.log("Event details:", {
      cron: event.cron,
      scheduledTime: event.scheduledTime,
    });

    try {
      // Get current queue status before processing
      const beforeStatus = await this.emailQueueService.getQueueStats();
      console.log("📊 Queue status before processing:", beforeStatus);

      // Process the queue with detailed logging
      const results = await this._processQueueWithLogging();

      // Get status after processing
      const afterStatus = await this.emailQueueService.getQueueStats();
      console.log("📊 Queue status after processing:", afterStatus);

      // Store run results
      await this._storeRunResults({
        beforeStatus,
        afterStatus,
        results,
        timestamp: new Date().toISOString(),
      });

      return {
        success: true,
        processed: results.processed,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ Cron processing error:", {
        message: error.message,
        stack: error.stack,
      });

      // Store error details
      await this._storeError(error);

      throw error;
    }
  }

  async _processQueueWithLogging() {
    console.log("🔍 Starting queue processing...");

    // Get pending emails
    const pendingEmails = await this._getPendingEmails();
    console.log(`📧 Found ${pendingEmails.length} pending emails`);

    if (pendingEmails.length === 0) {
      console.log("ℹ️ No pending emails to process");
      return { processed: 0, succeeded: 0, failed: 0 };
    }

    // Process each email with detailed logging
    const results = {
      processed: 0,
      succeeded: 0,
      failed: 0,
      errors: [],
    };

    for (const email of pendingEmails) {
      try {
        console.log(`📤 Processing email ${email.id} for ${email.email}`);

        // Attempt to send email
        const sendResult = await this.emailQueueService.processEmail(email);

        console.log(`✉️ Email processing result:`, {
          id: email.id,
          success: sendResult.success,
          attempts: email.attempts || 0,
        });

        if (sendResult.success) {
          results.succeeded++;
        } else {
          results.failed++;
          results.errors.push({
            id: email.id,
            error: sendResult.error,
          });
        }

        results.processed++;
      } catch (error) {
        console.error(`❌ Error processing email ${email.id}:`, {
          error: error.message,
          stack: error.stack,
        });

        results.failed++;
        results.errors.push({
          id: email.id,
          error: error.message,
        });
      }
    }

    console.log("✅ Queue processing completed:", results);
    return results;
  }

  async _getPendingEmails() {
    const { keys } = await this.env.USERS_KV.list({
      prefix: "email_queue:pending:",
    });

    const emails = [];
    for (const key of keys) {
      const email = await this.env.USERS_KV.get(key.name, "json");
      if (email) emails.push(email);
    }

    return emails;
  }

  async _storeRunResults(data) {
    const key = `cron_run:${new Date().toISOString()}`;
    await this.env.USERS_KV.put(key, JSON.stringify(data));
  }

  async _storeError(error) {
    const key = `cron_error:${new Date().toISOString()}`;
    await this.env.USERS_KV.put(
      key,
      JSON.stringify({
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      })
    );
  }
}
