// src/services/emailQueueService.js
import { EmailService } from "./emailService";

export class EmailQueueService {
  constructor(env) {
    this.env = env;
    this.emailService = new EmailService(env);
  }

  async addToQueue(emailData) {
    try {
      const id = crypto.randomUUID();
      const queueItem = {
        id,
        ...emailData,
        status: "pending",
        attempts: 0,
        queuedAt: new Date().toISOString(),
        processAfter: new Date().toISOString(), // Immediate processing for first attempt
      };

      await this.env.USERS_KV.put(
        `email_queue:${id}`,
        JSON.stringify(queueItem)
      );

      console.log("Email queued:", {
        id,
        email: emailData.email,
        status: "pending",
      });

      // Trigger immediate processing
      await this.processQueue();

      return id;
    } catch (error) {
      console.error("Error adding to queue:", error);
      throw error;
    }
  }

  async processQueue() {
    try {
      const { keys } = await this.env.USERS_KV.list({
        prefix: "email_queue:",
      });

      console.log(`Processing ${keys.length} emails in queue`);

      for (const key of keys) {
        const queueItem = await this.env.USERS_KV.get(key.name, "json");

        if (!queueItem) continue;

        // Check if it's time to process this item
        if (new Date(queueItem.processAfter) > new Date()) {
          console.log(`Skipping email ${queueItem.id} - scheduled for later`);
          continue;
        }

        console.log(`Processing email for ${queueItem.email}`);

        try {
          const result = await this.emailService.sendApiKeyEmail(
            queueItem.email,
            queueItem.username,
            queueItem.apiKey,
            queueItem.password,
            queueItem.domain
          );

          if (result.success) {
            // On success, remove from queue and store success record
            await Promise.all([
              this.env.USERS_KV.delete(key.name),
              this.env.USERS_KV.put(
                `email_sent:${queueItem.id}`,
                JSON.stringify({
                  ...queueItem,
                  status: "sent",
                  sentAt: new Date().toISOString(),
                })
              ),
            ]);

            console.log(`Successfully sent email to ${queueItem.email}`);
          } else {
            throw new Error(result.error || "Failed to send email");
          }
        } catch (error) {
          console.error(`Error sending email to ${queueItem.email}:`, error);

          const attempts = (queueItem.attempts || 0) + 1;

          if (attempts >= 3) {
            // Move to failed after 3 attempts
            await Promise.all([
              this.env.USERS_KV.delete(key.name),
              this.env.USERS_KV.put(
                `email_failed:${queueItem.id}`,
                JSON.stringify({
                  ...queueItem,
                  status: "failed",
                  error: error.message,
                  failedAt: new Date().toISOString(),
                  attempts,
                })
              ),
            ]);

            console.log(
              `Email to ${queueItem.email} failed permanently after ${attempts} attempts`
            );
          } else {
            // Schedule retry with exponential backoff
            const delayMinutes = Math.pow(2, attempts - 1) * 1; // 1, 2, 4 minutes
            const processAfter = new Date(
              Date.now() + delayMinutes * 60 * 1000
            ).toISOString();

            await this.env.USERS_KV.put(
              key.name,
              JSON.stringify({
                ...queueItem,
                attempts,
                processAfter,
                lastError: error.message,
                lastAttempt: new Date().toISOString(),
              })
            );

            console.log(
              `Scheduled retry #${attempts} for ${queueItem.email} after ${delayMinutes} minutes`
            );
          }
        }
      }
    } catch (error) {
      console.error("Error processing queue:", error);
    }
  }

  async getQueueStatus() {
    const [queue, sent, failed] = await Promise.all([
      this.env.USERS_KV.list({ prefix: "email_queue:" }),
      this.env.USERS_KV.list({ prefix: "email_sent:" }),
      this.env.USERS_KV.list({ prefix: "email_failed:" }),
    ]);

    return {
      pending: queue.keys.length,
      sent: sent.keys.length,
      failed: failed.keys.length,
    };
  }
}
