// Complete User Cleanup Script
// This script handles both D1 and KV storage cleanup
// Run with: node complete-user-cleanup.js <<EMAIL>>

import { DatabaseService } from './src/services/databaseService.js';
import { PortalUserService } from './src/services/portalUserServiceD1.js';

class UserCleanupService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
    this.portalUserService = new PortalUserService(env);
  }

  async deleteUserCompletely(email) {
    console.log(`🗑️ Starting complete user deletion for: ${email}`);
    
    try {
      // Step 1: Delete from D1 database
      await this.deleteFromD1(email);
      
      // Step 2: Delete from KV storage (portal users)
      await this.deleteFromKV(email);
      
      // Step 3: Clean up any remaining KV entries
      await this.cleanupKVEntries(email);
      
      console.log(`✅ Complete user deletion successful for: ${email}`);
      return { success: true, email, message: 'User completely deleted from all storage systems' };
      
    } catch (error) {
      console.error(`❌ Error during complete user deletion:`, error);
      throw error;
    }
  }

  async deleteFromD1(email) {
    console.log(`🗄️ Deleting user from D1 database: ${email}`);
    
    try {
      // First, get user details
      const user = await this.db.getUserByEmail(email);
      if (!user) {
        console.log(`ℹ️ No user found in D1 database with email: ${email}`);
        return;
      }
      
      console.log(`📋 Found user in D1: ${user.id} (${user.email})`);
      
      // Get related data counts for logging
      const domains = await this.db.getDomainsByUserId(user.id);
      console.log(`📊 User has ${domains.length} domains`);
      
      // Delete user (CASCADE will handle related data)
      const deleteResult = await this.db.deleteUser(user.id);
      if (deleteResult) {
        console.log(`✅ User deleted from D1 database: ${user.id}`);
      } else {
        throw new Error('Failed to delete user from D1 database');
      }
      
      // Clean up email_tiers (no foreign key constraint)
      await this.cleanupEmailTiers(email);
      
    } catch (error) {
      console.error(`❌ Error deleting from D1:`, error);
      throw error;
    }
  }

  async deleteFromKV(email) {
    console.log(`🗂️ Deleting user from KV storage: ${email}`);
    
    try {
      // Try to delete portal user
      const result = await this.portalUserService.deletePortalUserByEmail(email);
      console.log(`✅ Portal user deleted from KV:`, result);
    } catch (error) {
      if (error.message.includes('not found')) {
        console.log(`ℹ️ No portal user found in KV storage with email: ${email}`);
      } else {
        console.error(`❌ Error deleting portal user from KV:`, error);
        throw error;
      }
    }
  }

  async cleanupKVEntries(email) {
    console.log(`🧹 Cleaning up additional KV entries for: ${email}`);
    
    try {
      const kv = this.env.USERS_KV;
      
      // Clean up any remaining email-based entries
      const emailKey = `email:${email}`;
      await kv.delete(emailKey);
      console.log(`🗑️ Deleted KV entry: ${emailKey}`);
      
      // Clean up email tier entries
      const emailTierKey = `t_setting:email_tier:${email}`;
      await kv.delete(emailTierKey);
      console.log(`🗑️ Deleted KV entry: ${emailTierKey}`);
      
      // List and clean up any other email-related entries
      const emailRelatedKeys = await kv.list({ prefix: `email:${email}` });
      for (const key of emailRelatedKeys.keys) {
        await kv.delete(key.name);
        console.log(`🗑️ Deleted KV entry: ${key.name}`);
      }
      
    } catch (error) {
      console.error(`❌ Error cleaning up KV entries:`, error);
      // Don't throw here as this is cleanup
    }
  }

  async cleanupEmailTiers(email) {
    console.log(`🧹 Cleaning up email tiers for: ${email}`);
    
    try {
      // Clean up from D1 email_tiers table
      const result = await this.env.DB.prepare(`
        DELETE FROM email_tiers WHERE email = ?
      `).bind(email).run();
      
      if (result.success) {
        console.log(`✅ Cleaned up email tiers for: ${email}`);
      }
    } catch (error) {
      console.error(`❌ Error cleaning up email tiers:`, error);
      // Don't throw as this is cleanup
    }
  }

  async verifyDeletion(email) {
    console.log(`🔍 Verifying complete deletion for: ${email}`);
    
    try {
      // Check D1
      const d1User = await this.db.getUserByEmail(email);
      console.log(`D1 user check: ${d1User ? 'STILL EXISTS' : 'DELETED'}`);
      
      // Check KV
      const kvUser = await this.portalUserService.getPortalUserByEmail(email);
      console.log(`KV user check: ${kvUser ? 'STILL EXISTS' : 'DELETED'}`);
      
      // Check email entries
      const emailEntry = await this.env.USERS_KV.get(`email:${email}`);
      console.log(`Email entry check: ${emailEntry ? 'STILL EXISTS' : 'DELETED'}`);
      
      const allDeleted = !d1User && !kvUser && !emailEntry;
      console.log(`🎯 Complete deletion status: ${allDeleted ? 'SUCCESS' : 'INCOMPLETE'}`);
      
      return allDeleted;
      
    } catch (error) {
      console.error(`❌ Error verifying deletion:`, error);
      return false;
    }
  }
}

// CLI usage
async function main() {
  const email = process.argv[2];
  
  if (!email) {
    console.error('❌ Error: Email address is required');
    console.log('Usage: node complete-user-cleanup.js <<EMAIL>>');
    process.exit(1);
  }
  
  // Validate email format
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  if (!emailRegex.test(email)) {
    console.error(`❌ Error: Invalid email format: ${email}`);
    process.exit(1);
  }
  
  // Mock environment for testing
  // In production, you would get this from your actual environment
  const env = {
    DB: null, // You'll need to set up your D1 database connection
    USERS_KV: null, // You'll need to set up your KV namespace connection
  };
  
  console.log('⚠️ WARNING: This script requires proper environment setup');
  console.log('🔧 Please configure the env object with your actual D1 and KV connections');
  console.log(`📧 Target email: ${email}`);
  
  // Uncomment and configure when ready to use:
  /*
  try {
    const cleanup = new UserCleanupService(env);
    await cleanup.deleteUserCompletely(email);
    await cleanup.verifyDeletion(email);
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
  */
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { UserCleanupService };
