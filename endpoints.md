# API Endpoints Documentation

This document provides a comprehensive overview of all API endpoints in this Cloudflare Workers subscription-based service with payment processing, user management, and usage tracking.

## Table of Contents

- [User Management](#-user-management-endpoints)
- [Subscription Management](#-subscription-management)
- [Tier Management](#-tier-management)
- [Payment Processing](#-payment-processing)
- [Xendit Payment Gateway](#-xendit-payment-gateway)
- [Usage Tracking](#-usage-tracking)
- [Webhooks](#-webhooks)
- [Dashboard & Analytics](#-dashboard--analytics)
- [Debug & Testing](#-debug--testing)
- [Documentation](#-documentation)

---

## 🔐 User Management Endpoints

**Base Path:** `/api/users`

### 1. Create User

**POST** `/api/users/`

Creates a new user account with domain registration.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "domain": "example.com",
  "password": "password123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User created successfully. Please check your email for credentials.",
  "data": {
    "userId": "user-id",
    "email": "<EMAIL>",
    "domain": "example.com"
  }
}
```

### 2. User Login

**POST** `/api/users/login`

Authenticates user and returns JWT token.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "email": "<EMAIL>",
    "token": "jwt-token-here"
  }
}
```

### 3. Activate API Key

**POST** `/api/users/activate`

Activates an API key for a specific domain.

**Request Body:**

```json
{
  "license": "api-key-here",
  "domain": "example.com"
}
```

**Response:**

```json
{
  "status": true,
  "message": "Success activate api key",
  "domain": "example.com",
  "email": "<EMAIL>"
}
```

### 4. Validate API Key

**POST** `/api/users/validatekey`

Validates an API key.

**Headers:**

- `x-sps-key: your-api-key`

**Response:**

```json
{
  "success": true,
  "data": {
    "isValid": true
  }
}
```

### 5. Check License Status

**POST** `/api/users/license/status`

Checks the status of a license key.

**Request Body:**

```json
{
  "license": "api-key-here"
}
```

### 6. Get User Domains

**POST** `/api/users/domains`

Retrieves all domains associated with a user.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

### 7. Remove Domain

**DELETE** `/api/users/domains`

Removes a domain from user's account.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "domain": "example.com"
}
```

### 8. Get User Details

**GET** `/api/users/userdetail`

Gets user details by API key.

**Headers:**

- `x-sps-key: your-api-key`

### 9. Verify Token

**GET** `/api/users/verifytoken`

Verifies JWT token validity.

**Headers:**

- `Authorization: Bearer jwt-token`

---

## 💳 PayPal Payment Integration

**Base Path:** `/api/paypal`

### Subscriptions

#### 1. Create PayPal Subscription

**POST** `/api/paypal/subscriptions`

Creates a new PayPal subscription with billing agreement. Email is extracted from the bearer token.

**Headers:**

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**

```json
{
  "tier": "pro",
  "addons": {
    "addon1": false,
    "addon2": true
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "subscriptionId": "sub-*********",
    "approvalUrl": "https://paypal.com/approve/...",
    "tier": "pro",
    "basePrice": 40,
    "addons": {
      "addon1": false,
      "addon2": true
    },
    "totalPrice": 50,
    "status": "pending"
  }
}
```

#### 2. PayPal Subscription Success

**GET** `/api/paypal/subscriptions/success`

Handles successful PayPal subscription approval.

**Query Parameters:**

- `ba_token`: Billing agreement token from PayPal

#### 3. PayPal Subscription Cancel

**GET** `/api/paypal/subscriptions/cancel`

Handles PayPal subscription cancellation.

#### 4. Update Subscription Addons

**PATCH** `/api/paypal/subscriptions/:subscriptionId/addons`

Updates subscription addon configuration.

**Headers:**

```
Authorization: Bearer <jwt_token>
```

#### 5. Get Subscription Status

**GET** `/api/paypal/subscriptions/:subscriptionId/status`

Retrieves current subscription status.

**Headers:**

```
Authorization: Bearer <jwt_token>
```

### Purchases

#### 1. Create One-Time Purchase

**POST** `/api/paypal/purchases`

Creates a PayPal one-time purchase order. Email is extracted from the bearer token.

**Headers:**

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**

```json
{
  "tier": "pro",
  "amount": 99.99,
  "addons": {
    "addon1": false,
    "addon2": true
  }
}
```

#### 2. Purchase Success Callback

**GET** `/api/paypal/purchases/success`

Handles successful PayPal purchase completion.

### Webhooks

#### PayPal Webhook Handler

**POST** `/api/paypal/webhooks`

Receives and processes PayPal webhook notifications.

---

## 🏦 Xendit Payment Integration (Enhanced)

**Base Path:** `/api/xendit`

### Subscriptions

#### 1. Create Xendit Subscription

**POST** `/api/xendit/subscriptions`

Creates a new Xendit subscription.

**Request Body:**

```json
{
  "userId": "<EMAIL>",
  "tier": "basic",
  "addons": {
    "addon1": true,
    "addon2": false
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "invoiceUrl": "https://checkout.xendit.co/...",
    "referenceId": "sub_*********",
    "amount": 150000,
    "status": "pending"
  }
}
```

#### 2. Xendit Success Callback

**GET** `/api/xendit/subscriptions/success`

Handles successful Xendit payment.

### Webhooks

#### Xendit Webhook Handler

**POST** `/api/xendit/webhooks`

Receives and processes Xendit webhook notifications.

---

## 💰 Legacy Subscription Management (Deprecated)

**Base Path:** `/api/subscriptions` - ⚠️ **Use payment-specific endpoints instead**

_These endpoints are deprecated. Use `/api/paypal/subscriptions` or `/api/xendit/subscriptions` instead._

**Query Parameters:**

- `ref`: Reference ID from Xendit

### 6. Update Subscription Addons

**PATCH** `/api/subscriptions/:subscriptionId/addons`

Updates subscription addon options.

**Headers:**

- `x-sps-key: your-api-key`

### 7. Get Subscription Status

**GET** `/api/subscriptions/:subscriptionId/status`

Gets current subscription status.

---

## 🏆 Tier Management

**Base Path:** `/api/tiers`

### 1. Get Tier Settings (Public)

**GET** `/api/tiers/settings`

Retrieves available tier configurations.

**Response:**

```json
{
  "success": true,
  "data": {
    "config": {
      "starter": {
        "price": 0,
        "quota": 100,
        "addon1_price": 5,
        "addon2_price": 10
      },
      "basic": {
        "price": 15,
        "quota": 1000,
        "addon1_price": 5,
        "addon2_price": 10
      },
      "pro": {
        "price": 40,
        "quota": 5000,
        "addon1_price": 10,
        "addon2_price": 15
      }
    }
  }
}
```

### 2. Get User Tier Status

**GET** `/api/tiers/status`

Gets current user's tier status.

**Headers:**

- `x-sps-key: your-api-key`

### 3. Upgrade Tier

**POST** `/api/tiers/upgrade`

Upgrades user to a higher tier.

**Headers:**

- `x-sps-key: your-api-key`

### 4. Get Quota Usage

**GET** `/api/tiers/quota`

Gets current quota usage.

**Headers:**

- `x-sps-key: your-api-key`

### 5. Reset Quota Usage

**POST** `/api/tiers/quota/reset`

Resets user's quota usage.

**Headers:**

- `x-sps-key: your-api-key`

### 6. Set Quota to Zero

**POST** `/api/tiers/quota/zero`

Sets user's quota to zero.

**Headers:**

- `x-sps-key: your-api-key`

### 7. Check Quota Cron (Internal)

**POST** `/api/tiers/internal/cron/check-quota`

Internal endpoint for cron quota checking.

---

## 💳 Payment Processing

**Base Path:** `/api/payment`

### 1. Store Payment Data

**POST** `/api/payment/store`

Stores payment information before processing.

**Request Body:**

```json
{
  "external_id": "payment-123",
  "user_email": "<EMAIL>",
  "payment_data": {
    "tier": "pro",
    "amount": 400000,
    "status": "pending",
    "currency": "IDR"
  }
}
```

### 2. List All Payments

**GET** `/api/payment/list_payment`

Retrieves all payment records.

### 3. Get Payment Status

**GET** `/api/payment/status`

Gets payment status by external_id.

**Query Parameters:**

- `external_id`: Payment external ID

---

## 🇮🇩 Xendit Payment Gateway

**Base Path:** `/api/xendit`

### 1. Create Invoice

**POST** `/api/xendit/invoice`

Creates a Xendit invoice for payment.

**Headers:**

- `Authorization: Bearer token`

**Request Body:**

```json
{
  "external_id": "inv-123",
  "amount": 100000,
  "payer_email": "<EMAIL>",
  "description": "Upgrade to Pro Plan",
  "currency": "IDR",
  "payment_methods": ["QRIS", "BANK_TRANSFER"],
  "should_send_email": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "xendit-invoice-id",
    "external_id": "inv-123",
    "invoice_url": "https://checkout.xendit.co/...",
    "amount": 100000,
    "status": "PENDING"
  }
}
```

### 2. Create Customer

**POST** `/api/xendit/customer`

Creates a customer in Xendit system.

**Request Body:**

```json
{
  "reference_id": "cust-123",
  "email": "<EMAIL>",
  "given_names": "John",
  "surname": "Doe",
  "mobile_number": "+************",
  "type": "INDIVIDUAL",
  "addresses": [
    {
      "country": "ID",
      "street_line1": "Jl. Sudirman No. 1",
      "city": "Jakarta",
      "postal_code": "12190"
    }
  ]
}
```

### 3. Create QRIS Payment

**POST** `/api/xendit/qris`

Creates a QRIS payment method.

**Request Body:**

```json
{
  "external_id": "qris-123",
  "amount": 50000,
  "description": "Payment for services"
}
```

### 4. Create Virtual Account

**POST** `/api/xendit/virtual-account`

Creates a virtual account for bank transfer.

**Request Body:**

```json
{
  "external_id": "va-123",
  "bank_code": "BCA",
  "name": "John Doe",
  "amount": 100000,
  "is_closed": true,
  "description": "Payment for subscription"
}
```

### 5. Create Payment Request

**POST** `/api/xendit/payment-request`

Creates a payment request with specific payment method.

**Request Body:**

```json
{
  "amount": 100000,
  "currency": "IDR",
  "payment_method": {
    "type": "QRIS"
  },
  "customer_id": "cust-123",
  "description": "Subscription payment",
  "reference_id": "ref-123"
}
```

### 6. Generate QR Code

**POST** `/api/xendit/qris/qr-code`

Generates QR code image from QRIS string.

**Request Body:**

```json
{
  "qr_string": "qris-string-here"
}
```

**Response:** PNG image

### 7. Validate Direct Debit OTP

**POST** `/api/xendit/payment-methods/:payment_method_id/auth`

Validates OTP for direct debit payment.

**Request Body:**

```json
{
  "otp_code": "123456"
}
```

### 8. Xendit Webhook Handler

**POST** `/api/xendit/webhook`

Handles Xendit payment webhooks.

### 9. Check Payment Status

**GET** `/api/xendit/payment-status`

Checks payment status by invoice ID.

**Query Parameters:**

- `invoice_id`: Xendit invoice ID

---

## 📊 Usage Tracking

**Base Path:** `/api/usage`

### 1. Track Image Usage

**POST** `/api/usage/images`

Tracks image generation usage.

**Headers:**

- `x-sps-key: your-api-key`

### 2. Track Content Usage

**POST** `/api/usage/content`

Tracks content generation usage.

**Headers:**

- `x-sps-key: your-api-key`

### 3. Track Title Usage

**POST** `/api/usage/title`

Tracks title generation usage.

**Headers:**

- `x-sps-key: your-api-key`

### 4. Get Usage Statistics

**GET** `/api/usage/stats`

Gets usage statistics.

**Headers:**

- `x-sps-key: your-api-key`

### 5. Get Usage History

**GET** `/api/usage/history`

Gets detailed usage history.

**Headers:**

- `x-sps-key: your-api-key`

### 6. Get Daily Usage

**GET** `/api/usage/daily`

Gets daily usage breakdown.

**Headers:**

- `x-sps-key: your-api-key`

### 7. Get Email Usage History

**POST** `/api/usage/email`

Gets usage history by email.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

---

## 🔔 Webhooks

**Base Path:** `/api/webhooks`

### 1. PayPal Webhook Handler

**POST** `/api/webhooks/paypal`

Handles PayPal subscription webhooks.

### 2. Xendit Webhook Handler

**POST** `/api/webhooks/xendit`

Handles Xendit payment webhooks.

**Headers:**

- `x-callback-token`: Xendit webhook token

### 3. Webhook Status

**GET** `/api/webhooks/status`

Gets webhook processing status.

### 4. Recent Events

**GET** `/api/webhooks/events`

Gets recent webhook events (development only).

---

## 📈 Dashboard & Analytics

**Base Path:** `/api/dashboard` & `/api/activity`

### 1. Get Dashboard Data

**POST** `/api/dashboard`

Retrieves dashboard data for user.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

### 2. Get Activity Data

**POST** `/api/activity`

Retrieves user activity data.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

---

## 🛠️ Debug & Testing

**Base Path:** `/api/debug` & `/api/test`

### 1. List All KV Data

**GET** `/api/users/debug/kv`

Lists all data in KV store (debug only).

### 2. Delete KV Data

**POST** `/api/users/debug/kv/delete`

Deletes specific KV data (debug only).

### 3. Track API Usage

**POST** `/api/users/usage/track`

Tracks API usage for analytics.

### 4. Get User Usage

**GET** `/api/users/usage`

Gets user's API usage statistics.

### 5. Simulate PayPal Webhook

**POST** `/api/test/webhooks/simulate/paypal`

Simulates PayPal webhook for testing.

### 6. Simulate PayPal Activation

**POST** `/api/test/webhooks/simulate/paypal/activate`

Simulates PayPal subscription activation.

**Request Body:**

```json
{
  "subscriptionId": "sub-123",
  "userId": "<EMAIL>",
  "tier": "pro"
}
```

---

## 📚 Documentation

### 1. Swagger API Documentation

**GET** `/api/docs`

Displays interactive Swagger API documentation.

---

## Key Features

- **🌐 CORS Enabled**: All endpoints support Cross-Origin Resource Sharing
- **🔐 Multiple Authentication**: API key (`x-sps-key`) and JWT token (`Authorization: Bearer`) support
- **💳 Multi-Payment Gateway**: Supports both PayPal and Xendit payment processors
- **🏆 Tier-based Subscriptions**: Multiple subscription tiers with addon support
- **📊 Usage Tracking**: Comprehensive API usage monitoring and analytics
- **🔔 Real-time Webhooks**: Instant payment and subscription status updates
- **💾 KV Storage**: Uses Cloudflare KV for persistent data storage
- **🚀 Cloudflare Workers**: Serverless architecture for global performance

## Response Format

All endpoints follow a consistent response format:

**Success Response:**

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    /* response data */
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response:**

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Authentication

### API Key Authentication

Include in headers:

```
x-sps-key: your-api-key-here
```

### JWT Token Authentication

Include in headers:

```
Authorization: Bearer your-jwt-token-here
```

### Webhook Authentication

Xendit webhooks require:

```
x-callback-token: your-webhook-token
```
